﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Red20.Model.Data.Order;
using Red20.Model.Data.Stock;
using Red20.Service.Data.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Red20.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class OrderStockAllocationController : ControllerBase
    {

        private IOrderStockAllocationService service;
        private IOrderItemService orderItemService;
        private IStockService stockService;
        private IOrderService orderService;
        private IOrderItemStockItemService orderItemStockItemService;
        private ILogger<AuthController> logger;
        private IUnitOfWork unitOfWork;

        public OrderStockAllocationController(
            IOrderStockAllocationService service,
            IOrderItemService orderItemService,
            IStockService stockService,
            IOrderItemStockItemService orderItemStockItemService,
            IOrderService orderService,
            ILogger<AuthController> logger,
            IUnitOfWork unitOfWork)
        {

            this.service = service;
            this.orderItemService = orderItemService;
            this.stockService = stockService;
            this.orderItemStockItemService = orderItemStockItemService;
            this.orderService = orderService;
            this.logger = logger;
            this.unitOfWork = unitOfWork;
        }

        [HttpGet]
        [ProducesResponseType(200, Type = typeof(IList<OrderStockAllocationModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get()
        {
            var allocations = service.GetAll();
            var allocationsToRemove = new List<Guid>();

            if (allocations != null && allocations.Any())
            {
                foreach (var allocation in allocations)
                {
                    var order = allocation.Order;
                    var orderItemIds = allocation.Order.OrderItems.Select(s => s.OrderItemId).ToArray();
                    var orderItemStockItemAllocationModels = new List<OrderItemStockItemAllocationModel>();
                    var stock = allocation.Stock;

                    var freeStock = stock.FreeStock;

                    var orderStockItems = allocation.Stock.OrderItemStockItems;
                    //allocation.Stock.OrderItemStockItems = orderStockItems.ToList();

                    var orderItemStockItems = orderStockItems.Where(w => orderItemIds.Contains(w.OrderItemId) && w.StockId == allocation.StockId);

                    if (orderItemStockItems != null && orderItemStockItems.Any())
                    {
                        foreach (var orderItemStockItem in orderItemStockItems)
                        {
                            var orderItem = order.OrderItems.FirstOrDefault(f => f.OrderItemId == orderItemStockItem.OrderItemId);

                            if (orderItemStockItem != null && (orderItemStockItem.Allocated > 0 || orderItemStockItem.Unallocated > 0))
                            {
                                var quantityToAllocate = allocation.Stock.FreeStock == 0 ? 0 :
                                                         allocation.Stock.FreeStock >= orderItemStockItem.Unallocated ? orderItemStockItem.Unallocated :
                                                         allocation.Stock.FreeStock;

                                orderItemStockItemAllocationModels
                                .Add(new OrderItemStockItemAllocationModel
                                {
                                    OrderItemStockItemId = orderItemStockItem.OrderItemStockItemId,
                                    OrderItemId = orderItemStockItem.OrderItemId,
                                    StockId = orderItemStockItem.StockId,
                                    Allocated = orderItemStockItem.Allocated,
                                    Quantity = orderItemStockItem.Quantity,
                                    Unallocated = orderItemStockItem.Unallocated,
                                    Invoiced = orderItemStockItem.Invoiced,
                                    OrderItemDescription = orderItem.OrderItemFirstTwoLinesDescription,
                                    QuantityToAllocate = freeStock == 0 || freeStock < 0 ? 0 : freeStock >= orderItemStockItem.Unallocated ? orderItemStockItem.Unallocated : freeStock,
                                    QuantityToUnallocate = 0,
                                    StockCode = orderItemStockItem.StockCode,
                                    FreeStock = freeStock
                                });
                            }
                        }
                    }

                    if (orderItemStockItemAllocationModels != null && orderItemStockItemAllocationModels.Any())
                    {
                        allocation.OrderItemStockItemAllocationModels = orderItemStockItemAllocationModels;
                    } else
                    {
                        allocationsToRemove.Add(allocation.OrderStockAllocationId);
                    }
                }

                allocations = allocations.AsParallel().WithDegreeOfParallelism(Environment.ProcessorCount).Where(w => !allocationsToRemove.Contains(w.OrderStockAllocationId)).OrderByDescending(o => o.Modified).ToList();
            }
            return Ok(allocations);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(OrderStockAllocationModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id)
        {
            var orderStockAllocation = await service.GetAsync(id);

            if (orderStockAllocation != null)
            {
                var order = await orderService.GetAsync(orderStockAllocation.OrderId);
                var orderItems = await orderItemService.GetByOrderAsync(order.OrderId);
                var orderItemStockItemAllocationModels = new List<OrderItemStockItemAllocationModel>();
                var stock = await stockService.GetAsync(orderStockAllocation.StockId);

                var freeStock = stock.FreeStock;

                if (orderItems != null && orderItems.Any())
                {
                    foreach (var orderItem in orderItems)
                    {
                        if (orderItem.OrderItemStockItems != null && orderItem.OrderItemStockItems.Any())
                        {
                            var orderItemStockItems = orderItem.OrderItemStockItems.Where(w => w.StockId == orderStockAllocation.StockId).ToList();

                            if (orderItemStockItems.Any())
                            {

                                foreach (var orderItemStockItem in orderItemStockItems)
                                {
                                    var orderItemStockAllocationModel = new OrderItemStockItemAllocationModel
                                    {
                                        OrderItemStockItemId = orderItemStockItem.OrderItemStockItemId,
                                        OrderItemId = orderItemStockItem.OrderItemId,
                                        StockId = orderItemStockItem.StockId,
                                        Allocated = orderItemStockItem.Allocated,
                                        Quantity = orderItemStockItem.Quantity,
                                        Unallocated = orderItemStockItem.Unallocated,
                                        Invoiced = orderItemStockItem.Invoiced,
                                        OrderItemDescription = orderItem.OrderItemFirstTwoLinesDescription,
                                        QuantityToAllocate = freeStock == 0 || freeStock < 0 ? 0 : freeStock >= orderItemStockItem.Unallocated ? orderItemStockItem.Unallocated : freeStock,
                                        QuantityToUnallocate = 0,
                                        StockCode = orderItemStockItem.StockCode,
                                        FreeStock = freeStock
                                    };

                                    orderItemStockItemAllocationModels.Add(orderItemStockAllocationModel);
                                    freeStock = freeStock - orderItemStockAllocationModel.QuantityToAllocate;
                                }
                            }
                        }
                    }
                    orderStockAllocation.OrderItemStockItemAllocationModels = orderItemStockItemAllocationModels;
                }
            }

            return Ok(orderStockAllocation);
        }

        [HttpPost("allocateOrUnallocate")]
        [ProducesResponseType(200, Type = typeof(OrderStockAllocationModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> AllocateOrUnallocate([FromBody] OrderItemStockItemAllocationModel model)
        {
            var orderItemStockItem = await orderItemStockItemService.GetByOrderItemAndStockAsync(model.OrderItemId, model.StockId, null);
            if (orderItemStockItem is null)
            {
                return BadRequest();
            }

            var stock = await unitOfWork.Stock.GetAsync(model.StockId);
            if (stock is null)
            {
                return BadRequest();
            }

            if (stock != null)
            {
                if (model.QuantityToAllocate > 0)
                {
                    stock.FreeStock = Math.Max(0, stock.FreeStock - model.QuantityToAllocate);
                } else if (model.QuantityToUnallocate > 0)
                {
                    stock.FreeStock = stock.FreeStock + model.QuantityToUnallocate;
                }

                unitOfWork.Stock.Update(stock);
            }

            var orderItemStockItemUpdateModel = new OrderItemStockItemUpdateModel();

            if (model.OperationType == "allocate")
            {
                orderItemStockItem.Unallocated = orderItemStockItem.Unallocated - model.QuantityToAllocate >= 0 ? orderItemStockItem.Unallocated - model.QuantityToAllocate : 0;
                orderItemStockItem.Allocated = orderItemStockItem.Allocated + model.QuantityToAllocate <= orderItemStockItem.Quantity ? orderItemStockItem.Allocated + model.QuantityToAllocate : orderItemStockItem.Quantity;
            } else if (model.OperationType == "unallocate")
            {
                orderItemStockItem.Allocated = model.QuantityToUnallocate <= orderItemStockItem.Allocated ?
                                                    orderItemStockItem.Allocated - model.QuantityToUnallocate :
                                                    0;
                orderItemStockItem.Unallocated = orderItemStockItem.Unallocated + model.QuantityToUnallocate;
            }

            try
            {
                orderItemStockItemUpdateModel.Allocated = orderItemStockItem.Allocated;
                orderItemStockItemUpdateModel.Unallocated = orderItemStockItem.Unallocated;
                orderItemStockItemUpdateModel.StockId = stock.StockId;
                orderItemStockItemUpdateModel.OrderItemId = orderItemStockItem.OrderItemId;
                orderItemStockItemUpdateModel.StockCode = stock.Code;
                orderItemStockItemUpdateModel.Description = stock.Description;
                orderItemStockItemUpdateModel.Quantity = orderItemStockItem.Quantity;
                orderItemStockItemUpdateModel.Type = orderItemStockItem.Type;
                orderItemStockItemUpdateModel.SheetType = orderItemStockItem.SheetType;

                orderItemStockItem = await orderItemStockItemService.PutAsync(orderItemStockItem.OrderItemStockItemId, orderItemStockItemUpdateModel);
                model.QuantityToAllocate = 0;
                model.QuantityToUnallocate = 0;

                await unitOfWork.SaveChangesAsync();

                return Ok(model);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot update order item stock allocations - {ex}");
                return BadRequest();
            }
        }

        [HttpPost("allocateOrUnallocateFromStockAllocationScreen")]
        [ProducesResponseType(200, Type = typeof(OrderStockAllocationModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> AllocateOrUnallocateFromStockAllocationScreen([FromBody] OrderStockAllocationModel model)
        {
            var orderItemIds = model.Order.OrderItems.Select(s => s.OrderItemId).ToArray();
            var stock = await unitOfWork.Stock.GetAsync(model.StockId);
            var orderStockItems = model.Stock.OrderItemStockItems;
            var orderItemStockItems = orderStockItems.Where(w => orderItemIds.Contains(w.OrderItemId) && w.StockId == model.StockId);

            if (model.QuantityToAllocate.HasValue)
            {
                stock.FreeStock = Math.Max(0, stock.FreeStock - model.QuantityToAllocate.Value);

                unitOfWork.Stock.Update(stock);

            } else if (model.QuantityToUnallocate.HasValue)
            {
                stock.FreeStock = stock.FreeStock + model.QuantityToUnallocate.Value;

                unitOfWork.Stock.Update(stock);
            }

            if (orderItemStockItems != null && orderItemStockItems.Any())
            {
                foreach (var orderItemStockItem in orderItemStockItems)
                {
                    var orderItemStockItemUpdateModel = new OrderItemStockItemUpdateModel();

                    if (model.OperationType == "allocate" && model.QuantityToAllocate.HasValue)
                    {
                        orderItemStockItem.Unallocated = orderItemStockItem.Unallocated - model.QuantityToAllocate.Value >= 0 ? orderItemStockItem.Unallocated - model.QuantityToAllocate.Value : 0;
                        orderItemStockItem.Allocated = orderItemStockItem.Allocated + model.QuantityToAllocate.Value <= orderItemStockItem.Quantity ? orderItemStockItem.Allocated + model.QuantityToAllocate.Value : orderItemStockItem.Quantity;
                    } else if (model.OperationType == "unallocate" && model.QuantityToUnallocate.HasValue)
                    {
                        orderItemStockItem.Allocated = model.QuantityToUnallocate.Value <= orderItemStockItem.Allocated ?
                                                            orderItemStockItem.Allocated - model.QuantityToUnallocate.Value :
                                                            0;
                        orderItemStockItem.Unallocated = orderItemStockItem.Unallocated + model.QuantityToUnallocate.Value;
                    }

                    try
                    {
                        orderItemStockItemUpdateModel.Allocated = orderItemStockItem.Allocated;
                        orderItemStockItemUpdateModel.Unallocated = orderItemStockItem.Unallocated;
                        orderItemStockItemUpdateModel.StockId = stock.StockId;
                        orderItemStockItemUpdateModel.OrderItemId = orderItemStockItem.OrderItemId;
                        orderItemStockItemUpdateModel.StockCode = stock.Code;
                        orderItemStockItemUpdateModel.Description = stock.Description;
                        orderItemStockItemUpdateModel.Quantity = orderItemStockItem.Quantity;
                        orderItemStockItemUpdateModel.Type = orderItemStockItem.Type;
                        orderItemStockItemUpdateModel.SheetType = orderItemStockItem.SheetType;

                        var updatedOrderItemStockItem = await orderItemStockItemService.PutAsync(orderItemStockItem.OrderItemStockItemId, orderItemStockItemUpdateModel);
                    } catch (Exception ex)
                    {
                        logger.LogError($"Cannot update order item stock allocations - {ex.Message}");
                        return BadRequest($"{ex.Message}");
                    }
                }
            }

            model.QuantityToAllocate = 0;
            model.QuantityToUnallocate = 0;

            await unitOfWork.SaveChangesAsync();

            return Ok(model);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(OrderStockAllocationModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody] OrderStockAllocationUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }

            try
            {
                var stockAllocation = await service.PostAsync(model);
                return Ok(stockAllocation);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot create Order Stock Allocation - {ex}");
                return BadRequest();
            }
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(OrderStockAllocationModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Put(Guid id, [FromBody] OrderStockAllocationUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }

            var stockAllocation = await service.GetAsync(id);

            if (stockAllocation is null)
            {
                return BadRequest();
            }

            try
            {
                var stock = await stockService.GetAsync(stockAllocation.StockId);
                var orderItems = await orderItemService.GetByOrderIdAsync(stockAllocation.Order.OrderId);

                var quantityToAllocate = model.Allocate;

                if (orderItems != null && orderItems.Any())
                {
                    foreach (var item in orderItems)
                    {
                        var orderItemStockItems = await orderItemStockItemService.GetUnallocatedByOrderItemAsync(item.OrderItemId, stock.StockId);

                        if (orderItemStockItems != null && orderItemStockItems.Any())
                        {

                            foreach (var orderItemStockItem in orderItemStockItems)
                            {
                                if (quantityToAllocate > 0)
                                {
                                    if (quantityToAllocate >= orderItemStockItem.Unallocated)
                                    {
                                        orderItemStockItem.Allocated = orderItemStockItem.Allocated + orderItemStockItem.Unallocated;
                                        quantityToAllocate = quantityToAllocate - orderItemStockItem.Unallocated;
                                        orderItemStockItem.Unallocated = 0;
                                    } else
                                    {
                                        orderItemStockItem.Allocated = orderItemStockItem.Allocated + quantityToAllocate;
                                        orderItemStockItem.Unallocated = orderItemStockItem.Unallocated - quantityToAllocate;
                                        quantityToAllocate = 0;
                                    }
                                }

                                var orderItemStockItemUpdateModel = new OrderItemStockItemUpdateModel();
                                orderItemStockItemUpdateModel.Allocated = orderItemStockItem.Allocated;
                                orderItemStockItemUpdateModel.Unallocated = orderItemStockItem.Unallocated;
                                orderItemStockItemUpdateModel.StockId = stock.StockId;
                                orderItemStockItemUpdateModel.OrderItemId = orderItemStockItem.OrderItemId;
                                orderItemStockItemUpdateModel.StockCode = stock.Code;
                                orderItemStockItemUpdateModel.Description = stock.Description;
                                orderItemStockItemUpdateModel.Quantity = orderItemStockItem.Quantity;
                                orderItemStockItemUpdateModel.Type = orderItemStockItem.Type;
                                orderItemStockItemUpdateModel.SheetType = orderItemStockItem.SheetType;

                                await orderItemStockItemService.PutAsync(orderItemStockItem.OrderItemStockItemId, orderItemStockItemUpdateModel);
                            }
                        }
                    }
                }

                //orderItems = await orderItemService.GetByOrderAsync(stockAllocation.Order.OrderId);
                //var existingStockAllocation = 0.0;
                //var existingUnallocated = 0.0;

                //if (orderItems != null && orderItems.Any()) {
                //    foreach (var item in orderItems) {
                //        if (item.OrderItemStockItems != null && item.OrderItemStockItems.Any()) {
                //            if (item.OrderItemStockItems.Any(a => a.StockId == stock.StockId && a.Type == "Stock")) {
                //                existingStockAllocation = existingStockAllocation + item.OrderItemStockItems.Where(w => w.StockId == stock.StockId && w.Type == "Stock").Sum(s => s.Allocated);
                //                existingUnallocated = existingUnallocated + item.OrderItemStockItems.Where(w => w.StockId == stock.StockId && w.Type == "Stock").Sum(s => s.Unallocated);
                //            }
                //        }
                //    }
                //}

                model.Allocate = 0;
                //model.ExistingAllocation = existingStockAllocation;
                //model.Unallocated = existingUnallocated;
                stockAllocation = await service.PutAsync(id, model);
                return Ok(stockAllocation);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot update Order Stock Allocation - {ex}");
                return BadRequest();
            }
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Delete(Guid id)
        {
            if (!await service.OrderStockAllocationIdExistsAsync(id))
            {
                return BadRequest();
            }

            await service.DeleteAsync(id);
            return Ok();
        }
    }
}