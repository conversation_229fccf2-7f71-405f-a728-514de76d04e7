﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Red20.Data.Migrations
{
    public partial class UpdateJobInvoiceTables : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "PurchaseOrderItemInvoices");

            migrationBuilder.AddColumn<Guid>(
                name: "XeroSupplierId",
                table: "Suppliers",
                nullable: true);

            migrationBuilder.AlterColumn<double>(
                name: "TotalInvoiced",
                table: "PurchaseOrderItemLogs",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AddColumn<DateTime>(
                name: "InvoiceDate",
                table: "JobInvoices",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "AccountCode",
                table: "JobInvoiceItem",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Currency",
                table: "JobInvoiceItem",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Description",
                table: "JobInvoiceItem",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "OrderItemId",
                table: "JobInvoiceItem",
                nullable: false,
                defaultValue: new Guid("********-0000-0000-0000-************"));

            migrationBuilder.AddColumn<double>(
                name: "TotalGross",
                table: "JobInvoiceItem",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "TotalNet",
                table: "JobInvoiceItem",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "TotalVat",
                table: "JobInvoiceItem",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Vat",
                table: "JobInvoiceItem",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "XeroSupplierId",
                table: "Suppliers");

            migrationBuilder.DropColumn(
                name: "InvoiceDate",
                table: "JobInvoices");

            migrationBuilder.DropColumn(
                name: "AccountCode",
                table: "JobInvoiceItem");

            migrationBuilder.DropColumn(
                name: "Currency",
                table: "JobInvoiceItem");

            migrationBuilder.DropColumn(
                name: "Description",
                table: "JobInvoiceItem");

            migrationBuilder.DropColumn(
                name: "OrderItemId",
                table: "JobInvoiceItem");

            migrationBuilder.DropColumn(
                name: "TotalGross",
                table: "JobInvoiceItem");

            migrationBuilder.DropColumn(
                name: "TotalNet",
                table: "JobInvoiceItem");

            migrationBuilder.DropColumn(
                name: "TotalVat",
                table: "JobInvoiceItem");

            migrationBuilder.DropColumn(
                name: "Vat",
                table: "JobInvoiceItem");

            migrationBuilder.AlterColumn<int>(
                name: "TotalInvoiced",
                table: "PurchaseOrderItemLogs",
                type: "int",
                nullable: false,
                oldClrType: typeof(double));

            migrationBuilder.CreateTable(
                name: "PurchaseOrderItemInvoices",
                columns: table => new
                {
                    PurchaseOrderItemInvoiceId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    InvoiceDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    InvoiceNumber = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    PurchaseOrderId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    PurchaseOrderItemId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    TotalGross = table.Column<double>(type: "float", nullable: false),
                    TotalInvoiced = table.Column<int>(type: "int", nullable: false),
                    TotalNet = table.Column<double>(type: "float", nullable: false),
                    TotalVat = table.Column<double>(type: "float", nullable: false),
                    Vat = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PurchaseOrderItemInvoices", x => x.PurchaseOrderItemInvoiceId);
                    table.ForeignKey(
                        name: "FK_PurchaseOrderItemInvoices_PurchaseOrderItems_PurchaseOrderItemId",
                        column: x => x.PurchaseOrderItemId,
                        principalTable: "PurchaseOrderItems",
                        principalColumn: "PurchaseOrderItemId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_PurchaseOrderItemInvoices_PurchaseOrderItemId",
                table: "PurchaseOrderItemInvoices",
                column: "PurchaseOrderItemId");
        }
    }
}
