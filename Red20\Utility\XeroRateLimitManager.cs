﻿using System.Threading;
using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace Red20.Utility
{
    public class XeroRateLimitManager
    {
        private static readonly SemaphoreSlim _semaphore = new SemaphoreSlim(1, 1);
        private static DateTime _lastRequestTime = DateTime.MinValue;
        private static int _requestCount = 0;
        private static readonly object _lockObject = new object();

        // Xero API limits: 60 requests per minute, 5000 per day
        private const int MaxRequestsPerMinute = 50; // Leave buffer
        private const int DelayBetweenRequests = 1200; // 1.2 seconds
        private const int BackoffDelay = 70000; // 70 seconds when hitting limits

        public static async Task WaitForRateLimit(ILogger logger)
        {
            await _semaphore.WaitAsync();
            try
            {
                lock (_lockObject)
                {
                    var now = DateTime.UtcNow;
                    var timeSinceLastRequest = (now - _lastRequestTime).TotalMilliseconds;

                    // Reset counter every minute
                    if ((now - _lastRequestTime).TotalMinutes >= 1)
                    {
                        _requestCount = 0;
                    }

                    _requestCount++;

                    // If we're approaching the limit, implement progressive delays
                    if (_requestCount >= MaxRequestsPerMinute)
                    {
                        logger.LogWarning($"Approaching Xero API rate limit. Request #{_requestCount}. Implementing long delay.");
                        _lastRequestTime = now;
                        _requestCount = 0; // Reset counter
                    } else if (_requestCount > 40) // Start slowing down at 40 requests
                    {
                        var progressiveDelay = DelayBetweenRequests * 2; // Double delay
                        if (timeSinceLastRequest < progressiveDelay)
                        {
                            var waitTime = progressiveDelay - (int)timeSinceLastRequest;
                            Thread.Sleep(Math.Max(waitTime, 0));
                        }
                    } else
                    {
                        // Normal rate limiting
                        if (timeSinceLastRequest < DelayBetweenRequests)
                        {
                            var waitTime = DelayBetweenRequests - (int)timeSinceLastRequest;
                            Thread.Sleep(Math.Max(waitTime, 0));
                        }
                    }

                    _lastRequestTime = DateTime.UtcNow;
                }
            } finally
            {
                _semaphore.Release();
            }
        }

        public static async Task HandleRateLimitException(ILogger logger, int attempt = 1)
        {
            var baseDelay = BackoffDelay;
            var exponentialDelay = baseDelay * Math.Pow(2, attempt - 1); // Exponential backoff
            var maxDelay = 300000; // 5 minutes max

            var finalDelay = Math.Min(exponentialDelay, maxDelay);

            logger.LogWarning($"Xero API rate limit hit. Attempt #{attempt}. Waiting {finalDelay / 1000} seconds before retry.");

            await Task.Delay((int)finalDelay);

            // Reset our internal counter
            lock (_lockObject)
            {
                _requestCount = 0;
                _lastRequestTime = DateTime.UtcNow;
            }
        }
    }
}
