﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Red20.Excel.Export;
using Red20.Model.Data;
using Red20.Model.Data.Document;
using Red20.Model.Data.HireEquipment;
using Red20.Model.Entity;
using Red20.Service.Data.Interface;
using Red20.Service.Storage.Interface;
using Red20.Utility;

namespace Red20.Controllers {
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class HireEquipmentJobCardController : ControllerBase {
        private IHireEquipmentJobCardService service;
        private IUserService userService;
        private ILogger<AuthController> logger;
        private IUnitOfWork unitOfWork;

        public HireEquipmentJobCardController(
            IUnitOfWork unitOfWork,
            IHireEquipmentJobCardService service,
            IUserService userService,
            ILogger<AuthController> logger) {


            this.service = service;
            this.userService = userService;
            this.logger = logger;
            this.unitOfWork = unitOfWork;
        }

        [HttpGet("byHireEquipment/{id}")]
        [ProducesResponseType(200, Type = typeof(IList<HireEquipmentJobCardModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetByHireEquipment(Guid id) {

            var jobCards = await service.GetByHireEquipmentAsync(id);

            return Ok(jobCards);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(HireEquipmentJobCardModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody] HireEquipmentJobCardUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }

            try {
                var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
                if (emailClaim == null)
                {
                    return Unauthorized();
                }

                var user = await userService.GetUserByEmailAsync(emailClaim.Value);
                var number = await NumberSequenceUtility.GetNextPurchaseOrderNumber(unitOfWork);
                var raisedBy = user.Name;

                model.CreatedBy = raisedBy;

                var equipment = await service.PostAsync(model);

                return Ok(equipment);
            } catch (Exception ex) {
                logger.LogError($"Cannot create Hire Equipment Job Card - {ex}");
                return BadRequest();
            }
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(HireEquipmentModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Put(Guid id, [FromBody] HireEquipmentJobCardUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }

            var equipment = await service.GetAsync(id);

            if (equipment is null) {
                return BadRequest();
            }

            try {
                equipment = await service.PutAsync(id, model);
                return Ok(equipment);
            } catch (Exception ex) {
                logger.LogError($"Cannot update Hire Equipment - {ex}");
                return BadRequest();
            }
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Delete(Guid id) {

            await service.DeleteAsync(id);
            return Ok();
        }
    }
}

