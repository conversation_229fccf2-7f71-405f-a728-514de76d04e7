﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Red20.Data.Migrations
{
    public partial class PurchaseorderEmailDate : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "PoCcEmailAddress",
                table: "PurchaseOrders",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PoEmailAddress",
                table: "PurchaseOrders",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "PoEmailDate",
                table: "PurchaseOrders",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "PoCcEmailAddress",
                table: "PurchaseOrders");

            migrationBuilder.DropColumn(
                name: "PoEmailAddress",
                table: "PurchaseOrders");

            migrationBuilder.DropColumn(
                name: "PoEmailDate",
                table: "PurchaseOrders");
        }
    }
}
