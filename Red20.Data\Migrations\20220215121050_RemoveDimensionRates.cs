﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Red20.Data.Migrations
{
    public partial class RemoveDimensionRates : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Basic",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "OffshoreRateWeekday",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "OffshoreRateWeekend",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "Overtime1",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "Overtime2",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "StandardHours",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "StandardWeeklyHours",
                table: "Users");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<double>(
                name: "Basic",
                table: "Users",
                type: "float",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "OffshoreRateWeekday",
                table: "Users",
                type: "float",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "OffshoreRateWeekend",
                table: "Users",
                type: "float",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "Overtime1",
                table: "Users",
                type: "float",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "Overtime2",
                table: "Users",
                type: "float",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "StandardHours",
                table: "Users",
                type: "float",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "StandardWeeklyHours",
                table: "Users",
                type: "float",
                nullable: true);
        }
    }
}
