﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Red20.Model.Data.Email
{
    public class InvoiceProcessingResult
    {
        public string OrderNumber { get; set; }
        public string CustomerName { get; set; }
        public bool Success { get; set; }
        public string ErrorMessage { get; set; }
        public decimal? InvoiceAmount { get; set; }
        public string InvoiceNumber { get; set; }
        public DateTime ProcessedAt { get; set; }
        public string Stage { get; set; } // e.g., "JobCreation", "XeroInvoice", "DeliveryCharge"
    }
}
