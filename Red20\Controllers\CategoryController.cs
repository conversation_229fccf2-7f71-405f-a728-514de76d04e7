﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Red20.Excel.Export;
using Red20.Model.Data;
using Red20.Model.Data.Document;
using Red20.Model.Entity;
using Red20.Service.Data.Interface;
using Red20.Service.Storage.Interface;

namespace Red20.Controllers {
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class CategoryController : ControllerBase {

        private ICategoryService categoryService;
        private ILogger<AuthController> logger;
        private IDocumentService documentService;
        private IStorageService blobStorage;
        private IUserService userService;
        private IUnitOfWork unitOfWork;

        public CategoryController(
            ICategoryService categoryService,
            IUnitOfWork unitOfWork,
            IDocumentService documentService,
            IStorageService blobStorage,
            IUserService userService,
            ILogger<AuthController> logger) {

            this.categoryService = categoryService;
            this.logger = logger;
            this.documentService = documentService;
            this.blobStorage = blobStorage;
            this.userService = userService;
            this.unitOfWork = unitOfWork;

        }

        [HttpGet]
        [ProducesResponseType(200, Type = typeof(IList<CategoryModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get() {
            var categories = categoryService.GetAllCategories();
            return Ok(categories);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(CategoryModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id) {

            var category = await categoryService.GetAsync(id);

            return Ok(category);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(CategoryModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody]CategoryUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }

            var category = await categoryService.PostAsync(model);

            return Ok(category);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(CategoryModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Put(Guid id, [FromBody]CategoryUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }

            var category = await categoryService.GetAsync(id);

            if (category is null) {
                return BadRequest();
            }

            category = await categoryService.PutAsync(id, model);

            return Ok(category);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Delete(Guid id) {

            await categoryService.DeleteAsync(id);
            return Ok();
        }

        [HttpPost("export")]
        [ProducesResponseType(200)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> GenerateReport(List<CategoryModel> models)
        {
            try
            {
                var document = new DocumentModel();
                var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
                if (emailClaim == null)
                {
                    return Unauthorized();
                }

                var user = await userService.GetUserByEmailAsync(emailClaim.Value);
                var currentUser = $"{user.Firstname} {user.Lastname}";
                var itemData = ExportUtility.ExportStockCategories(models, currentUser);
                document = await documentService.PostAsync("Stock_Categories.xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"{user.Firstname} {user.Lastname}", "Categories", null, null, null, false, false, false);
                var file = File(itemData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                await blobStorage.UploadAsync(document.DocumentId, itemData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                return Ok();
            }
            catch (Exception ex)
            {
                return NotFound();
            }
        }
        [HttpGet("downloadReport")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> DownloadReport()
        {
            try
            {
                var lastDocument = await unitOfWork.Document.Query().Where(t => t.Type == "Categories").OrderByDescending(c => c.Created).FirstOrDefaultAsync();
                var download = await blobStorage.DownloadAsync(lastDocument.DocumentId);
                var file = File(download.Item1, download.Item2);
                return file;
            }
            catch (Exception ex)
            {
                return NotFound();
            }
        }
    }
}