﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Red20.Model.Data;
using Red20.Model.Data.Job;
using Red20.Model.Data.Order;
using Red20.Model.Data.Stock;
using Red20.Model.Data.StockTransaction;
using Red20.Model.Entity;
using Red20.Service.Data.Interface;
using Red20.Service.Xero.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Red20.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class OrderItemController : ControllerBase
    {

        private IOrderItemService service;
        private ILogger<AuthController> logger;
        private IOrderItemStockItemService itemStockItemService;
        private IJobService jobService;
        private IJobInvoiceService jobInvoiceService;
        private IJobInvoiceItemService jobInvoiceItemService;
        private IUserService userService;
        private IStockService stockService;
        private IStockTransactionService stockTransactionService;
        private IOrderStockAllocationService orderStockAllocationService;
        private IOrderService orderService;
        private IOrderAssemblyService assemblyService;
        private IPurchaseOrderService purchaseOrderService;
        private IStockFifoService fifoService;
        private IStockTakeService stockTakeService;
        private IStockSerialTrackingService serialTrackingService;
        private IStockSerialTrackingLogService serialTrackingLogService;
        private IUnitOfWork unitOfWork;
        private IXeroService xeroService;
        private IMapper mapper;
        private readonly ISaleOrderItemStockService saleOrderItemStockService;

        public OrderItemController(
            IOrderItemService service,
            ILogger<AuthController> logger,
            IOrderItemStockItemService itemStockItemService,
            IJobService jobService,
            IJobInvoiceService jobInvoiceService,
            IUserService userService,
            IOrderService orderService,
            IJobInvoiceItemService jobInvoiceItemService,
            IStockService stockService,
            IStockTransactionService stockTransactionService,
            IOrderStockAllocationService orderStockAllocationService,
            IOrderAssemblyService assemblyService,
            IPurchaseOrderService purchaseOrderService,
            IStockFifoService fifoService,
            ICurrencyRateService currencyRateService,
            IStockTakeService stockTakeService,
            IStockSerialTrackingService serialTrackingService,
            IUnitOfWork unitOfWork,
            IXeroService xeroService,
            IMapper mapper,
            IStockSerialTrackingLogService serialTrackingLogService,
            ISaleOrderItemStockService saleOrderItemStockService)
        {

            this.service = service;
            this.logger = logger;
            this.itemStockItemService = itemStockItemService;
            this.jobService = jobService;
            this.jobInvoiceService = jobInvoiceService;
            this.userService = userService;
            this.orderService = orderService;
            this.jobInvoiceItemService = jobInvoiceItemService;
            this.stockService = stockService;
            this.stockTransactionService = stockTransactionService;
            this.orderStockAllocationService = orderStockAllocationService;
            this.assemblyService = assemblyService;
            this.purchaseOrderService = purchaseOrderService;
            this.fifoService = fifoService;
            this.stockTakeService = stockTakeService;
            this.serialTrackingService = serialTrackingService;
            this.serialTrackingLogService = serialTrackingLogService;
            this.unitOfWork = unitOfWork;
            this.mapper = mapper;
            this.xeroService = xeroService;
            this.saleOrderItemStockService = saleOrderItemStockService;
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(OrderItemModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id)
        {
            var orderItem = await service.GetAsync(id);
            return Ok(orderItem);
        }

        [HttpGet("byOrder/{id}")]
        [ProducesResponseType(200, Type = typeof(OrderItemModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetByOrder(Guid id)
        {
            var orderItems = await service.GetByOrderAsNoTrackingAsync(id);
            var result = orderItems.ToList();
            if (result != null && result.Any())
            {
                foreach (var orderItem in result)
                {
                    var invoiceItem = await jobInvoiceItemService.GetLatestInvoiceItemByOrderItemIdAsync(orderItem.OrderItemId);
                    if (invoiceItem != null)
                    {
                        orderItem.LastInvoiceDate = invoiceItem.InvoiceDate.ToString("dd/MM/yyyy");
                        orderItem.LastInvoiceNumber = invoiceItem.InvoiceNumber;
                    }
                }
            }
            return Ok(result);
        }

        [HttpGet("allOrderItems/{id}")]
        [ProducesResponseType(200, Type = typeof(OrderItemModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAllOrderItems(Guid id)
        {
            var orderItems = await service.GetByOrderWithDeliveryChargeAsync(id);
            return Ok(orderItems);
        }

        [HttpGet("byOrderForJobSheets/{id}")]
        [ProducesResponseType(200, Type = typeof(OrderItemModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetByOrderForJobSheets(Guid id)
        {
            var orderItems = await service.GetByOrderAsync(id);
            orderItems = orderItems.Where(w => !w.IsDeliveryChargeItem).ToList();
            return Ok(orderItems);
        }

        [HttpGet("byOrderForInvoice/{id}")]
        [ProducesResponseType(200, Type = typeof(OrderItemInvoiceModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetByOrderForInvoice(Guid id)
        {
            var invoiceItems = await service.GetByOrderForInvoiceAsync(id);
            List<Guid> itemsToRemove = new List<Guid>();

            if (invoiceItems != null && invoiceItems.Any())
            {
                foreach (var invoiceItem in invoiceItems)
                {

                    if (!invoiceItem.IsOrderItemCopy && invoiceItem.TotalInvoiced > 0 && invoiceItem.TotalInvoiced == invoiceItem.Quantity)
                    {
                        itemsToRemove.Add(invoiceItem.OrderItemId);
                    } else
                    {
                        bool isFullyInvoiced = invoiceItem.TotalInvoiced == invoiceItem.Quantity;

                        if (invoiceItem.OrderItemStockItems != null && invoiceItem.OrderItemStockItems.Any())
                        {
                            foreach (var stockItem in invoiceItem.OrderItemStockItems)
                            {
                                if (stockItem.Invoiced > 0 && stockItem.Invoiced == stockItem.Quantity)
                                {
                                    if (isFullyInvoiced)
                                    {
                                        itemsToRemove.Add(stockItem.OrderItemId);
                                    }
                                }
                            }
                        } else
                        {
                            if (isFullyInvoiced)
                            {
                                itemsToRemove.Add(invoiceItem.OrderItemId);
                            }
                        }
                    }
                }
            }

            if (itemsToRemove != null && itemsToRemove.Any())
            {
                invoiceItems = invoiceItems.Where(w => !itemsToRemove.Contains(w.OrderItemId)).ToList();
            }

            if (invoiceItems != null && invoiceItems.Any())
            {
                invoiceItems = invoiceItems.OrderByDescending(o => !o.IsDeliveryChargeItem).ToList();
            }

            return Ok(invoiceItems);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(OrderItemModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody] OrderItemUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }
            if (model.IsDeliveryChargeItem)
            {
                model.Quantity = 1;
            }

            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);
            var currentUser = $"{user.Firstname} {user.Lastname}";
            model.CreatedBy = currentUser;
            await service.PostAsync(model);

            return Ok();
        }

        [HttpPost("cloneOrderItem")]
        [ProducesResponseType(200, Type = typeof(OrderItemModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> CloneOrderItem(List<OrderItemStockItemModel> orderItemStockItems)
        {
            var orderItemId = orderItemStockItems != null && orderItemStockItems.Any() ? orderItemStockItems.First().OrderItemId : Guid.Empty;
            var newlyCreatedOrderItemStockItemModels = new List<OrderItemStockItemModel>();
            var newOrderItem = new OrderItem();

            try
            {
                if (orderItemId != null && orderItemId != Guid.Empty)
                {
                    var orderItemEntity = await service.GetForCloneAsync(orderItemId);

                    if (orderItemEntity != null)
                    {
                        orderItemEntity.HasCopies = true;
                        orderItemEntity.IsCopy = false;
                        orderItemEntity.NumberOfCopies++;

                        orderItemEntity = await service.PutNumberOfCopiesAsync(orderItemEntity.OrderItemId, orderItemEntity.NumberOfCopies, orderItemEntity.HasCopies, false);

                        string newOrderItemDescription = string.Empty;

                        if (!string.IsNullOrWhiteSpace(orderItemEntity.Description))
                        {
                            if (orderItemEntity.Description.Contains("</p>"))
                            {
                                if (orderItemEntity.NumberOfCopies > 0)
                                {
                                    newOrderItemDescription = ReplaceFirst(orderItemEntity.Description, "</p>", $" (copy {orderItemEntity.NumberOfCopies})</p>");
                                } else
                                {
                                    newOrderItemDescription = ReplaceFirst(orderItemEntity.Description, "</p>", $" (copy {orderItemEntity.NumberOfCopies + 1})</p>");
                                }
                            } else
                            {
                                newOrderItemDescription = orderItemEntity.Description + Environment.NewLine + $"<p>(copy {(orderItemEntity.NumberOfCopies > 0 ? orderItemEntity.NumberOfCopies : orderItemEntity.NumberOfCopies + 1)})</p>";
                            }
                        }

                        //create New Order Item
                        newOrderItem = new OrderItem
                        {
                            Created = DateTime.UtcNow,
                            OrderItemId = Guid.NewGuid(),
                            OrderId = orderItemEntity.OrderId,
                            UnitPrice = orderItemEntity.UnitPrice,
                            Quantity = orderItemEntity.TotalInvoiced > 0 ?
                                                (orderItemEntity.Quantity - orderItemEntity.TotalInvoiced) > 0 ?
                                                        (int)(orderItemEntity.Quantity - orderItemEntity.TotalInvoiced) :
                                                0 :
                                            0,
                            Discount = orderItemEntity.Discount,
                            TotalInvoiced = 0,
                            TotalDelivered = 0,
                            AccountCode = orderItemEntity.AccountCode,
                            IsDeliveryChargeItem = false,
                            IsInitialDeliveryCharge = false,
                            HasCreditNote = orderItemEntity.HasCreditNote,
                            PoundValue = orderItemEntity.PoundValue,
                            CurrencyRate = orderItemEntity.CurrencyRate,
                            PartCredit = orderItemEntity.PartCredit,
                            TotalCredited = 0,
                            FullCredit = orderItemEntity.FullCredit,
                            Description = newOrderItemDescription,
                            IsCopy = true
                        };

                        await unitOfWork.OrderItem.CreateAsync(newOrderItem);
                        await unitOfWork.SaveChangesAsync();

                        if (orderItemStockItems != null && orderItemStockItems.Any())
                        {
                            foreach (var item in orderItemStockItems)
                            {
                                if (orderItemEntity.OrderItemStockItems != null && orderItemEntity.OrderItemStockItems.Any())
                                {
                                    var existingOrderItem = orderItemEntity.OrderItemStockItems.Where(w => w.StockCode == item.StockCode).FirstOrDefault();
                                    if (existingOrderItem != null)
                                    {
                                        if (existingOrderItem.Unallocated > 0)
                                        {
                                            existingOrderItem.Unallocated = existingOrderItem.Unallocated - item.Quantity;
                                            existingOrderItem.Quantity = existingOrderItem.Quantity - item.Quantity > 0 ? existingOrderItem.Quantity - item.Quantity : 0;
                                            await itemStockItemService.UpdateUnallocatedNumberAsync(existingOrderItem.OrderItemStockItemId, existingOrderItem.Unallocated, existingOrderItem.Quantity);
                                        }
                                    }
                                }

                                var stock = await stockService.GetAsync(item.StockId);

                                if (stock != null)
                                {
                                    var modelAllocated = item.Quantity;

                                    var newOrderItemStockItem = new OrderItemStockItem
                                    {
                                        OrderItemStockItemId = Guid.NewGuid(),
                                        OrderItemId = newOrderItem.OrderItemId,
                                        Quantity = item.Quantity,
                                        StockId = stock.StockId,
                                        Allocated = modelAllocated,
                                        Unallocated = 0.0,
                                        SheetType = "jobRecordSheet",
                                        Created = DateTime.UtcNow,
                                        Type = "Stock",
                                        StockCode = stock.Code,
                                        Description = stock.CategoryName
                                    };

                                    await unitOfWork.OrderItemStockItem.CreateAsync(newOrderItemStockItem);
                                }
                            }

                            await unitOfWork.SaveChangesAsync();
                        }
                    }
                }
            } catch (Exception ex)
            {
                return BadRequest($"{ex}");
            }

            return Ok();
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(OrderItemModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Put(Guid id, [FromBody] OrderItemUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }
            if (model.IsDeliveryChargeItem)
            {
                model.Quantity = 1;
            }

            var orderItem = await service.GetAsync(id);
            if (orderItem is null)
            {
                return BadRequest();
            }

            try
            {
                orderItem = await service.PutAsync(id, model);

                return Ok(orderItem);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot update Order Item - {ex}");
                return BadRequest();
            }
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Delete(Guid id)
        {
            var orderItemStockItems = await itemStockItemService.GetByOrderItemAsync(id);
            if (orderItemStockItems.Any())
            {
                var stockItemsValue = orderItemStockItems.Sum(s => s.Quantity);

                var orderItem = await service.GetAsync(orderItemStockItems.Select(s => s.OrderItemId).First());
                var order = await orderService.GetAsync(orderItem.OrderId);

                foreach (var itemStockItem in orderItemStockItems)
                {
                    if (await itemStockItemService.OrderItemStockItemIdExistsAsync(itemStockItem.OrderItemStockItemId))
                    {
                        await itemStockItemService.DeleteAsync(itemStockItem.OrderItemStockItemId);
                    }

                    var orderStockAllocation = await orderStockAllocationService.GetByOrderIdAndStockIdAsync(order.OrderId, itemStockItem.StockId);
                    if (orderStockAllocation != null)
                    {
                        await orderStockAllocationService.DeleteAsync(orderStockAllocation.OrderStockAllocationId);
                    }
                }
            }

            await service.DeleteAsync(id);
            return Ok();
        }

        protected async Task<Dictionary<double?, double?>> GetAverageStockPrice(OrderItemStockItemInvoiceModel stockItem, double? costPrice)
        {
            var result = new Dictionary<double?, double?>();

            //get quantity invoiced so far from order stock allocations table by stockItem.StockId
            var stockInvoicedSoFar = 0.0;
            var stockTransactions = await stockTransactionService.GetByStockOutIdAsync(stockItem.StockId);
            if (stockTransactions != null && stockTransactions.Any())
            {
                stockInvoicedSoFar += stockTransactions.Sum(c => c.Quantity);
            }

            //get all FIFO records by stock ID ordered by Created date ascending
            var fifoRecords = await fifoService.GetByStockIdAsync(stockItem.StockId);
            var fifoRecord = fifoRecords.Where(w => stockInvoicedSoFar + 1 >= w.From && stockInvoicedSoFar + 1 <= w.To).FirstOrDefault();
            var quantityList = new List<double>();
            var priceList = new List<double?>();

            if (fifoRecord != null)
            {
                //we now need to know how many of the items to be invoiced can be invoiced at this fifo record's cost price
                double canInvoiceQuantity = fifoRecord.To - stockInvoicedSoFar;

                if (canInvoiceQuantity >= stockItem.NumberToInvoice)
                { // means that we can invoice the required quantity in full using a single fifo record
                    quantityList.Add(stockItem.NumberToInvoice);
                    priceList.Add(fifoRecord.CostPrice);
                } else
                {
                    // we can only invoice part of the required quantity using the first fifo record
                    quantityList.Add(canInvoiceQuantity);
                    priceList.Add(fifoRecord.CostPrice);

                    stockInvoicedSoFar = stockInvoicedSoFar + canInvoiceQuantity;
                    var remainingToInvoice = stockItem.NumberToInvoice - canInvoiceQuantity;

                    while (remainingToInvoice > 0)
                    {
                        //we have remaining items to invoice, so we now get the next in line FIFO record
                        fifoRecord = fifoRecords.Where(w => stockInvoicedSoFar + 1 >= w.From && stockInvoicedSoFar + 1 <= w.To).FirstOrDefault();

                        if (fifoRecord == null)
                        {
                            break;
                        }

                        canInvoiceQuantity = fifoRecord.To - stockInvoicedSoFar; //check how many we can invoice from the newly retrieved fifo record

                        if (canInvoiceQuantity >= remainingToInvoice)
                        {// means that we can invoice the required quantity in full using the newly retrieved FIFO record
                            quantityList.Add(remainingToInvoice);
                            priceList.Add(fifoRecord.CostPrice);

                            remainingToInvoice = 0;
                        } else
                        {
                            //update remaining to invoice and re-start while loop until we have invoiced the full required quantity
                            quantityList.Add(canInvoiceQuantity);
                            priceList.Add(fifoRecord.CostPrice);

                            stockInvoicedSoFar = stockInvoicedSoFar + canInvoiceQuantity;
                            remainingToInvoice = remainingToInvoice - canInvoiceQuantity;
                        }
                    }
                }
            }

            var finalStockPrice = costPrice; //initially set it to the stock cost price
            if (quantityList != null && quantityList.Any() && priceList != null && priceList.Any() && quantityList.Count == priceList.Count)
            {
                double? sum = 0.0;

                for (var x = 0; x < quantityList.Count; x++)
                {
                    sum += quantityList[x] * priceList[x];
                }

                finalStockPrice = sum / quantityList.Sum(); // get the average stock cost price
            }

            result.Add(finalStockPrice, fifoRecord != null ? fifoRecord.CostPrice : costPrice); //return the average price AND the last used FIFO record price (to update the stock cost price and stock FIFO value)

            return result;
        }

        [HttpPost("invoicePO")]
        [ProducesResponseType(200, Type = typeof(OrderItemModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> InvoicePurchaseOrderItems(List<JobInvoiceItemModel> lineItems)
        {
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);

            var orderLineItems = lineItems != null && lineItems.Any(a => !string.IsNullOrWhiteSpace(a.SaleOrderNumber)) ?
                                    lineItems.Where(w => !string.IsNullOrWhiteSpace(w.SaleOrderNumber)).ToList() : null;

            var nonOrderLineItems = lineItems != null && lineItems.Any(a => string.IsNullOrWhiteSpace(a.SaleOrderNumber)) ?
                                    lineItems.Where(w => string.IsNullOrWhiteSpace(w.SaleOrderNumber)).ToList() : null;

            var purchaseOrder = await unitOfWork.PurchaseOrder.Query(q => q.PurchaseOrderId == lineItems[0].PurchaseOrderId).Include(i => i.Supplier).Include(c => c.PurchaseOrderItems).FirstOrDefaultAsync();

            string xeroInvoiceNumber = lineItems[0].InvoiceNumber;
            DateTime invoiceDate = lineItems[0].InvoiceDate;

            try
            {
                //create invoice and invoice items in Xero
                var po = await jobInvoiceItemService.PostPurchaseOrderItemsAsync(lineItems, xeroInvoiceNumber, invoiceDate);

                // Update Job / Job Invoice / Purchase Order Item Log / Purchase order status

                #region Update Job / Job Invoice / Purchase Order Item Log / Purchase order status

                if (po)
                {

                    if ((orderLineItems != null && orderLineItems.Any()))
                    {

                        var groupedItems = orderLineItems.GroupBy(g => g.SaleOrderNumber);

                        foreach (var group in groupedItems)
                        {
                            var job = new JobModel();
                            var jobInvoice = new JobInvoiceModel();
                            var order = await orderService.GetByNumber(group.Key);
                            job = await jobService.GetByOrderIdAsync(order.OrderId);

                            if (job == null)
                            {
                                job = await jobService.PostAsync(new JobModel
                                {
                                    CreatedBy = user != null ? $"{user.Firstname} {user.Lastname}" : null,
                                    OrderId = order.OrderId,
                                    OrderType = order.Type,
                                    OrderNumber = order.Number,
                                    OrderCustomerName = order.Customer != null ? order.Customer.Name : string.Empty,
                                    Description = order.FirstItemDescription,
                                    DateOrderRaised = order.Created,
                                    Currency = order.Currency
                                });
                            }

                            var jobInvoices = await jobInvoiceService.GetPOInvoicesByJobIdAsync(job.JobId);

                            int number = jobInvoices.Any() ? jobInvoices.Count + 1 : 1;

                            jobInvoice.JobId = job.JobId;
                            jobInvoice.CreatedBy = $"{user.Firstname} {user.Lastname}";
                            jobInvoice.JobType = "PO";
                            jobInvoice.InvoiceNumber = $"{order.Number}/PO/{number}";
                            jobInvoice.InvoiceDate = invoiceDate;
                            jobInvoice.PurchaseOrderId = purchaseOrder != null ? purchaseOrder.PurchaseOrderId : (Guid?)null;

                            if (!await jobInvoiceService.JobInvoiceNumberExistsAsync(jobInvoice.InvoiceNumber))
                            {
                                jobInvoice = await jobInvoiceService.PostAsync(jobInvoice);

                                var jobInvoiceModels = new List<JobInvoiceItemModel>();

                                foreach (var lineItem in group)
                                {
                                    jobInvoiceModels.Add(lineItem);
                                }

                                foreach (var invoiceItem in jobInvoiceModels)
                                {
                                    var jobInvoiceItem = new JobInvoiceItem();
                                    var currencyRate = new CurrencyRate();
                                    double vat = 0.0;
                                    var discountRate = invoiceItem.DiscountRate;
                                    var purchaseOrderItem = await unitOfWork.PurchaseOrderItem.Query(c => c.PurchaseOrderItemId == invoiceItem.OrderItemId).Include(x => x.PurchaseOrder).Include(x => x.PurchaseOrderItemLogs).FirstOrDefaultAsync();
                                    if (purchaseOrderItem.PurchaseOrder.Currency == "Euro" || purchaseOrderItem.PurchaseOrder.Currency == "US Dollar")
                                    {
                                        currencyRate = unitOfWork.CurrencyRate.Query().Where(c => c.Name == purchaseOrderItem.PurchaseOrder.Currency && c.ExpiryDate.Month == invoiceDate.Month && c.ExpiryDate.Year == invoiceDate.Year).FirstOrDefault();
                                        if (currencyRate == null)
                                        {
                                            currencyRate = unitOfWork.CurrencyRate.Query().Where(c => c.Name == purchaseOrderItem.PurchaseOrder.Currency && c.ExpiryDate.Year == invoiceDate.Year).OrderByDescending(c => c.ExpiryDate).FirstOrDefault();
                                        }
                                        var unitPrice = purchaseOrderItem.Price.HasValue && currencyRate != null ? purchaseOrderItem.Price.Value / currencyRate.Rate : 0.0;
                                        jobInvoiceItem.UnitPrice = unitPrice;
                                        jobInvoiceItem.Currency = "Pounds Sterling";
                                        jobInvoiceItem.Quantity = invoiceItem.Quantity;

                                        var discValue = discountRate.HasValue ? invoiceItem.Quantity * unitPrice * (discountRate.Value / 100) : 0;
                                        var net = (invoiceItem.Quantity * unitPrice) - discValue;

                                        if (purchaseOrderItem.PurchaseOrder.VatCode.Contains("20%"))
                                        {
                                            vat = net * 0.2;
                                        } else if (purchaseOrderItem.PurchaseOrder.VatCode.Contains("5%"))
                                        {
                                            vat = net * 0.05;
                                        } else if (purchaseOrderItem.PurchaseOrder.VatCode.Contains("10%"))
                                        {
                                            vat = net * 0.10;
                                        }

                                        jobInvoiceItem.TotalVat = vat;
                                        jobInvoiceItem.TotalNet = net;
                                        jobInvoiceItem.TotalGross = net + vat;
                                        jobInvoiceItem.Value = net + vat;

                                    } else
                                    {
                                        jobInvoiceItem.UnitPrice = purchaseOrderItem.Price.HasValue ? purchaseOrderItem.Price.Value : 0.0;
                                        jobInvoiceItem.Quantity = invoiceItem.Quantity;
                                        jobInvoiceItem.Currency = purchaseOrderItem.PurchaseOrder.Currency;

                                        var discount = discountRate.HasValue ? invoiceItem.Quantity * invoiceItem.UnitPrice * (discountRate.Value / 100) : 0;
                                        var totalNet = (invoiceItem.Quantity * invoiceItem.UnitPrice) - discount;

                                        if (purchaseOrderItem.PurchaseOrder.VatCode.Contains("20%"))
                                        {
                                            vat = totalNet * 0.2;
                                        } else if (purchaseOrderItem.PurchaseOrder.VatCode.Contains("5%"))
                                        {
                                            vat = totalNet * 0.05;
                                        } else if (purchaseOrderItem.PurchaseOrder.VatCode.Contains("10%"))
                                        {
                                            vat = totalNet * 0.10;
                                        }

                                        jobInvoiceItem.TotalVat = vat;
                                        jobInvoiceItem.TotalNet = totalNet;
                                        jobInvoiceItem.TotalGross = totalNet + vat;
                                        jobInvoiceItem.Value = totalNet + vat;
                                    }

                                    jobInvoiceItem.PurchaseOrderSupplierName = purchaseOrder.Supplier != null ? purchaseOrder.Supplier.Name : null;
                                    jobInvoiceItem.Description = purchaseOrderItem.Description;
                                    jobInvoiceItem.AccountCode = purchaseOrderItem.AccountCode;
                                    jobInvoiceItem.JobInvoiceId = jobInvoice.JobInvoiceId;
                                    jobInvoiceItem.JobId = job.JobId;
                                    jobInvoiceItem.Vat = purchaseOrderItem.PurchaseOrder.VatCode;
                                    jobInvoiceItem.AccountCode = purchaseOrderItem.PurchaseOrder.AccountCode;
                                    jobInvoiceItem.InvoiceType = "PO";
                                    jobInvoiceItem.Created = DateTime.UtcNow;
                                    jobInvoiceItem.InvoiceDate = invoiceDate;
                                    jobInvoiceItem.InvoiceNumber = purchaseOrder.Number;
                                    jobInvoiceItem.Description = invoiceItem.Description;
                                    jobInvoiceItem.OrderItemId = invoiceItem.OrderItemId;
                                    jobInvoiceItem.DiscountRate = invoiceItem.DiscountRate;

                                    jobInvoiceItem.JobInvoiceItemId = Guid.NewGuid();

                                    await unitOfWork.JobInvoiceItem.CreateAsync(jobInvoiceItem);

                                    purchaseOrderItem.TotalInvoiced = purchaseOrderItem.TotalInvoiced.HasValue ?
                                            purchaseOrderItem.TotalInvoiced.Value + Double.Parse(invoiceItem.Quantity.ToString()) :
                                            Double.Parse(invoiceItem.Quantity.ToString());

                                    var newLog = new PurchaseOrderItemLog
                                    {
                                        Created = DateTime.Now,
                                        Username = user.Name,
                                        PurchaseOrderItemId = invoiceItem.OrderItemId,
                                        PurchaseOrderId = purchaseOrderItem.PurchaseOrder.PurchaseOrderId,
                                        InvoiceDate = invoiceDate,
                                        InvoiceNumber = invoiceItem.InvoiceNumber,
                                        TotalInvoiced = invoiceItem.Quantity,
                                        ReceivedDate = purchaseOrderItem.DateReceived.Value.ToString("dd/MM/yyyy"),
                                        TotalReceived = purchaseOrderItem.TotalReceived.ToString()
                                    };

                                    await unitOfWork.PurchaseOrderItemLog.CreateAsync(newLog);

                                    await unitOfWork.SaveChangesAsync();
                                }

                            } else
                            {
                                return BadRequest($"Invoice Number already exists in the system");
                            }
                        }
                    }

                    if (nonOrderLineItems != null && nonOrderLineItems.Any())
                    {
                        foreach (var item in nonOrderLineItems)
                        {
                            var purchaseOrderItem = await unitOfWork.PurchaseOrderItem.Query(c => c.PurchaseOrderItemId == item.OrderItemId).Include(x => x.PurchaseOrder).Include(x => x.PurchaseOrderItemLogs).FirstOrDefaultAsync();
                            purchaseOrderItem.TotalInvoiced = purchaseOrderItem.TotalInvoiced.HasValue ?
                                                 purchaseOrderItem.TotalInvoiced.Value + Int32.Parse(item.Quantity.ToString()) :
                                                 Int32.Parse(item.Quantity.ToString());

                            var newLog = new PurchaseOrderItemLog
                            {
                                Created = DateTime.Now,
                                Username = user.Name,
                                PurchaseOrderItemId = item.OrderItemId,
                                PurchaseOrderId = purchaseOrderItem.PurchaseOrder.PurchaseOrderId,
                                InvoiceDate = invoiceDate,
                                InvoiceNumber = item.InvoiceNumber,
                                TotalInvoiced = item.Quantity,
                                ReceivedDate = purchaseOrderItem.DateReceived.Value.ToString("dd/MM/yyyy"),
                                TotalReceived = purchaseOrderItem.TotalReceived.ToString()
                            };

                            await unitOfWork.PurchaseOrderItemLog.CreateAsync(newLog);
                            await unitOfWork.SaveChangesAsync();
                        }
                    }

                    var purchaseOrderItems = purchaseOrder.PurchaseOrderItems.ToList();

                    if (purchaseOrderItems.Any(c => c.TotalReceived == null || (c.TotalReceived.HasValue && c.Quantity != c.TotalReceived.Value)))
                    {
                        purchaseOrder.Status = "Part-Received";
                        purchaseOrder.DateReceived = DateTime.UtcNow;
                        unitOfWork.PurchaseOrder.Update(purchaseOrder);
                    } else if (purchaseOrderItems.All(c => c.TotalInvoiced != null && c.TotalReceived != null && c.TotalInvoiced.HasValue && c.TotalReceived.HasValue && c.TotalInvoiced.Value == c.TotalReceived.Value))
                    {
                        purchaseOrder.Status = "Completed";
                        purchaseOrder.DateCompleted = DateTime.UtcNow;
                        unitOfWork.PurchaseOrder.Update(purchaseOrder);
                    } else if (purchaseOrderItems.Any(c => (!c.TotalInvoiced.HasValue && c.TotalReceived.HasValue) || (c.TotalInvoiced.HasValue && c.TotalReceived.HasValue && c.TotalInvoiced.Value != c.TotalReceived.Value)))
                    {
                        purchaseOrder.Status = "Part-Invoiced";
                        unitOfWork.PurchaseOrder.Update(purchaseOrder);
                    }
                    await unitOfWork.SaveChangesAsync();
                }

                #endregion

            } catch (Exception ex)
            {
                logger.LogError($"Cannot Invoice Purchase Order Line Items which have an order number - {ex}");
                return BadRequest();
                throw;
            }

            return Ok();
        }

        public static string ReplaceFirst(string text, string search, string replace)
        {
            int pos = text.IndexOf(search);
            if (pos < 0)
            {
                return text;
            }
            return text.Substring(0, pos) + replace + text.Substring(pos + search.Length);
        }

        [HttpPost("updateItems")]
        [ProducesResponseType(200, Type = typeof(OrderItemModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> UpdateOrderItems()
        {
            var orderList = unitOfWork.Order.Query().Include(c => c.OrderItems).Include(c => c.OrderAssemblies).ToList();
            foreach (var order in orderList)
            {
                if (order.OrderItems != null)
                {
                    var orderItems = unitOfWork.OrderItem.Query(w => w.OrderId == order.OrderId && !w.IsInitialDeliveryCharge).Include(w => w.Order).Include(w => w.OrderItemStockItems).ToList();
                    foreach (var item in orderItems)
                    {
                        item.Description = item.Description.Replace("<p></p>", "");
                        unitOfWork.OrderItem.Update(item);
                        await unitOfWork.SaveChangesAsync();
                    }
                }
            }
            return Ok();
        }


        [HttpPut("updateInvoicedDate/{id}/{invoiceDate}")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> UpdateInvoicedDate(Guid id, DateTime invoiceDate)
        {

            try
            {
                var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
                if (emailClaim == null)
                {
                    return Unauthorized();
                }

                var user = await userService.GetUserByEmailAsync(emailClaim.Value);

                var orderItem = unitOfWork.OrderItem.Query().Include(x => x.Order).Where(c => c.OrderItemId == id).FirstOrDefault();

                orderItem.InvoiceDate = invoiceDate;

                unitOfWork.OrderItem.Update(orderItem);

                var jobInvoice = unitOfWork.JobInvoice.Query().Where(c => c.InvoiceNumber.Contains(orderItem.Order.Number) && c.JobType == "Sale").FirstOrDefault();

                if (jobInvoice != null)
                {

                    var jobInvoiceItems = unitOfWork.JobInvoiceItem.Query().Include(x => x.JobInvoice).Where(c => c.JobInvoiceId == jobInvoice.JobInvoiceId).ToList();

                    jobInvoice.InvoiceDate = invoiceDate;

                    unitOfWork.JobInvoice.Update(jobInvoice);

                    if (jobInvoiceItems.Any())
                    {
                        foreach (var jobInvoiceItem in jobInvoiceItems)
                        {
                            jobInvoiceItem.InvoiceDate = invoiceDate;

                            unitOfWork.JobInvoiceItem.Update(jobInvoiceItem);
                        }
                    }
                }

                await unitOfWork.SaveChangesAsync();

            } catch (Exception ex)
            {
                logger.LogError($"Cannot update Order Item - {ex}");
                return BadRequest();
            }
            return Ok();
        }


        #region New Implementation

        [HttpPost("invoice")]
        [ProducesResponseType(200, Type = typeof(OrderItemModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Invoice_NEW(List<OrderItemInvoiceModel> lineItems)
        {
            try
            {
                var isDaylightSaving = TimeZoneInfo.Local.IsDaylightSavingTime(DateTime.UtcNow);
                var time = isDaylightSaving ? DateTime.UtcNow.AddHours(1).TimeOfDay : DateTime.UtcNow.TimeOfDay;
                var date = isDaylightSaving ? DateTime.UtcNow.AddHours(1) : DateTime.UtcNow;

                var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
                if (emailClaim == null)
                {
                    return Unauthorized();
                }

                var user = await userService.GetUserByEmailAsync(emailClaim.Value);
                var order = await GetOrderAsync(lineItems);
                var job = await saleOrderItemStockService.GetOrCreateJobAsync(order, user);
                var jobInvoice = await saleOrderItemStockService.CreateOrUpdateJobInvoiceAsync(lineItems, order, job, user);

                await saleOrderItemStockService.ProcessLineItemsAsync(lineItems, order, job, jobInvoice, isDaylightSaving, user);

                return Ok();
            } catch (Exception ex)
            {
                return BadRequest($"{ex}");
            }
        }

        private async Task<UserModel> GetCurrentUserAsync()
        {
            var nameClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);
            return await userService.GetAsync(Guid.Parse(nameClaim.Value));
        }

        private async Task<OrderModel> GetOrderAsync(List<OrderItemInvoiceModel> lineItems)
        {
            if (lineItems == null || !lineItems.Any())
            {
                throw new ArgumentException("Line items cannot be null or empty");
            }

            return await orderService.GetAsync(lineItems[0].OrderId);
        }

        #endregion
    }
}