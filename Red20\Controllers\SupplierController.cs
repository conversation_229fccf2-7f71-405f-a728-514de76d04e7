﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Red20.Excel.Export;
using Red20.Model.Data;
using Red20.Model.Data.Customer;
using Red20.Model.Data.Document;
using Red20.Model.Data.Supplier;
using Red20.Service.Data.Interface;
using Red20.Service.Storage.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Red20.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class SupplierController : ControllerBase
    {
        private ISupplierService service;
        private ISupplierAddressService addressService;
        private ISupplierContactService contactService;
        private IDocumentService documentService;
        private IStorageService blobStorage;
        private IUserService userService;
        private ILogger<AuthController> logger;
        IUnitOfWork unitOfWork;

        public SupplierController(
            ISupplierService service,
            ISupplierAddressService addressService,
            ISupplierContactService contactService,
            IDocumentService documentService,
            IStorageService blobStorage,
            IUserService userService,
            IUnitOfWork unitOfWork,
            ILogger<AuthController> logger)
        {

            this.service = service;
            this.addressService = addressService;
            this.contactService = contactService;
            this.documentService = documentService;
            this.blobStorage = blobStorage;
            this.userService = userService;
            this.unitOfWork = unitOfWork;
            this.logger = logger;
        }

        [HttpGet]
        [ProducesResponseType(200, Type = typeof(IList<SupplierModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get()
        {
            var suppliers = service.GetSuppliers();
            return Ok(suppliers);
        }

        [HttpGet("getAllSuppliers")]
        [ProducesResponseType(200, Type = typeof(IList<AllSupplierModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAllSuppliers()
        {
            var customers = await service.GetAllSuppliers();
            return Ok(customers);
        }

        [HttpGet("getAllArchivedSuppliers")]
        [ProducesResponseType(200, Type = typeof(IList<SupplierModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetArchivedSuppliers()
        {
            var suppliers = service.GetArchivedSuppliers();
            return Ok(suppliers);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(SupplierModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id)
        {
            if (!await service.SupplierIdExistsAsync(id))
            {
                return NotFound();
            }

            var supplier = await service.GetAsync(id);

            return Ok(supplier);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(SupplierUpdateModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody] SupplierUpdateModel model)
        {
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);
            if (model is null)
            {
                return BadRequest();
            }
            model.QAStatusModifiedBy = $"{user.Firstname} {user.Lastname}";

            var supplierExists = await service.SupplierNameExistsAsync(model.Name);

            if (supplierExists)
            {
                return BadRequest();
            }

            var supplier = await service.PostAsync(model);
            return Ok(supplier);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(SupplierUpdateModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Put(Guid id, [FromBody] SupplierUpdateModel model)
        {
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);

            if (model is null)
            {
                return BadRequest();
            }

            if (!await service.SupplierIdExistsAsync(id))
            {
                return BadRequest();
            }

            var supplier = await service.PutAsync(id, model, user);

            return Ok(supplier);
        }

        [HttpPut("archive/{id}")]
        [ProducesResponseType(200, Type = typeof(SupplierUpdateModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> ArchiveSupplier(Guid id, [FromBody] SupplierUpdateModel model)
        {
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);

            if (model is null)
            {
                return BadRequest();
            }

            if (!await service.SupplierIdExistsAsync(id))
            {
                return BadRequest();
            }
            model.ArchivedDate = DateTime.UtcNow;
            var supplier = await service.PutAsync(id, model, user);

            return Ok(supplier);
        }

        [HttpPut("unArchive/{id}")]
        [ProducesResponseType(200, Type = typeof(SupplierUpdateModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> UnArchiveSupplier(Guid id, [FromBody] SupplierUpdateModel model)
        {
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);

            if (model is null)
            {
                return BadRequest();
            }

            if (!await service.SupplierIdExistsAsync(id))
            {
                return BadRequest();
            }
            model.ArchivedDate = null;
            var supplier = await service.PutAsync(id, model, user);

            return Ok(supplier);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Delete(Guid id)
        {
            if (!await service.SupplierIdExistsAsync(id))
            {
                return NotFound();
            }

            await service.DeleteAsync(id);
            return Ok();
        }
        [HttpPost("export")]
        [ProducesResponseType(200)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> GenerateReport(List<SupplierModel> models)
        {
            try
            {
                var document = new DocumentModel();
                var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
                if (emailClaim == null)
                {
                    return Unauthorized();
                }

                var user = await userService.GetUserByEmailAsync(emailClaim.Value);
                var currentUser = $"{user.Firstname} {user.Lastname}";
                var itemData = ExportUtility.ExportSuppliers(models, currentUser);
                document = await documentService.PostAsync("Supplier_Report.xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"{user.Firstname} {user.Lastname}", "Suppliers", null, null, null, false, false, false);
                var file = File(itemData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                await blobStorage.UploadAsync(document.DocumentId, itemData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                return Ok();
            } catch (Exception ex)
            {
                return NotFound();
            }
        }
        [HttpGet("downloadReport")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> DownloadReport()
        {
            try
            {
                var lastDocument = await unitOfWork.Document.Query().Where(t => t.Type == "Suppliers").OrderByDescending(c => c.Created).FirstOrDefaultAsync();
                var download = await blobStorage.DownloadAsync(lastDocument.DocumentId);
                var file = File(download.Item1, download.Item2);
                return file;
            } catch (Exception ex)
            {
                return NotFound();
            }
        }
    }
}