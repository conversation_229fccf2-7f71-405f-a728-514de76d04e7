﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Red20.Data.Migrations
{
    public partial class AddXeroApiExceptionTableAndAddMissingIndexesToDbSchema : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "XeroApiExceptions",
                columns: table => new
                {
                    Id = table.Column<Guid>(nullable: false),
                    Created = table.Column<DateTime>(nullable: false),
                    Type = table.Column<string>(nullable: true),
                    Message = table.Column<string>(nullable: true),
                    OrderId = table.Column<Guid>(nullable: false),
                    OrderNumber = table.Column<string>(nullable: true),
                    OrderAssemblyId = table.Column<Guid>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_XeroApiExceptions", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_UserTokens_Expires",
                table: "UserTokens",
                column: "Expires");

            migrationBuilder.CreateIndex(
                name: "IX_JobInvoiceItem_OrderItemId",
                table: "JobInvoiceItem",
                column: "OrderItemId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "XeroApiExceptions");

            migrationBuilder.DropIndex(
                name: "IX_UserTokens_Expires",
                table: "UserTokens");

            migrationBuilder.DropIndex(
                name: "IX_JobInvoiceItem_OrderItemId",
                table: "JobInvoiceItem");
        }
    }
}
