﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Red20.Model.Data;
using Red20.Model.Entity;
using Red20.Service.Data.Interface;

namespace Red20.Controllers {
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class CustomerAddressController : ControllerBase {

        private ICustomerAddressService service;
        private ICustomerContactService contactService;
        private ICustomerService customerService;
        private ILogger<AuthController> logger;
        IUnitOfWork unitOfWork;

        public CustomerAddressController(
            ICustomerAddressService service,
            ICustomerService customerService,
            ICustomerContactService contactService,
            IUnitOfWork unitOfWork,
            ILogger<AuthController> logger) {

            this.service = service;
            this.contactService = contactService;
            this.customerService = customerService;
            this.logger = logger;
            this.unitOfWork = unitOfWork;
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(CustomerAddressModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id) {

            var customerAddress = await service.GetAsync(id);

            return Ok(customerAddress);
        }

        [HttpGet]
        [ProducesResponseType(200, Type = typeof(IList<CustomerAddressModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get() {
            var customerAddress = await service.GetAllCustomerAddresses();
            return Ok(customerAddress);
        }

        [HttpGet("byCustomer/{id}")]
        [ProducesResponseType(200, Type = typeof(IList<CustomerAddressModel>))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetByCustomer(Guid id) {

            var customerAddress = service.GetByCustomerAsync(id);

            return Ok(customerAddress);
        }

        [HttpGet("byCustomerInvoiceAddress/{id}")]
        [ProducesResponseType(200, Type = typeof(IList<CustomerAddressModel>))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetCustomerInvoiceAddress(Guid id) {

            var customerAddress = service.GetCustomerInvoiceAddressAsync(id);

            return Ok(customerAddress);
        }

        [HttpGet("byCustomerShippingAddress/{id}")]
        [ProducesResponseType(200, Type = typeof(IList<CustomerAddressModel>))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetCustomerShippingAddress(Guid id) {

            var customerAddress = service.GetByCustomerAsync(id);

            return Ok(customerAddress);
        }


        [HttpGet("getInternalShippingAddresses")]
        [ProducesResponseType(200, Type = typeof(IList<CustomerAddressModel>))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetInternalShippingAddresses() {
            var customerAddresses = await service.GetInternalAddresses();
            return Ok(customerAddresses);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(CustomerAddressModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody]CustomerAddressUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }

            var customerAddress = await service.PostAsync(model);

            return Ok(customerAddress);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(CustomerAddressModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Put(Guid id, [FromBody]CustomerAddressUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }

            var customerAddress = await service.GetAsync(id);

            if(model.IsShipping && model.IsInvoiceDefault) {
                model.IsShipping = false;
            }

            if (!model.EditAddress) {
                if(!model.IsInvoiceDefault && customerAddress.IsInvoiceDefault) {
                    var customerAddresses = unitOfWork.CustomerAddress.Query().Where(c => c.CustomerId == model.CustomerId && c.IsInvoiceDefault).ToList();

                    if (customerAddresses.Count > 1 && customerAddresses.Any(c => c.CustomerAddressId != customerAddress.CustomerAddressId && !model.IsInvoiceDefault)) {
                        return BadRequest();
                    } else {
                        return BadRequest();
                    }
                }
            }

            if (customerAddress is null) {
                return BadRequest();
            }

            customerAddress = await service.PutAsync(id, model);

            return Ok(customerAddress);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Delete(Guid id) {

            var customerAddress = await service.GetAsync(id);
            if(customerAddress.IsInvoiceDefault) {
                return BadRequest();
            }
            await service.DeleteAsync(id);
            return Ok();
        }
    }
}