﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Red20.Data.Migrations
{
    public partial class UserCheckInCheckOut : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CheckIn",
                table: "UserCheckIns");

            migrationBuilder.DropColumn(
                name: "CheckOut",
                table: "UserCheckIns");

            migrationBuilder.DropColumn(
                name: "TotalHours",
                table: "UserCheckIns");

            migrationBuilder.CreateTable(
                name: "UserCheckInCheckOuts",
                columns: table => new
                {
                    UserCheckInCheckOutId = table.Column<Guid>(nullable: false),
                    UserCheckInId = table.Column<Guid>(nullable: false),
                    CheckIn = table.Column<DateTime>(nullable: true),
                    CheckOut = table.Column<DateTime>(nullable: true),
                    TotalHours = table.Column<double>(nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserCheckInCheckOuts", x => x.UserCheckInCheckOutId);
                    table.ForeignKey(
                        name: "FK_UserCheckInCheckOuts_UserCheckIns_UserCheckInId",
                        column: x => x.UserCheckInId,
                        principalTable: "UserCheckIns",
                        principalColumn: "UserCheckInId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_UserCheckInCheckOuts_UserCheckInId",
                table: "UserCheckInCheckOuts",
                column: "UserCheckInId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "UserCheckInCheckOuts");

            migrationBuilder.AddColumn<DateTime>(
                name: "CheckIn",
                table: "UserCheckIns",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "CheckOut",
                table: "UserCheckIns",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "TotalHours",
                table: "UserCheckIns",
                type: "float",
                nullable: true);
        }
    }
}
