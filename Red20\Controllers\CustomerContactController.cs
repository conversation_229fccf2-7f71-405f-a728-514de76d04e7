﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Red20.Model.Data;
using Red20.Service.Data.Interface;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Red20.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class CustomerContactController : ControllerBase
    {

        private ICustomerContactService service;
        private ICustomerAddressService addressService;
        private ILogger<AuthController> logger;
        IUnitOfWork unitOfWork;

        public CustomerContactController(
            ICustomerContactService service,
            ICustomerAddressService addressService,
            IUnitOfWork unitOfWork,
            ILogger<AuthController> logger)
        {

            this.service = service;
            this.addressService = addressService;
            this.unitOfWork = unitOfWork;
            this.logger = logger;
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(CustomerContactModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id)
        {

            var Customer = await service.GetAsync(id);

            return Ok(Customer);
        }
        [HttpGet]
        [ProducesResponseType(200, Type = typeof(IList<CustomerModel>))]
        [ProducesResponseType(401)]
        public IActionResult GetAll()
        {
            var customers = service.GetAllCustomerContacts();
            return Ok(customers);
        }

        [HttpGet("byCustomer/{id}")]
        [ProducesResponseType(200, Type = typeof(IList<CustomerContactModel>))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetByCustomer(Guid id)
        {

            var CustomerContact = service.GetByCustomerAsync(id);

            return Ok(CustomerContact);
        }

        [HttpGet("byAddress/{id}")]
        [ProducesResponseType(200, Type = typeof(IList<CustomerContactModel>))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetByCustomerAddress(Guid id)
        {
            var CustomerContact = await service.GetContactModelsByCustomerAddress(id);
            return Ok(CustomerContact);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(CustomerContactModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody] CustomerContactUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }

            model.IsEnquiryContact = false;

            var CustomerContact = await service.PostAsync(model);

            return Ok(CustomerContact);
        }

        [HttpPost("enquiry")]
        [ProducesResponseType(200, Type = typeof(CustomerContactModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> AddEnquiryContact([FromBody] CustomerContactUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }

            model.IsEnquiryContact = true;

            var CustomerContact = await service.PostAsync(model);

            return Ok(CustomerContact);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(CustomerContactModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Put(Guid id, [FromBody] CustomerContactUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }
            var customerContact = await service.GetAsync(id);

            if (customerContact is null)
            {
                return BadRequest();
            }

            customerContact = await service.PutAsync(id, model);

            return Ok(customerContact);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Delete(Guid id)
        {

            await service.DeleteAsync(id);
            return Ok();
        }
    }
}