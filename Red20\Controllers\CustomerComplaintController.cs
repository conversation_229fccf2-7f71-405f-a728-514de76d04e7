﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Red20.Excel.Export;
using Red20.Model.Data.Customer;
using Red20.Model.Data.Document;
using Red20.Service.Data.Interface;
using Red20.Service.Storage.Interface;
using Red20.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Red20.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class CustomerComplaintController : ControllerBase
    {

        private ICustomerComplaintService customerComplaintService;
        private IPurchaseOrderService purchaseOrderService;
        private IOrderService orderService;
        private IDocumentService documentService;
        private IStorageService blobStorage;
        private ILogger<AuthController> logger;
        private IUserService userService;
        private IUnitOfWork unitOfWork;
        private IMapper mapper;

        public CustomerComplaintController(
            ICustomerComplaintService customerComplaintService,
            IPurchaseOrderService purchaseOrderService,
            IUnitOfWork unitOfWork,
            IUserService userService,
            IOrderService orderService,
            IDocumentService documentService,
            IStorageService blobStorage,
            IMapper mapper,
            ILogger<AuthController> logger)
        {

            this.customerComplaintService = customerComplaintService;
            this.purchaseOrderService = purchaseOrderService;
            this.logger = logger;
            this.unitOfWork = unitOfWork;
            this.userService = userService;
            this.orderService = orderService;
            this.documentService = documentService;
            this.blobStorage = blobStorage;
            this.mapper = mapper;
        }

        [HttpGet]
        [ProducesResponseType(200, Type = typeof(IList<CustomerComplaintModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get()
        {
            try
            {
                var customerComplaints = await customerComplaintService.GetAllCustomerComplaints();
                return Ok(customerComplaints);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot get complaints - {ex}");
                return BadRequest();
            }

        }

        [HttpGet("getAverage/{type}")]
        [ProducesResponseType(200, Type = typeof(IList<CustomerComplaintModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAverage(string type)
        {
            var closedComplaints = unitOfWork.CustomerComplaint.Query(c => c.DateClosed.HasValue && c.Type == type).ToList();
            var total = closedComplaints.Sum(s => s.TotalDays);
            var count = closedComplaints.Count();
            var totalAverage = total / count;
            return Ok(totalAverage);
        }


        [HttpGet("getOpen")]
        [ProducesResponseType(200, Type = typeof(IList<CustomerComplaintModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetOpen()
        {
            var closedComplaints = unitOfWork.CustomerComplaint.Query(c => !c.DateClosed.HasValue).ToList();
            var count = closedComplaints.Count();
            return Ok(count);
        }

        [HttpGet("getClosed")]
        [ProducesResponseType(200, Type = typeof(IList<CustomerComplaintModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetClosed()
        {
            var closedComplaints = unitOfWork.CustomerComplaint.Query(c => c.DateClosed.HasValue).ToList();
            var count = closedComplaints.Count();
            return Ok(count);
        }

        [HttpGet("getCustomers")]
        [ProducesResponseType(200, Type = typeof(IList<CustomerComplaintModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetCustomers()
        {
            var customers = unitOfWork.CustomerComplaint.Query(c => c.CustomerId.HasValue).ToList();
            var count = customers.Count();
            return Ok(count);
        }

        [HttpGet("getSuppliers")]
        [ProducesResponseType(200, Type = typeof(IList<CustomerComplaintModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetSuppliers()
        {
            var suppliers = unitOfWork.CustomerComplaint.Query(c => c.SupplierId.HasValue).ToList();
            var count = suppliers.Count();
            return Ok(count);
        }


        [HttpGet("getAllComplaints/{type}")]
        [ProducesResponseType(200, Type = typeof(IList<CustomerComplaintModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAllOpens(string type)
        {
            var model = new List<CustomerComplaintDashboardModel>();
            var newModel = model.OrderBy(c => c.Year).ThenBy(x => x.Month);

            for (int i = 0; i < 12; i++)
            {
                var date = DateTime.Now.AddMonths(-i);

                model.Add(new CustomerComplaintDashboardModel
                {
                    DisplayMonth = date.ToString("MMM"),
                    Month = date.Month,
                    Year = date.Year,
                    TotalOpen = unitOfWork.CustomerComplaint.Query(c => c.Type == type && !c.DateClosed.HasValue && c.Created.Month == date.Month && c.Created.Year == date.Year).Count(),
                    TotalClosed = unitOfWork.CustomerComplaint.Query(c => c.Type == type && c.DateClosed.HasValue && c.DateClosed.Value.Month == date.Month && c.DateClosed.Value.Year == date.Year).Count(),
                    TotalSuppliers = unitOfWork.CustomerComplaint.Query(c => c.Type == "Supplier" && c.Created.Month == date.Month && c.Created.Year == date.Year).Count(),
                    TotalCustomers = unitOfWork.CustomerComplaint.Query(c => c.Type == "Customer" && c.Created.Month == date.Month && c.Created.Year == date.Year).Count(),
                    TotalHigh = unitOfWork.CustomerComplaint.Query(c => c.Type == type && c.Severity == "High" && c.Created.Month == date.Month && c.Created.Year == date.Year).Count(),
                    TotalLow = unitOfWork.CustomerComplaint.Query(c => c.Type == type && c.Severity == "Low" && c.Created.Month == date.Month && c.Created.Year == date.Year).Count()
                });
            }
            return Ok(newModel);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(CustomerComplaintModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id)
        {

            var customerComplaint = await customerComplaintService.GetAsync(id);

            return Ok(customerComplaint);
        }

        [HttpGet("byType/{type}")]
        [ProducesResponseType(200, Type = typeof(IList<CustomerComplaintModel>))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetByType(string type)
        {

            var customerComplaints = await customerComplaintService.GetByTypeAsync(type);

            return Ok(customerComplaints);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(CustomerComplaintModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody] CustomerComplaintUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);
            model.Number = model.IsCustomer ? await NumberSequenceUtility.GetNextCustomerComplaintNumber(unitOfWork) : await NumberSequenceUtility.GetNextSupplierComplaintNumber(unitOfWork);
            model.Status = "Open";
            model.CreatedBy = user.Name;
            try
            {
                var customerComplaint = await customerComplaintService.PostAsync(model);

                return Ok(customerComplaint);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot create a compllaint - {ex}");
                return BadRequest();
            }
        }

        [HttpPut("close/{id}/{date}/{status}")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> CloseComplaint(Guid id, string date, string status)
        {
            var closedDate = Convert.ToDateTime(date);
            await customerComplaintService.CloseComplaint(id, closedDate, status);
            return Ok();
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(CustomerComplaintModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Put(Guid id, [FromBody] CustomerComplaintUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }

            var customerComplaint = await customerComplaintService.GetAsync(id);

            if (customerComplaint is null)
            {
                return BadRequest();
            }

            customerComplaint = await customerComplaintService.PutAsync(id, model);

            return Ok(customerComplaint);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Delete(Guid id)
        {

            await customerComplaintService.DeleteAsync(id);
            return Ok();
        }

        [HttpGet("complaintRatioReport/{type}/{from}/{to}")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> PrintComplaintRatioReport(string type, DateTime from, DateTime to)
        {
            var fromMonth = from.ToString("MMMM");
            var toMonth = to.ToString("MMMM");
            var purchaseOrders = await purchaseOrderService.GetAllPurchaseOrders();
            var filteredPurchaseOrders = purchaseOrders.Where(c => c.Created.Date >= from.Date && c.Created.Date <= to.Date).ToList();
            var orders = await orderService.GetAllAsync("");
            var filteredOrders = orders.Where(c => c.Created.Date >= from.Date && c.Created.Date <= to.Date).ToList();
            var complaints = await unitOfWork.CustomerComplaint
                .Query(c => c.Created.Date >= from.Date && c.Created.Date <= to.Date)
                .AsNoTracking()
                .Include(i => i.Order)
                .Include(i => i.Customer)
                .Include(i => i.Supplier)
                .Include(i => i.PurchaseOrder)
                .OrderByDescending(o => o.Number)
                .ToListAsync();

            var models = new List<CustomerComplaintsRatioModel>();
            var accountCodeModels = new List<CustomerComplaintsRatioModel>();
            var groupedOrdersByMonth = filteredOrders.GroupBy(c => c.Created.ToString("MMMMM")).ToList();
            var groupedOrderByAccountCodes = filteredOrders.GroupBy(c => c.AccountCode).ToList();
            var groupedPurchaseOrdersByMonth = filteredPurchaseOrders.GroupBy(c => c.Created.ToString("MMMMM")).ToList();
            var groupedPurchaseOrdersByAccountCode = filteredPurchaseOrders.GroupBy(c => c.AccountCode).ToList();
            var groupedComplaintsByMonth = type == "Customer" ? complaints.Where(d => d.IsCustomer).GroupBy(c => c.Created.ToString("MMMM")).ToList() : complaints.Where(d => !d.IsCustomer).GroupBy(c => c.Created.ToString("MMMM")).ToList();
            var groupedComplaintsByAccountCode = type == "Customer" ? complaints.Where(d => d.IsCustomer).GroupBy(c => c.Order.AccountCode).ToList() : complaints.Where(d => !d.IsCustomer).GroupBy(c => c.PurchaseOrder.AccountCode).ToList();

            if (type == "Customer")
            {
                foreach (var order in groupedOrdersByMonth)
                {
                    var model = new CustomerComplaintsRatioModel
                    {
                        Month = order.Key,
                        TotalOrdered = order.Count()
                    };
                    models.Add(model);
                }
            } else
            {
                foreach (var order in groupedPurchaseOrdersByMonth)
                {
                    var model = new CustomerComplaintsRatioModel
                    {
                        Month = order.Key,
                        TotalOrdered = order.Count()
                    };
                    models.Add(model);
                }
            }
            if (type == "Customer")
            {
                foreach (var order in groupedOrderByAccountCodes)
                {
                    var model = new CustomerComplaintsRatioModel
                    {
                        AccountCode = order.Key,
                        TotalOrdered = order.Count()
                    };
                    accountCodeModels.Add(model);
                }
            } else
            {
                foreach (var order in groupedPurchaseOrdersByAccountCode)
                {
                    var model = new CustomerComplaintsRatioModel
                    {
                        AccountCode = order.Key,
                        TotalOrdered = order.Count()
                    };
                    accountCodeModels.Add(model);
                }
            }

            foreach (var complaint in groupedComplaintsByMonth)
            {
                var model = new CustomerComplaintsRatioModel
                {
                    Month = complaint.Key,
                    TotalComplaints = complaint.Count()
                };
                models.Add(model);
            }

            foreach (var complaint in groupedComplaintsByAccountCode)
            {
                var model = new CustomerComplaintsRatioModel
                {
                    AccountCode = complaint.Key,
                    TotalComplaints = complaint.Count()
                };
                accountCodeModels.Add(model);
            }

            var groupedModels = models.GroupBy(c => c.Month).Select(d => new CustomerComplaintsRatioModel
            {
                Month = d.Key,
                TotalComplaints = d.Sum(c => c.TotalComplaints),
                TotalOrdered = d.Sum(c => c.TotalOrdered)
            }).ToList();

            var groupedAccountCodeModels = accountCodeModels.GroupBy(c => c.AccountCode).Select(d => new CustomerComplaintsRatioModel
            {
                AccountCode = d.Key,
                TotalComplaints = d.Sum(c => c.TotalComplaints),
                TotalOrdered = d.Sum(c => c.TotalOrdered)
            }).ToList();

            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);
            var currentUser = $"{user.Firstname} {user.Lastname}";
            try
            {
                var itemData = ExportUtility.ExportCustomerComplaintReport(groupedModels, groupedAccountCodeModels, currentUser, fromMonth, toMonth);
                return File(itemData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            } catch (Exception ex)
            {
                var error = ex;
            }

            return Ok();
        }

        [HttpPost("complaintReport/{type}")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> PrintComplaintReport(string type, List<CustomerComplaintModel> models)
        {
            var document = new DocumentModel();
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);
            var currentUser = $"{user.Firstname} {user.Lastname}";
            try
            {
                var itemData = type == "Customer" ? ExportUtility.ExportCustomerComplaintReport(models, currentUser) : ExportUtility.ExportSupplierComplaintReport(models, currentUser);
                document = await documentService.PostAsync("Customer_Complaint_Report.xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"{user.Firstname} {user.Lastname}", "Customer Complaints", null, null, null, false, false, false);
                var file = File(itemData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                await blobStorage.UploadAsync(document.DocumentId, itemData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                return Ok();
            } catch (Exception ex)
            {
                var error = ex;
            }

            return Ok();
        }
        [HttpGet("downloadReport")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> DownloadReport()
        {
            try
            {
                var lastDocument = await unitOfWork.Document.Query().Where(t => t.Type == "Customer Complaints").OrderByDescending(c => c.Created).FirstOrDefaultAsync();
                var download = await blobStorage.DownloadAsync(lastDocument.DocumentId);
                var file = File(download.Item1, download.Item2);
                return file;
            } catch (Exception ex)
            {
                return NotFound();
            }
        }
    }
}