﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace Red20.Data.Migrations
{
    public partial class RemoveAllocatedAndUnallocatedFromStockAllocationsToBeComputedProps : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ExistingAllocation",
                table: "OrderStockAllocations");

            migrationBuilder.DropColumn(
                name: "Unallocated",
                table: "OrderStockAllocations");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<double>(
                name: "ExistingAllocation",
                table: "OrderStockAllocations",
                type: "float",
                nullable: false,
                defaultValue: 0.0);

            migrationBuilder.AddColumn<double>(
                name: "Unallocated",
                table: "OrderStockAllocations",
                type: "float",
                nullable: false,
                defaultValue: 0.0);
        }
    }
}
