﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Red20.Excel.Export;
using Red20.Model.Data.Document;
using Red20.Model.Data.Stock;
using Red20.Model.Data.StockTransaction;
using Red20.Service.Data.Interface;
using Red20.Service.Storage.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Red20.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class StockTransactionController : ControllerBase
    {
        private IStockTransactionService stockTransactionService;
        private ILogger<AuthController> logger;
        private IUnitOfWork unitOfWork;
        private IDocumentService documentService;
        private IStorageService blobStorage;
        private IUserService userService;

        public StockTransactionController(IStockTransactionService stockTransactionService, ILogger<AuthController> logger, IUnitOfWork unitOfWork, IDocumentService documentService, IStorageService blobStorage, IUserService userService)
        {
            this.stockTransactionService = stockTransactionService;
            this.logger = logger;
            this.unitOfWork = unitOfWork;
            this.documentService = documentService;
            this.blobStorage = blobStorage;
            this.userService = userService;
        }

        [HttpGet("byStock/{id}")]
        [ProducesResponseType(200, Type = typeof(List<StockTransactionModel>))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetByStockId(Guid id)
        {
            try
            {
                var stockTransactions = await stockTransactionService.GetByStockIdAsync(id);
                return Ok(stockTransactions);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot get stock transactions - {ex}");
                return BadRequest();
            }
        }

        [HttpGet("byArchivedStock/{id}")]
        [ProducesResponseType(200, Type = typeof(List<StockTransactionModel>))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAllArchivedByStockId(Guid id)
        {
            try
            {
                var stockTransactions = await stockTransactionService.GetAllArchivedByStockIdAsync(id);
                return Ok(stockTransactions);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot get archived stock transactions - {ex}");
                return BadRequest();
            }
        }

        [HttpPost("export")]
        [ProducesResponseType(200)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> GenerateReport(List<StockTransactionModel> models)
        {
            try
            {
                var document = new DocumentModel();
                var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
                if (emailClaim == null)
                {
                    return Unauthorized();
                }

                var user = await userService.GetUserByEmailAsync(emailClaim.Value);
                var currentUser = $"{user.Firstname} {user.Lastname}";
                var itemData = ExportUtility.ExportStockTransactionReport(models, currentUser);
                document = await documentService.PostAsync("Stock_Transaction_Report.xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"{user.Firstname} {user.Lastname}", "Stock Transactions", null, null, null, false, false, false);
                var file = File(itemData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                await blobStorage.UploadAsync(document.DocumentId, itemData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                return Ok();
            } catch (Exception ex)
            {
                return NotFound();
            }
        }

        [HttpGet("downloadReport")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> DownloadReport()
        {
            try
            {
                var lastDocument = await unitOfWork.Document.Query().Where(t => t.Type == "Stock Transactions").OrderByDescending(c => c.Created).FirstOrDefaultAsync();
                var download = await blobStorage.DownloadAsync(lastDocument.DocumentId);
                var file = File(download.Item1, download.Item2);
                return file;
            } catch (Exception ex)
            {
                return NotFound();
            }
        }

        [HttpGet("printStock/{fromDate}/{categoryId}")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> StockReport(DateTime fromDate, Guid categoryId)
        {
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);
            var currentUser = $"{user.Firstname} {user.Lastname}";

            var result = new List<SlowMovingStockModel>();

            var stockTransactions = await unitOfWork.StockTransaction
                      .Query(q => q.Stock.CategoryId == categoryId && q.Stock.PhysicalStock > 0)
                      .Include(s => s.Stock)
                      .ThenInclude(s => s.Category)
                      .GroupBy(st => st.StockId)
                      .Select(g => new { StockId = g.Key, LatestTransactionDate = g.Max(st => st.Created) })
                      .Where(x => x.LatestTransactionDate <= fromDate)
                      .ToListAsync();


            var stockIds = stockTransactions.Select(x => x.StockId).ToList();

            var stocks = await unitOfWork.Stock.Query(s => stockIds.Contains(s.StockId)).Include(c => c.Category).ToListAsync();

            foreach (var stock in stocks)
            {
                var slowStock = new SlowMovingStockModel
                {
                    StockCode = stock.Code,
                    Category = $"{stock.Category.Code} - {stock.Category.Description}",
                    CostPrice = stock.CostPrice,
                    Description = stock.Description,
                    PhysicalStock = stock.PhysicalStock,
                    TotalNet = stock.CostPrice.Value * stock.PhysicalStock
                };

                result.Add(slowStock);
            }


            var stockTransationData = ExportUtility.ExportSlowMovingStocks(result, currentUser);

            return File(stockTransationData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        }

        [HttpGet("printAllSlowingMovingStocks/{fromDate}")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> PrintAllSlowingMovingStocks(DateTime fromDate)
        {
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);
            var currentUser = $"{user.Firstname} {user.Lastname}";

            var result = new List<SlowMovingStockModel>();

            var categories = await unitOfWork.Category.Query().ToListAsync();

            var newStockTransactionList = new List<Model.Entity.StockTransaction>();

            foreach (var category in categories)
            {
                var stockTransactions = await unitOfWork.StockTransaction
                      .Query(q => q.Stock.CategoryId == category.CategoryId && q.Stock.PhysicalStock > 0)
                      .Include(s => s.Stock)
                      .ThenInclude(s => s.Category)
                      .GroupBy(st => st.StockId)
                      .Select(g => new { StockId = g.Key, LatestTransactionDate = g.Max(st => st.Created) })
                      .Where(x => x.LatestTransactionDate <= fromDate)
                      .ToListAsync();

                newStockTransactionList.AddRange(stockTransactions.Select(st => new Model.Entity.StockTransaction
                {
                    StockId = st.StockId,
                    Created = st.LatestTransactionDate
                }));
            }

            var stockIds = newStockTransactionList.Select(x => x.StockId).ToList();

            var stocks = await unitOfWork.Stock.Query(s => stockIds.Contains(s.StockId)).Include(c => c.Category).ToListAsync();

            foreach (var stock in stocks)
            {
                var slowStock = new SlowMovingStockModel
                {
                    StockCode = stock.Code,
                    Category = $"{stock.Category.Code} - {stock.Category.Description}",
                    CostPrice = stock.CostPrice,
                    Description = stock.Description,
                    PhysicalStock = stock.PhysicalStock,
                    TotalNet = stock.CostPrice.Value * stock.PhysicalStock
                };

                result.Add(slowStock);
            }

            var stockTransationData = ExportUtility.ExportSlowMovingStocks(result, currentUser);

            return File(stockTransationData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        }
    }
}
