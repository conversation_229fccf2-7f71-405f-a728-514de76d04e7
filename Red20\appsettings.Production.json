{
  "ConnectionStrings": {
    "ConnectionString": "Server=tcp:red-20.database.windows.net,1433;Initial Catalog=red20;Persist Security Info=False;User ID=red20;Password=**********;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;"
    //"SqlConnectionString": "Server=(local);Initial Catalog=red20; Integrated Security=True; MultipleActiveResultSets=true"
  },
  "Client": {
    "Host": "https://red-20.redroosterlifting.com/",
    "EngineerHost": "https://red-20timesheet.redroosterlifting.com/"
  },
  "Security": {
    "Key": "{BAE6DD16-59D9-4092-AF82-F32F260B5207}",
    "TokenExpiryMins": "60",
    "RefreshTokenExpiryMins": "60"
  },
  "AzureBlobStorage": {
    "StorageAccountName": "red20web",
    "StorageAccountKey": "****************************************************************************************",
    "Container": "files"
  },
  "XeroConfiguration": {
    "ClientId": "E4BC995AA0AC46D6A87ABC2FA1CDC606",
    "ClientSecret": "E4F0E4LJM4YrCeVaH9yFQN8FhyNm_qn_BPaTfeTotfTx8RQx",
    "CallbackUri": "https://localhost:44359/Authorization/Callback",
    "Scope": "openid profile email files offline_access accounting.transactions accounting.transactions.read accounting.reports.read accounting.journals.read accounting.settings accounting.settings.read accounting.contacts accounting.contacts.read accounting.attachments accounting.attachments.read assets assets.read",
    "State": "YOUR_STATE"
  },
  "Email": {
    "SmtpHost": "smtp.mandrillapp.com",
    "Sender": "<EMAIL>",
    "Name": "Red 20",
    "MandrillApiKey": "**********************",
    "CertificateExpiryEmail": "<EMAIL>"
  },
  "ElmahIo": {
    "ApiKey": "8fa238b6630d4e299547ae4ad9da8c4d",
    "LogId": "1b40cb39-14e9-4dd8-8e31-0296080c1b93",
    "LogLevel": {
      "Default": "Warning"
    }
  },
  "AllowedHosts": "*",
  "KeyVaultUri": "",
  "Auth0": {
    "Domain": "red20.uk.auth0.com",
    "Audience": "https://red20.uk.auth0.com/api/v2/",
    "DatabaseConnectionId": "con_txQOPHudo0fTzgm5",
    "DatabaseConnectionName": "red20-live"
  }
}