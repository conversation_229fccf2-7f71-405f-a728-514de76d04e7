﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Red20.Excel.Export;
using Red20.Model.Data;
using Red20.Model.Data.Document;
using Red20.Model.Entity;
using Red20.Service.Data;
using Red20.Service.Data.Interface;
using Red20.Service.Email.Interface;
using Red20.Service.Storage.Interface;
using Red20.Service.Xero.Interface;
using Red20.Settings;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;

namespace Red20.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class UserController : ControllerBase
    {

        private IUserService service;
        private IUserPayrollRateService payrollRateService;
        private IUserRed20RateService red20RateService;
        private ILogger<AuthController> logger;
        private IEmailService emailService;
        private IDocumentService documentService;
        private ClientSettings settings;
        private IUnitOfWork unitOfWork;
        private IStorageService blobStorage;
        private IXeroService xeroService;
        private IUserTimesheetEntryService userTimesheetEntryService;
        private IAuth0UserService auth0UserService;

        public UserController(
            IUserService service,
            IUserPayrollRateService payrollRateService,
            IUserRed20RateService red20RateService,
            IEmailService emailService,
            IOptions<ClientSettings> settings,
            IUnitOfWork unitOfWork,
            IStorageService blobStorage,
            IDocumentService documentService,
            IUserTimesheetEntryService userTimesheetEntryService,
            IXeroService xeroService,
            IAuth0UserService auth0UserService,
            ILogger<AuthController> logger)
        {

            this.service = service;
            this.payrollRateService = payrollRateService;
            this.red20RateService = red20RateService;
            this.emailService = emailService;
            this.settings = settings.Value;
            this.logger = logger;
            this.unitOfWork = unitOfWork;
            this.blobStorage = blobStorage;
            this.documentService = documentService;
            this.xeroService = xeroService;
            this.userTimesheetEntryService = userTimesheetEntryService;
            this.auth0UserService = auth0UserService;
        }

        [HttpGet]
        [ProducesResponseType(200, Type = typeof(IList<UserModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get()
        {
            var users = await service.GetAsync();
            return Ok(users.Where(c => c.Firstname != "Check-In" && !c.DateArchived.HasValue));
        }           


        [HttpGet("officeUsers")]
        [ProducesResponseType(200, Type = typeof(IList<UserModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetOfficeUsers()
        {
            var users = await service.GetAsync();
            var newUsers = users.Where(c => c.Firstname != "Check-In" && c.Firstname != "Arrash" && c.Firstname != "Ravi" && !c.Roles.Contains("Engineer"));
            return Ok(newUsers);
        }

        [HttpGet("newStarts")]
        [ProducesResponseType(200, Type = typeof(IList<UserModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetNewStarts()
        {
            var users = unitOfWork.User.Query(c => c.StartDate.HasValue && c.StartDate.Value.Month == DateTime.Now.Month && c.StartDate.Value.Year == DateTime.Now.Year).ToList();
            return Ok(users);
        }

        [HttpGet("archivedUsers")]
        [ProducesResponseType(200, Type = typeof(IList<UserModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetArchivedUsers()
        {
            var users = unitOfWork.User.Query(c => c.DateArchived.HasValue).ToList();
            return Ok(users);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(UserModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id)
        {
            if (!await service.UserIdExistsAsync(id))
            {
                return NotFound();
            }

            var user = await service.GetAsync(id);

            return Ok(user);
        }

        //get user by email

        [HttpGet("byemail/{email}")]
        [ProducesResponseType(200, Type = typeof(UserModel))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetUserByEMail(string email)
        {
            var user = await service.GetUserByEmailAsync(email);

            return Ok(user);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(UserModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody] UserUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }
            if (await service.EmailAddressExistsAsync(model.EmailAddress))
            {
                return BadRequest("Email address already exists");
            }

            var builder = new StringBuilder();
            var random = new Random();
            char character;

            for (int i = 0; i < 8; i++)
            {
                character = Convert.ToChar(Convert.ToInt32(Math.Floor(26 * random.NextDouble() + 65)));
                builder.Append(character);
            }
            var userPassword = builder.ToString();

            var user = await service.PostAsync(model, userPassword);

            await auth0UserService.CreateAuth0User(user);

            var url = user.IsMobileUser ? settings.EngineerHost : settings.Host;

            var name = $"{user.Firstname} {user.Lastname}";

            if (user.IsMobileUser)
            {
                await emailService.SendNewUsermail(user.EmailAddress, name, userPassword, url);
            }

            return Ok(user);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(UserModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Put(Guid id, [FromBody] UserUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }

            if (!await service.UserIdExistsAsync(id))
            {
                return NotFound();
            }

            if (model.Leaver)
            {
                model.DateArchived = DateTime.Now;
            }

            var oldUser = await service.GetAsync(id);

            var user = await service.PutAsync(id, model);

            await auth0UserService.UpdateAuth0UserEmail(oldUser, user);

            return Ok(user);
        }

        [HttpPut("archive/{id}")]
        [ProducesResponseType(200, Type = typeof(UserModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> ArchiveUser(Guid id, [FromBody] UserUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }

            if (!await service.UserIdExistsAsync(id))
            {
                return NotFound();
            }
            model.DateArchived = DateTime.Now;

            var user = await service.PutAsync(id, model);
            return Ok(user);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Delete(Guid id)
        {
            if (!await service.UserIdExistsAsync(id))
            {
                return NotFound();
            }

            var user = await service.GetAsync(id);

            await auth0UserService.DeleteAuth0User(user.EmailAddress);

            await service.DeleteAsync(id);

            return Ok();
        }

        [HttpGet("getUserData")]
        [ProducesResponseType(200, Type = typeof(UserModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetUserData()
        {
            var user = new UserModel();
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                var nameClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);
                 user = await service.GetAsync(Guid.Parse(nameClaim.Value));
            } else
            {
                user = await service.GetUserByEmailAsync(emailClaim.Value);
            }

            return Ok(user);
        }

        [AllowAnonymous]
        [HttpPost("sendpasswordreset")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> SendPasswordReset([FromBody] UserPasswordResetRequestModel model)
        {
            if (string.IsNullOrEmpty(model.EmailAddress))
            {
                return BadRequest();
            }

            if (!await service.EmailAddressExistsAsync(model.EmailAddress))
            {
                return BadRequest("Email address not found");
            }

            var url = settings.Host;
            var passwordReset = await service.SetPasswordReset(model.EmailAddress);

            if (passwordReset is null)
            {
                return BadRequest("Unable to send password reset request");
            }

            var link = Flurl.Url.Combine(url, "password-reset", passwordReset.ToString());

            await emailService.SendPasswordResetEmail(model.EmailAddress, link);

            return Ok();
        }

        [AllowAnonymous]
        [HttpPost("resetPassword/{id}")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> ResetPassword(Guid id, [FromBody] UserResetPasswordModel model)
        {
            var user = await service.ResetPassword(id, model.Password);

            if (!user)
            {
                return NotFound();
            }

            return Ok();
        }

        [HttpPut("updatePassword/{id}")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> UpdatePassword(Guid id, [FromBody] UserResetPasswordModel model)
        {
            var user = await service.ResetPassword(id, model.Password);

            if (!user)
            {
                return NotFound();
            }

            return Ok();
        }

        #region Payroll Rates

        [HttpGet("getPayrollRates/{id}")]
        [ProducesResponseType(200, Type = typeof(UserPayrollRateModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetPayrollRates(Guid id)
        {

            var payrollRates = await payrollRateService.GetByUserIdAsync(id);

            return Ok(payrollRates);
        }

        [HttpPost("postPayrollRate")]
        [ProducesResponseType(200, Type = typeof(UserUpdatePayrollRateModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> PostPayrollRate([FromBody] UserUpdatePayrollRateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }
            model.Modified = DateTime.Now;
            var payrollRate = await payrollRateService.PostAsync(model);
            return Ok(payrollRate);
        }

        [HttpPut("updatePayrollRate/{id}")]
        [ProducesResponseType(200, Type = typeof(UserUpdatePayrollRateModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> UpdatePayrollRate(Guid id, [FromBody] UserUpdatePayrollRateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }
            model.Modified = DateTime.Now;
            var payrollRate = await payrollRateService.PutAsync(id, model);
            return Ok(payrollRate);
        }

        [HttpDelete("deletePayRollRate/{id}")]
        [ProducesResponseType(200, Type = typeof(UserUpdatePayrollRateModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> DeletePayrollRate(Guid id)
        {

            await payrollRateService.DeleteAsync(id);
            return Ok();
        }

        #endregion

        #region Red20 Rates

        [HttpGet("getRed20Rates/{id}")]
        [ProducesResponseType(200, Type = typeof(UserRed20RateModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetRed20Rates(Guid id)
        {

            var red20Rates = await red20RateService.GetByUserIdAsync(id);

            return Ok(red20Rates);
        }

        [HttpPost("postRed20Rate")]
        [ProducesResponseType(200, Type = typeof(UserRed20RateUpdateModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> PostRed20Rate([FromBody] UserRed20RateUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }
            model.Created = DateTime.Now;
            model.Modified = DateTime.Now;
            var red20Rate = await red20RateService.PostAsync(model);
            return Ok(red20Rate);
        }

        [HttpPut("updateRed20Rate/{id}")]
        [ProducesResponseType(200, Type = typeof(UserRed20RateUpdateModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> UpdateRed20Rate(Guid id, [FromBody] UserRed20RateUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }
            model.Modified = DateTime.Now;
            var red20Rate = await red20RateService.PutAsync(id, model);
            return Ok(red20Rate);
        }

        [HttpDelete("deleteRed20RollRate/{id}")]
        [ProducesResponseType(200, Type = typeof(UserRed20RateUpdateModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> DeleteRed20Rate(Guid id)
        {

            await red20RateService.DeleteAsync(id);
            return Ok();
        }

        #endregion

        [HttpPost("printUser")]
        [ProducesResponseType(200)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> GenerateUserReport(List<UserModel> models)
        {
            try
            {
                var document = new DocumentModel();
                var newPayrollRates = new List<Model.Entity.UserPayrollRate>();
                var newRed20Rates = new List<Model.Entity.UserRed20Rate>();
                var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
                if (emailClaim == null)
                {
                    return Unauthorized();
                }

                var user = await service.GetUserByEmailAsync(emailClaim.Value);
                var currentUser = $"{user.Firstname} {user.Lastname}";
                var payrollRates = unitOfWork.UserPayrollRate.Query().Include(c => c.User).ToList();
                var red20Rates = unitOfWork.UserRed20Rate.Query().Include(c => c.User).ToList();

                foreach (var item in models)
                {
                    var usersPayrollRates = payrollRates.Where(c => c.UserId == item.UserId).ToList();

                    var groupedRates = usersPayrollRates.GroupBy(c => c.PayType);

                    foreach (var payrollRate in groupedRates)
                    {
                        var newPayrollRate = new UserPayrollRate();

                        if (payrollRate.Key == "Salary")
                        {
                            newPayrollRate = usersPayrollRates.Where(c => c.PayType == "Salary").OrderByDescending(c => c.Created).FirstOrDefault();
                        } else if (payrollRate.Key == "Annual Salary")
                        {
                            newPayrollRate = usersPayrollRates.Where(c => c.PayType == "Annual Salary").OrderByDescending(c => c.Created).FirstOrDefault();
                        } else if (payrollRate.Key == "Salary Adjustment")
                        {
                            newPayrollRate = usersPayrollRates.Where(c => c.PayType == "Salary Adjustment").OrderByDescending(c => c.Created).FirstOrDefault();
                        } else if (payrollRate.Key == "Hourly Rate")
                        {
                            newPayrollRate = usersPayrollRates.Where(c => c.PayType == "Hourly Rate").OrderByDescending(c => c.Created).FirstOrDefault();
                        } else if (payrollRate.Key == "Overtime 1")
                        {
                            newPayrollRate = usersPayrollRates.Where(c => c.PayType == "Overtime 1").OrderByDescending(c => c.Created).FirstOrDefault();
                        } else if (payrollRate.Key == "Overtime 2")
                        {
                            newPayrollRate = usersPayrollRates.Where(c => c.PayType == "Overtime 2").OrderByDescending(c => c.Created).FirstOrDefault();
                        } else if (payrollRate.Key == "Offshore Rate Weekday")
                        {
                            newPayrollRate = usersPayrollRates.Where(c => c.PayType == "Offshore Rate Weekday").OrderByDescending(c => c.Created).FirstOrDefault();
                        } else if (payrollRate.Key == "Offshore Rate Weekend")
                        {
                            newPayrollRate = usersPayrollRates.Where(c => c.PayType == "Offshore Rate Weekend").OrderByDescending(c => c.Created).FirstOrDefault();
                        } else if (payrollRate.Key == "First Aid/Warden")
                        {
                            newPayrollRate = usersPayrollRates.Where(c => c.PayType == "First Aid/Warden").OrderByDescending(c => c.Created).FirstOrDefault();
                        } else if (payrollRate.Key == "Holiday Pay")
                        {
                            newPayrollRate = usersPayrollRates.Where(c => c.PayType == "Holiday Pay").OrderByDescending(c => c.Created).FirstOrDefault();
                        } else if (payrollRate.Key == "Bonus")
                        {
                            newPayrollRate = usersPayrollRates.Where(c => c.PayType == "Bonus").OrderByDescending(c => c.Created).FirstOrDefault();
                        }
                        newPayrollRates.Add(newPayrollRate);
                    }
                    foreach (var red20Rate in red20Rates.Where(c => c.UserId == item.UserId))
                    {
                        newRed20Rates.Add(red20Rate);
                    }
                }

                var itemData = ExportUtility.ExportUserMonthEndReport(models, currentUser, newPayrollRates);
                document = await documentService.PostAsync("User_Month_End_Report.xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"{user.Firstname} {user.Lastname}", "Users", null, null, null, false, false, false);
                var file = File(itemData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                await blobStorage.UploadAsync(document.DocumentId, itemData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                return Ok();
            } catch (Exception)
            {
                return NotFound();
            }
        }

        [HttpGet("downloadReport")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> DownloadReport()
        {
            try
            {
                var lastDocument = await unitOfWork.Document.Query().Where(c => c.Type == "Users").OrderByDescending(c => c.Created).FirstOrDefaultAsync();
                var download = await blobStorage.DownloadAsync(lastDocument.DocumentId);
                var file = File(download.Item1, download.Item2);
                return file;
            } catch (Exception)
            {
                return NotFound();
            }
        }

        [HttpGet("printLabour/{from}/{to}")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> PrintLabourJournal(DateTime from, DateTime to)
        {
            try
            {
                // Get current user info asynchronously early to parallelize operations
                var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
                if (emailClaim == null)
                {
                    return Unauthorized();
                }

                var user = await service.GetUserByEmailAsync(emailClaim.Value);
                var currentUser = $"{user.Firstname} {user.Lastname}";

                var timesheetEntriesTask = await unitOfWork.UserTimesheetEntry
                    .Query()
                    .AsNoTracking() // Use no-tracking for read-only operations
                    .Where(c => c.Created.Date >= from.Date && c.Created.Date <= to.Date)
                    .Include(c => c.UserTimesheet)
                    .Include(c => c.Order)
                    .AsSplitQuery()
                    .ToListAsync();

                // Wait for both async operations
                var timesheetEntries = timesheetEntriesTask;
                var regularEntries = timesheetEntries.Where(e => e.PayType == "Hourly Rate");
                var overtimeEntries = timesheetEntries.Where(e => e.PayType != "Hourly Rate");

                // Extract unique user IDs
                var userIds = regularEntries.Select(s => s.UserTimesheet.UserId)
                    .Union(overtimeEntries.Select(s => s.UserTimesheet.UserId))
                    .ToHashSet();

                // Single query for all users with necessary includes
                var users = await unitOfWork.User
                    .Query()
                    .AsNoTracking()
                    // Use no-tracking for read-only operations
                    .Where(c => userIds.Contains(c.UserId) &&
                          (c.Department == "Unallocated Labour" ||
                           c.Department == "West Brom - Unallocated Labour"))
                    .Include(c => c.UserPayrollAdjustmentItems)
                    .Include(c => c.UserPayrollRates)
                    .Include(c => c.UserRed20Rates)
                    .Include(c => c.UserTimesheets)
                        .ThenInclude(c => c.UserTimesheetEntries)
                    .AsSplitQuery()
                    .ToListAsync();

                var itemData = await Task.Run(() => ExportUtility.ExportLabourJournal(
                    users,
                    users, // No need for separate overtime users query
                    regularEntries.ToList(),
                    overtimeEntries.ToList(),
                    currentUser));

                // Create document and upload in parallel
                var document = await documentService.PostAsync(
                    "User_Month_End_Report.xlsx",
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    currentUser,
                    "Users",
                    null, null, null,
                    false, false, false);

                // Start upload without awaiting
                var uploadTask = blobStorage.UploadAsync(
                    document.DocumentId,
                    itemData,
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

                // Create file result
                var file = File(
                    itemData,
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

                // Wait for upload to complete before returning
                await uploadTask;
                return file;
            } catch (Exception ex)
            {
                // Log the exception
                logger.LogError(ex, "Error generating labour journal");
                return StatusCode(500, "An error occurred while generating the report");
            }
        }

        private async Task<string> GetCurrentUserAsync()
        {
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            
            var user = await service.GetUserByEmailAsync(emailClaim.Value);

            return $"{user.Firstname} {user.Lastname}";
        }

        [HttpGet("xeroCustomers")]
        [ProducesResponseType(200, Type = typeof(IList<UserModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetXeroCustomers()
        {
            var users = await xeroService.GetContactsAsync();
            var usersList = users.Where(c => c.IsCustomer.Value).ToList();
            return Ok(usersList);
        }
    }
}