﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace Red20.Data.Migrations
{
    public partial class UserTimesheetentryapproved : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsFridayApproved",
                table: "UserTimesheetEntries",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsMondayApproved",
                table: "UserTimesheetEntries",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsSaturdayApproved",
                table: "UserTimesheetEntries",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsSundayApproved",
                table: "UserTimesheetEntries",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsThursdayApproved",
                table: "UserTimesheetEntries",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsTuesdayApproved",
                table: "UserTimesheetEntries",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsWednesdayApproved",
                table: "UserTimesheetEntries",
                nullable: false,
                defaultValue: false);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsFridayApproved",
                table: "UserTimesheetEntries");

            migrationBuilder.DropColumn(
                name: "IsMondayApproved",
                table: "UserTimesheetEntries");

            migrationBuilder.DropColumn(
                name: "IsSaturdayApproved",
                table: "UserTimesheetEntries");

            migrationBuilder.DropColumn(
                name: "IsSundayApproved",
                table: "UserTimesheetEntries");

            migrationBuilder.DropColumn(
                name: "IsThursdayApproved",
                table: "UserTimesheetEntries");

            migrationBuilder.DropColumn(
                name: "IsTuesdayApproved",
                table: "UserTimesheetEntries");

            migrationBuilder.DropColumn(
                name: "IsWednesdayApproved",
                table: "UserTimesheetEntries");
        }
    }
}
