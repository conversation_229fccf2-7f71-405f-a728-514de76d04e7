﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Red20.Model.Data;
using Red20.Model.Entity;
using Red20.Service.Data.Interface;

namespace Red20.Controllers {
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class AnalysisCodeController : ControllerBase {

        private IAnalysisCodeService analysisCodeService;
        private ILogger<AuthController> logger;

        public AnalysisCodeController(
            IAnalysisCodeService analysisCodeService,
            IUnitOfWork unitOfWork,
            ILogger<AuthController> logger) {

            this.analysisCodeService = analysisCodeService;
            this.logger = logger;
        }

        [HttpGet]
        [ProducesResponseType(200, Type = typeof(IList<AnalysisCodeModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get() {
            var analysisCodes = analysisCodeService.GetAllAnalysisCodes();
            return Ok(analysisCodes);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(AnalysisCodeModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id) {

            var analysisCode = await analysisCodeService.GetAsync(id);

            return Ok(analysisCode);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(AnalysisCodeModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody]AnalysisCodeUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }

            var analysisCode = await analysisCodeService.PostAsync(model);

            return Ok(analysisCode);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(AnalysisCodeModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Put(Guid id, [FromBody]AnalysisCodeUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }

            var analysisCode = await analysisCodeService.GetAsync(id);

            if (analysisCode is null) {
                return BadRequest();
            }

            analysisCode = await analysisCodeService.PutAsync(id, model);

            return Ok(analysisCode);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Delete(Guid id) {

            await analysisCodeService.DeleteAsync(id);
            return Ok();
        }
    }
}