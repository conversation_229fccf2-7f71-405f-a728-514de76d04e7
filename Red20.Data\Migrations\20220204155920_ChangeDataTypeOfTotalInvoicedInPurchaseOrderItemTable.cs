﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Red20.Data.Migrations
{
    public partial class ChangeDataTypeOfTotalInvoicedInPurchaseOrderItemTable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<double>(
                name: "TotalInvoiced",
                table: "PurchaseOrderItems",
                type: "float",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_OrderAssemblyHireEquipments_EquipmentId",
                table: "OrderAssemblyHireEquipments",
                column: "EquipmentId");

            migrationBuilder.AddForeignKey(
                name: "FK_OrderAssemblyHireEquipments_HireEquipments_EquipmentId",
                table: "OrderAssemblyHireEquipments",
                column: "EquipmentId",
                principalTable: "HireEquipments",
                principalColumn: "HireEquipmentId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_OrderAssemblyHireEquipments_HireEquipments_EquipmentId",
                table: "OrderAssemblyHireEquipments");

            migrationBuilder.DropIndex(
                name: "IX_OrderAssemblyHireEquipments_EquipmentId",
                table: "OrderAssemblyHireEquipments");

            migrationBuilder.AlterColumn<int>(
                name: "TotalInvoiced",
                table: "PurchaseOrderItems",
                type: "int",
                nullable: true,
                oldClrType: typeof(double),
                oldType: "float",
                oldNullable: true);
        }
    }
}
