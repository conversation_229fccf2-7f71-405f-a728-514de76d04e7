﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Red20.Model.Data.HireEquipment;
using Red20.Model.Data.Job;
using Red20.Model.Data.Order;
using Red20.Model.Data.OrderAssembly;
using Red20.Service.Data.Interface;

namespace Red20.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class OrderAssemblyController : ControllerBase {

        private IOrderAssemblyService service;
        private ILogger<AuthController> logger;
        private IOrderAssemblyHireEquipmentService orderAssemblyHireEquipmentService;
        private IHireEquipmentService equipmentService;
        private IUserService userService;
        private IOrderService orderService;
        private IJobService jobService;
        private IOrderAssemblyDayRateService dayRateService;

        public OrderAssemblyController(
            IOrderAssemblyService service,
            IOrderAssemblyHireEquipmentService orderAssemblyHireEquipmentService,
            IHireEquipmentService equipmentService,
            IOrderService orderService,
            ILogger<AuthController> logger,
            IUserService userService,
            IJobService jobService,
            IOrderAssemblyDayRateService dayRateService) {

            this.service = service;
            this.orderAssemblyHireEquipmentService = orderAssemblyHireEquipmentService;
            this.equipmentService = equipmentService;
            this.logger = logger;
            this.userService = userService;
            this.orderService = orderService;
            this.jobService = jobService;
            this.dayRateService = dayRateService;
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(OrderAssemblyModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id) {
            var assemblyItem = await service.GetAsync(id);
            return Ok(assemblyItem);
        }

        [HttpGet("byOrder/{id}")]
        [ProducesResponseType(200, Type = typeof(OrderAssemblyModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetByOrder(Guid id) {
            var assemblyItems = await service.GetByOrderAsync(id);
            return Ok(assemblyItems);
        }

        [HttpGet("allOrderAssemblies/{id}")]
        [ProducesResponseType(200, Type = typeof(OrderAssemblyModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAllOrderAssemblies(Guid id)
        {
            var orderItems = await service.GetByOrderWithDeliveryChargeAsync(id);
            return Ok(orderItems);
        }

        [HttpGet("byOrderForHPS/{id}")]
        [ProducesResponseType(200, Type = typeof(OrderAssemblyModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetByOrderForHPS(Guid id) {
            var assemblyItems = await service.GetByOrderAsync(id);
            assemblyItems = assemblyItems.Any() ? assemblyItems.Where(w => !w.IsSimpleAssembly).ToList() : null;
            return Ok(assemblyItems);
        }

        [HttpGet("byOrderForInvoice/{id}")]
        [ProducesResponseType(200, Type = typeof(OrderItemInvoiceModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetByOrderForInvoice(Guid id) {
            var orderAssemblies = await service.GetByOrderForInvoiceAsync(id);

            if (orderAssemblies.Any()) {
                orderAssemblies = orderAssemblies.OrderByDescending(o => !o.IsSimpleAssembly).ToList();
            }

            return Ok(orderAssemblies);
        }

        [HttpGet("byAssembly/{id}")]
        [ProducesResponseType(200, Type = typeof(OrderAssemblyHireEquipmentModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetEuipmentsByAssembly(Guid id) {
            var assemblyEquipments = await orderAssemblyHireEquipmentService.GetByOrderAssembly(id);
            return Ok(assemblyEquipments);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(OrderAssemblyModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody]OrderAssemblyUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }

            if (model.HireStart.HasValue) {
                model.HireStart = model.HireStart.HasValue ? model.HireStart.Value.AddHours(2) : (DateTime?)null;
            }
            if (model.HireEnd.HasValue) {
                model.HireEnd = model.HireEnd.HasValue ? model.HireEnd.Value.AddHours(2) : (DateTime?)null;
            }

            var assemblyItem = await service.PostAsync(model);

            return Ok(assemblyItem);
        }

        [HttpPut("receiveAssembly/{id}/{type:alpha}")]
        [ProducesResponseType(200, Type = typeof(OrderAssemblyModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post(Guid id, string type) {
            if (!await service.OrderAssemblyIdExistsAsync(id)) {
                return BadRequest();
            }

            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);
            var assembly = await service.GetAsync(id);
            var assemblyHireEquipments = await orderAssemblyHireEquipmentService.GetByOrderAssembly(id);
            var hireEquipments = new List<HireEquipmentModel>();

            if(type == "receive") {
                assembly.ReceivedBy = $"{user.Firstname} {user.Lastname}";
                assembly.ReceivedDate = DateTime.UtcNow;

                if(assemblyHireEquipments != null && assemblyHireEquipments.Any()) {
                    foreach(var assemblyHireEquipment in assemblyHireEquipments) {
                        var hireEquipment = await equipmentService.GetBySerialNumber(assemblyHireEquipment.Serial);
                        if(hireEquipment != null) {
                            hireEquipment.Status = "Inspection/Repair";
                            hireEquipment.OrderNumber = assembly.Order.Number;
                            hireEquipments.Add(hireEquipment);
                        }
                    }
                }
            } else if (type == "unReceive") {
                assembly.ReceivedBy = null;
                assembly.ReceivedDate = null;

                if (assemblyHireEquipments != null && assemblyHireEquipments.Any()) {
                    foreach (var assemblyHireEquipment in assemblyHireEquipments) {
                        var hireEquipment = await equipmentService.GetBySerialNumber(assemblyHireEquipment.Serial);
                        if (hireEquipment != null) {
                            hireEquipment.Status = "On hire";
                            hireEquipment.OrderNumber = assembly.Order.Number;
                            hireEquipments.Add(hireEquipment);
                        }
                    }
                }
            }

            if (hireEquipments.Any()) {
                await equipmentService.UpdateStatuses(hireEquipments);
            }

            assembly = await service.SetReceivedData(assembly);

            return Ok(assembly);
        }

        [HttpPut("receiveSelectedAssemblies/{id}")]
        [ProducesResponseType(200, Type = typeof(OrderAssemblyModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> ReceiveAllAssemblies(Guid id) {
            var assembly = await service.GetAsync(id);
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);

            if (assembly != null) {
                var assemblyHireEquipments = await orderAssemblyHireEquipmentService.GetByOrderAssembly(assembly.OrderAssemblyId);
                var hireEquipments = new List<HireEquipmentModel>();

                assembly.ReceivedBy = $"{user.Firstname} {user.Lastname}";
                assembly.ReceivedDate = DateTime.UtcNow;

                if (assemblyHireEquipments != null && assemblyHireEquipments.Any()) {
                    foreach (var assemblyHireEquipment in assemblyHireEquipments) {
                        var hireEquipment = await equipmentService.GetBySerialNumber(assemblyHireEquipment.Serial);
                        if (hireEquipment != null) {
                            hireEquipment.Status = "Inspection/Repair";
                            hireEquipment.OrderNumber = assembly.Order.Number;
                            hireEquipments.Add(hireEquipment);
                        }
                    }
                    if (hireEquipments.Any()) {
                        await equipmentService.UpdateStatuses(hireEquipments);
                    }
                }
                await service.SetReceivedData(assembly);
            }

            return Ok();
        }

        [HttpPut("putAssemblyEquipment/{id}")]
        [ProducesResponseType(200, Type = typeof(OrderAssemblyHireEquipmentModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> PutHireEquipment([FromRoute]Guid id, [FromBody]HireEquipmentModel model) {
            if(model is null) {
                return BadRequest();
            }
            var hireEquipment = await equipmentService.GetAsync(model.HireEquipmentId);
            if(hireEquipment == null) {
                return BadRequest();
            }

            var assembly = await service.GetAsync(id);
            if(assembly == null) {
                return BadRequest();
            }

            var assemblyEquipment = new OrderAssemblyHireEquipmentModel();
            assemblyEquipment.OrderAssemblyId = assembly.OrderAssemblyId;
            assemblyEquipment.Description = hireEquipment.Description;
            assemblyEquipment.Category = hireEquipment.Category;
            assemblyEquipment.Model = hireEquipment.Model;
            assemblyEquipment.Serial = hireEquipment.SerialNumber;
            assemblyEquipment.EquipmentId = hireEquipment.HireEquipmentId;

            try {
                //if (!await orderAssemblyHireEquipmentService.AssemblyEquipmentExists(assemblyEquipment.Serial)) { -> might need this check. To be seen later on
                    model.Status = "On hire";
                    model.OrderNumber = assembly.Order != null ? assembly.Order.Number : string.Empty;
                    hireEquipment = await equipmentService.PutAsync(hireEquipment.HireEquipmentId, model);
                    assemblyEquipment = await orderAssemblyHireEquipmentService.PostAsync(assemblyEquipment);
                    return Ok(assemblyEquipment);
            } catch (Exception ex) {
                logger.LogError($"Cannot create assembly equipment - {ex}");
                return BadRequest();
            }
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(OrderAssemblyModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Put(Guid id, [FromBody]OrderAssemblyUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }

            var assembly = await service.GetAsync(id);
            if (assembly is null) {
                return BadRequest();
            }

            try {
                if(model.HireStart != assembly.HireStart) {
                    model.HireStart = model.HireStart.HasValue ? model.HireStart.Value.AddHours(2) : (DateTime?)null;
                }
                if(model.HireEnd != assembly.HireEnd) {
                    model.HireEnd = model.HireEnd.HasValue ? model.HireEnd.Value.AddHours(2) : (DateTime?)null;
                }

                assembly = await service.PutAsync(id, model);

                return Ok(assembly);
            } catch (Exception ex) {
                logger.LogError($"Cannot update Order Assembly - {ex}");
                return BadRequest();
            }
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Delete(Guid id) {
            await service.DeleteAsync(id);
            return Ok();
        }

        [HttpDelete("deleteEquipment/{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> DeleteAssemblyEquipment(Guid id) {
            if(!await orderAssemblyHireEquipmentService.OrderAssemblyHireEquipmentIdExistsAsync(id)) {
                return BadRequest();
            }

            var assemblyEquipment = await orderAssemblyHireEquipmentService.GetAsync(id);
            var hireEquipment = await equipmentService.GetBySerialNumber(assemblyEquipment.Serial);
            if(hireEquipment == null) {
                return BadRequest();
            }

            hireEquipment.Status = "Available";
            hireEquipment.OrderNumber = null;
            await equipmentService.PutAsync(hireEquipment.HireEquipmentId, hireEquipment);

            await orderAssemblyHireEquipmentService.DeleteAsync(id);

            return Ok();
        }


        #region Order Assembly Day Rate

        [HttpGet("getOrderAssemblyDayRates/{id}")]
        [ProducesResponseType(200, Type = typeof(OrderAssemblyDayRateModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetOrderAssemblyDayRates(Guid id)
        {
            var dayRates = await dayRateService.GetByOrderAssemblyIdAsync(id);
            return Ok(dayRates);
        }

        [HttpDelete("deleteOrderAssemblyDayRate/{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> DeleteOrderAssemblyDayRate(Guid id)
        {
            if (!await dayRateService.OrderAssemblyDayRateExistsAsync(id))
            {
                return BadRequest();
            }

            await dayRateService.DeleteAsync(id);

            return Ok();
        }

        [HttpPost("createOrderAssemblyDayRate")]
        [ProducesResponseType(200, Type = typeof(OrderAssemblyDayRateModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> CreateOrderAssemblyDayRate([FromBody] OrderAssemblyDayRateUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }

            var orderAssembly = await service.GetAsync(model.OrderAssemblyId);
            if(orderAssembly == null)
            {
                return BadRequest();
            }

            if(await dayRateService.DatesExist(model))
            {
                return BadRequest($"From and To Date for a Day Rate can never overlap with the From and To dates from an existing Day Rate, for the same Order Assembly.");
            }

            if(orderAssembly.HireStart.HasValue && orderAssembly.HireStart.Value.Date > model.From.Date)
            {
                return BadRequest($"The From Date of the Day Rate cannot be before the Hire Start of the Assembly.");
            }

            var dayRate = await dayRateService.PostAsync(model);

            return Ok(dayRate);
        }

        [HttpPut("updateOrderAssemblyDayRate/{id}")]
        [ProducesResponseType(200, Type = typeof(OrderAssemblyDayRateModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> UpdateOrderAssemblyDayRate(Guid id, [FromBody] OrderAssemblyDayRateUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }

            var dayRate = await dayRateService.GetAsync(id);
            if (dayRate is null)
            {
                return BadRequest();
            }

            var orderAssembly = await service.GetAsync(model.OrderAssemblyId);
            if (orderAssembly == null)
            {
                return BadRequest();
            }

            if (await dayRateService.DatesExist(model))
            {
                return BadRequest($"From and To Date for a Day Rate can never overlap with the From and To dates from an existing Day Rate, for the same Order Assembly.");
            }

            if (orderAssembly.HireStart.HasValue && orderAssembly.HireStart.Value.Date > model.From.Date)
            {
                return BadRequest($"The From Date of the Day Rate cannot be before the Hire Start of the Assembly.");
            }

            try
            {
                dayRate = await dayRateService.PutAsync(id, model);

                return Ok(dayRate);
            }
            catch (Exception ex)
            {
                logger.LogError($"Cannot update Order Assembly Day Rate - {ex}");
                return BadRequest();
            }
        }

        #endregion
    }
}