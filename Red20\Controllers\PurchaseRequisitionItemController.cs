﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Red20.Model.Entity;
using Red20.Service.Data.Interface;
using System;
using System.Threading.Tasks;

namespace Red20.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class PurchaseRequisitionItemController : ControllerBase
    {

        private IPurchaseRequisitionService purchaseRequisitionService;
        private IPurchaseRequisitionItemService purchaseRequisitionItemService;
        private ILogger<AuthController> logger;
        IUnitOfWork unitOfWork;


        public PurchaseRequisitionItemController(
            IPurchaseRequisitionService purchaseRequisitionService,
            IPurchaseRequisitionItemService purchaseRequisitionItemService,
            IUnitOfWork unitOfWork,
            ILogger<AuthController> logger)
        {

            this.purchaseRequisitionService = purchaseRequisitionService;
            this.purchaseRequisitionItemService = purchaseRequisitionItemService;
            this.logger = logger;
            this.unitOfWork = unitOfWork;
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(PurchaseRequisitionItemModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id)
        {
            var purchaseRequisitionItem = await purchaseRequisitionItemService.GetByIdAsync(id);
            return Ok(purchaseRequisitionItem);
        }

        [HttpGet("byPurchaseRequisition/{id}")]
        [ProducesResponseType(200, Type = typeof(PurchaseRequisitionItemModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetByPurchaseRequisition(Guid id)
        {
            var purchaseRequisitionItems = await purchaseRequisitionItemService.GetByPurchaseRequisitionIdAsync(id);
            return Ok(purchaseRequisitionItems);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(PurchaseRequisitionItemModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody] PurchaseRequisitionItemUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }
            var purchaseRequisition = await purchaseRequisitionService.GetAsync(model.PurchaseRequisitionId);
            try
            {
                var purchaseRequisitionItem = await purchaseRequisitionItemService.PostAsync(model);
                return Ok(purchaseRequisitionItem);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot create Purchase Requisition - {ex}");
                return BadRequest();
            }
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(PurchaseRequisitionItemModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Put(Guid id, [FromBody] PurchaseRequisitionItemUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }
            var purchaseRequisitionItem = await purchaseRequisitionItemService.GetByIdAsync(id);
            if (purchaseRequisitionItem is null)
            {
                return BadRequest();
            }
            try
            {
                purchaseRequisitionItem = await purchaseRequisitionItemService.PutAsync(id, model);
                return Ok(purchaseRequisitionItem);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot update Purchase Requisition Item - {ex}");
                return BadRequest();
            }
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Delete(Guid id)
        {

            await purchaseRequisitionItemService.DeleteAsync(id);
            return Ok();
        }
    }
}