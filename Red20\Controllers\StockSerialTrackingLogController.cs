﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Red20.Model.Data.StockSerialTracking;
using Red20.Service.Data.Interface;

namespace Red20.Controllers {
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class StockSerialTrackingLogController : ControllerBase {
        private IStockSerialTrackingLogService stockSerialTrackingService;

        public StockSerialTrackingLogController(IStockSerialTrackingLogService stockSerialTrackingService) {
            this.stockSerialTrackingService = stockSerialTrackingService;
        }

        [HttpGet]
        [ProducesResponseType(200, Type = typeof(List<StockSerialTrackingLogModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get() {
            var stockSerialTrackingLogs = await stockSerialTrackingService.GetAsync();
            return Ok(stockSerialTrackingLogs);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(StockSerialTrackingLogModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id) {
            var stockSerialTracking = await stockSerialTrackingService.GetAsync(id);
            return Ok(stockSerialTracking);
        }

        [HttpGet("byStockSerialTrackingLog/{id}")]
        [ProducesResponseType(200, Type = typeof(List<StockSerialTrackingLogModel>))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetByStockSerialTrackingLogId(Guid id) {
            var stockSerialTrackingLogs = await stockSerialTrackingService.GetByStockSerialTrackingIdAsync(id);
            return Ok(stockSerialTrackingLogs);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(StockSerialTrackingLogModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody]StockSerialTrackingLogUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }

            var stockSerialTracking = await stockSerialTrackingService.PostAsync(model);
            return Ok(stockSerialTracking);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(StockSerialTrackingLogModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(404)]
        public async Task<IActionResult> Put(Guid id, [FromBody]StockSerialTrackingLogUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }

            if (await stockSerialTrackingService.GetAsync(id) is null) {
                return NotFound();
            }

            var stockSerialTracking = await stockSerialTrackingService.PutAsync(id, model);
            return Ok(stockSerialTracking);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Delete(Guid id) {
            if (await stockSerialTrackingService.GetAsync(id) is null) {
                return NotFound();
            }

            await stockSerialTrackingService.DeleteAsync(id);
            return Ok();
        }
    }
}
