﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Red20.Model.Data.Stock;
using Red20.Model.Data.StockAssembly;
using Red20.Service.Data.Interface;

namespace Red20.Controllers {
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class StockAssemblyController : ControllerBase {
        private IStockAssemblyService stockAssemblyService;

        public StockAssemblyController(IStockAssemblyService stockAssemblyService) {
            this.stockAssemblyService = stockAssemblyService;
        }

        [HttpGet]
        [ProducesResponseType(200, Type = typeof(List<StockAssemblyModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get() {
            var stockAssemblies = await stockAssemblyService.GetAsync();
            return Ok(stockAssemblies);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(StockAssemblyModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id) {
            var stockAssembly = await stockAssemblyService.GetAsync(id);
            return Ok(stockAssembly);
        }

        [HttpGet("by-stock-id/{id}")]
        [ProducesResponseType(200, Type = typeof(List<StockAssemblyModel>))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetByStockId(Guid id) {
            var stockAssemblies = await stockAssemblyService.GetByStockIdAsync(id);
            return Ok(stockAssemblies);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(StockAssemblyModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody]StockAssemblyUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }

            var stockAssembly = await stockAssemblyService.PostAsync(model);
            return Ok(stockAssembly);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(StockAssemblyModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(404)]
        public async Task<IActionResult> Put(Guid id, [FromBody]StockAssemblyUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }

            if (await stockAssemblyService.GetAsync(id) is null) {
                return NotFound();
            }

            var stockAssembly = await stockAssemblyService.PutAsync(id, model);
            return Ok(stockAssembly);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Delete(Guid id) {
            if (await stockAssemblyService.GetAsync(id) is null) {
                return NotFound();
            }

            await stockAssemblyService.DeleteAsync(id);
            return Ok();
        }

    }
}
