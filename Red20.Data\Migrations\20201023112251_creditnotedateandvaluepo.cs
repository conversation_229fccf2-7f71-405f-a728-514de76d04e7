﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace Red20.Data.Migrations
{
    public partial class creditnotedateandvaluepo : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "CreditedDate",
                table: "PurchaseOrderItemLogs",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "TotalCredited",
                table: "PurchaseOrderItemLogs",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CreditedDate",
                table: "PurchaseOrderItemLogs");

            migrationBuilder.DropColumn(
                name: "TotalCredited",
                table: "PurchaseOrderItemLogs");
        }
    }
}
