﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Red20.Data.Migrations
{
    public partial class StockAssemblyMovementJobNumber : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "<PERSON>N<PERSON><PERSON>",
                table: "StockAssemblyMovementItems");

            migrationBuilder.AddColumn<string>(
                name: "JobNum<PERSON>",
                table: "StockAssemblyMovements",
                type: "nvarchar(max)",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "<PERSON>Num<PERSON>",
                table: "StockAssemblyMovements");

            migrationBuilder.AddColumn<string>(
                name: "<PERSON>N<PERSON>ber",
                table: "StockAssemblyMovementItems",
                type: "nvarchar(max)",
                nullable: true);
        }
    }
}
