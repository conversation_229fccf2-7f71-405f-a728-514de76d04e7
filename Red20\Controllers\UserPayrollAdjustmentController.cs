﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Red20.Excel.Export;
using Red20.Model.Data;
using Red20.Model.Data.Document;
using Red20.Model.Data.User;
using Red20.Service.Data.Interface;
using Red20.Service.Storage.Interface;
using Red20.Settings;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Red20.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class UserPayrollAdjustmentController : ControllerBase
    {

        private IUserPayrollAdjustmentService service;
        private IUserPayrollAdjustmentItemService userPayrollAdjustmentItemService;
        private IUserService userService;
        private IUserPayrollRateService userPayrollRateService;
        private ILogger<AuthController> logger;
        private ClientSettings settings;
        private IUnitOfWork unitOfWork;
        private IDocumentService documentService;
        private IUserTimesheetService timesheetService;
        private IStorageService blobStorage;

        public UserPayrollAdjustmentController(
            IUserPayrollAdjustmentService service,
            IUserPayrollAdjustmentItemService userPayrollAdjustmentItemService,
            IUserPayrollRateService userPayrollRateService,
            IUserTimesheetService timesheetService,
            IStorageService blobStorage,
            IDocumentService documentService,
            IUserService userService,
            IUnitOfWork unitOfWork,
            ILogger<AuthController> logger)
        {
            this.service = service;
            this.userService = userService;
            this.userPayrollAdjustmentItemService = userPayrollAdjustmentItemService;
            this.logger = logger;
            this.unitOfWork = unitOfWork;
            this.documentService = documentService;
            this.blobStorage = blobStorage;
            this.userPayrollRateService = userPayrollRateService;
            this.timesheetService = timesheetService;
        }

        #region Payroll Adjustments

        [HttpGet]
        [ProducesResponseType(200, Type = typeof(IList<UserPayrollAdjustmentModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get()
        {
            var userPayrollAdjustments = await service.GetAsync();
            var nonArchived = userPayrollAdjustments.Where(c => !c.IsArchived).OrderByDescending(c => c.Created).ToList();
            return Ok(nonArchived);
        }

        [HttpGet("archived")]
        [ProducesResponseType(200, Type = typeof(IList<UserPayrollAdjustmentModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetArchived()
        {
            var userPayrollAdjustments = await service.GetArchivedAsync();
            return Ok(userPayrollAdjustments);
        }


        [HttpGet("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id)
        {

            var userPayrollAdjustment = await service.GetByIdAsync(id);

            return Ok(userPayrollAdjustment);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(UserPayrollAdjustmentModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody] UserPayrollAdjustmentUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }

            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);

            model.CreatedBy = $"{user.Firstname} {user.Lastname}";
            model.Created = DateTime.Now;
            model.CreatedMonth = DateTime.Now.ToString("MMMM");
            model.CreatedYear = DateTime.Now.ToString("yyyy");

            var userPayrollAdjustment = await service.PostAsync(model);
            return Ok(userPayrollAdjustment);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(UserPayrollAdjustmentModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Put(Guid id, [FromBody] UserPayrollAdjustmentUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);

            var userPayrollAdjustment = await service.PutAsync(id, model);
            return Ok(userPayrollAdjustment);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Delete(Guid id)
        {
            await service.DeleteAsync(id);
            return Ok();
        }

        #endregion

        #region Payroll Adjustment Items

        [HttpGet("payrollAdjustment/{id}")]
        [ProducesResponseType(200, Type = typeof(UserPayrollAdjustmentItemModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetItemsByPayrollAdjustment(Guid id)
        {
            var items = await userPayrollAdjustmentItemService.GetByUserPayrollAdjustmentIdAsync(id);
            return Ok(items);
        }

        [HttpGet("payrollAmount/{id}/{hours}/{payType}")]
        [ProducesResponseType(200, Type = typeof(UserPayrollAdjustmentItemModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetUserPayrollAmount(Guid id, double hours, string payType)
        {
            var amount = 0.0;
            var userPayrollRate = new UserPayrollRateModel();
            var userPayrollRates = await userPayrollRateService.GetByUserIdAsync(id);
            userPayrollRate = payType == "Hourly Rate 1" ?
               userPayrollRates.Any() && userPayrollRates.Any(c => c.PayType == "Hourly Rate") && payType == "Hourly Rate 1" ? userPayrollRates.Where(c => c.PayType == "Hourly Rate").FirstOrDefault() : null :
               payType == "Overtime 1" ? userPayrollRates.Any() && userPayrollRates.Any(c => c.PayType == "Overtime 1") && payType == "Overtime 1" ? userPayrollRates.Where(c => c.PayType == "Overtime 1").FirstOrDefault() : null :
               userPayrollRates.Any() && userPayrollRates.Any(c => c.PayType == "Overtime 2") && payType == "Overtime 2" ? userPayrollRates.Where(c => c.PayType == "Overtime 2").FirstOrDefault() : null;

            if (userPayrollRate != null)
            {
                amount = hours * userPayrollRate.OriginalRate.Value;
                return Ok(amount);
            } else
            {
                return Ok();
            }
        }

        [HttpGet("payrollTypeAmount/{id}/{payType}")]
        [ProducesResponseType(200, Type = typeof(UserPayrollAdjustmentItemModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetUserPayrollTypeAmount(Guid id, string payType)
        {
            var amount = 0.0;
            var userPayrollRates = await userPayrollRateService.GetByUserIdAsync(id);
            var rate = userPayrollRates.Any() && userPayrollRates.Any(c => c.PayType == "Hourly Rate") && payType == "Hourly Rate 1" ? userPayrollRates.Where(c => c.PayType == "Hourly Rate").FirstOrDefault() : null;
            if (rate != null)
            {
                amount = rate.OriginalRate.Value;
                return Ok(amount);
            } else
            {
                return Ok();
            }
        }

        [HttpPost("createUserPayrollAdjustmentItem")]
        [ProducesResponseType(200, Type = typeof(UserPayrollAdjustmentItemModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> CreateEntry([FromBody] UserPayrollAdjustmentItemUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }

            var userPayrollAdjustmentItem = await userPayrollAdjustmentItemService.PostAsync(model);
            return Ok(userPayrollAdjustmentItem);
        }

        [HttpPut("editPayrollAdjustmentItem/{id}")]
        [ProducesResponseType(200, Type = typeof(UserPayrollAdjustmentItemModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> EditEntry(Guid id, [FromBody] UserPayrollAdjustmentItemUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }

            var userPayrollAdjustmentItem = await userPayrollAdjustmentItemService.GetByIdAsync(id);

            if (userPayrollAdjustmentItem is null)
            {
                return BadRequest();
            }

            try
            {
                await userPayrollAdjustmentItemService.PutAsync(id, model);
                return Ok();
            } catch (Exception ex)
            {
                return BadRequest($"{ex}");
            }
        }

        [HttpDelete("deleteEntry/{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> DeleteEntry(Guid id)
        {

            await userPayrollAdjustmentItemService.DeleteAsync(id);
            return Ok();
        }

        [HttpGet("printPayrollSummary/{from}/{to}")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> PrintPayrollRateSummary(DateTime from, DateTime to)
        {
            try
            {
                var document = new DocumentModel();
                var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
                if (emailClaim == null)
                {
                    return Unauthorized();
                }

                var user = await userService.GetUserByEmailAsync(emailClaim.Value);
                var currentUser = $"{user.Firstname} {user.Lastname}";
                var userTimesheetEntries = unitOfWork.UserTimesheetEntry.Query(c => c.Created.Date >= from.Date && c.Created <= to.Date && c.CompletedDate.HasValue).Include(c => c.UserTimesheet).ToList();

                var userTimesheetEntryUserIds = userTimesheetEntries.Select(s => s.UserTimesheet.UserId).ToList();

                var userPayrollAdjustmentItems = unitOfWork.UserPayrollAdjustmentItem.Query(c => c.UserPayrollAdjustment.AdjustmentDate.Value.Date >= from.Date && c.UserPayrollAdjustment.AdjustmentDate.Value <= to.Date).Include(c => c.UserPayrollAdjustment).ToList();

                var currentMonthPayrollAdjustmentItems = unitOfWork.UserPayrollAdjustmentItem.Query(c => c.UserPayrollAdjustment.AdjustmentDate.Value.Month == to.Month && c.UserPayrollAdjustment.AdjustmentDate.Value.Year == to.Year).Include(c => c.UserPayrollAdjustment).Include(c => c.User).ToList();

                var userPayrollAdjustmentItemsUserIds = userPayrollAdjustmentItems.Select(c => c.UserId).ToList();

                var userIds = userTimesheetEntryUserIds.Concat(userPayrollAdjustmentItemsUserIds.Where(c => !userTimesheetEntryUserIds.Contains(c))).ToList();

                var newUsers = unitOfWork.User.Query(c => userIds.Contains(c.UserId)).Include(c => c.UserPayrollRates).ToList();

                var itemData = ExportUtility.ExportPayrollReport(newUsers, userTimesheetEntries, userPayrollAdjustmentItems, currentUser, currentMonthPayrollAdjustmentItems);

                document = await documentService.PostAsync("User_Month_End_Report.xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"{user.Firstname} {user.Lastname}", "Users", null, null, null, false, false, false);
                var file = File(itemData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                await blobStorage.UploadAsync(document.DocumentId, itemData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                return file;
            } catch (Exception ex)
            {
                return NotFound();
            }
        }

        #endregion
    }
}