﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Red20.Excel.Export;
using Red20.Model.Data.Quote;
using Red20.Model.Entity;
using Red20.Service;
using Red20.Service.Data.Interface;
using Red20.Service.Email.Interface;
using Red20.Service.Storage.Interface;
using Red20.Utility;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Red20.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class QuoteController : ControllerBase
    {

        private IQuoteService quoteService;
        private IQuoteItemService quoteItemService;
        private IOrderService orderService;
        private IEnquiryService enquiryService;
        private IUserService userService;
        private IStorageService blobStorage;
        private ILogger<AuthController> logger;
        private IDocumentService documentService;
        private IEmailService emailService;
        private ReportService reportService;
        IUnitOfWork unitOfWork;

        public QuoteController(
            IQuoteService quoteService,
            IQuoteItemService quoteItemService,
            IOrderService orderService,
            IEnquiryService enquiryService,
            IUserService userService,
            IUnitOfWork unitOfWork,
            IStorageService blobStorage,
            IDocumentService documentService,
            IEmailService emailService,
            ReportService reportService,
        ILogger<AuthController> logger)
        {

            this.quoteService = quoteService;
            this.quoteItemService = quoteItemService;
            this.orderService = orderService;
            this.enquiryService = enquiryService;
            this.userService = userService;
            this.unitOfWork = unitOfWork;
            this.blobStorage = blobStorage;
            this.documentService = documentService;
            this.emailService = emailService;
            this.reportService = reportService;
            this.logger = logger;
        }

        [HttpGet]
        [ProducesResponseType(200, Type = typeof(PaginatedResult<QuoteGridModel>))]
        [ProducesResponseType(401)]
        public IActionResult Get([FromQuery] QuoteFilterParams filterParams)
        {
            var paginatedQuotes = quoteService.GetPaginatedQuotes(filterParams);
            return Ok(paginatedQuotes);
        }

        [HttpGet("archivedQuotes")]
        [ProducesResponseType(200, Type = typeof(IList<QuoteGridModel>))]
        [ProducesResponseType(401)]
        public IActionResult GetArchivedQuotes()
        {
            var archivedQuotes = quoteService.GetArchivedQuotes();
            return Ok(archivedQuotes);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(QuoteModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id)
        {

            var quote = await quoteService.GetAsync(id);

            return Ok(quote);
        }

        [HttpGet("byEnquiry/{id}")]
        [ProducesResponseType(200, Type = typeof(IList<QuoteModel>))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetByEnquiry(Guid id)
        {

            var quotes = await quoteService.GetByEnquiryAsync(id);

            return Ok(quotes.ToList());
        }

        [HttpGet("getEnquiry/{id}")]
        [ProducesResponseType(200, Type = typeof(string))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetEnquiry(Guid id)
        {
            var quote = await quoteService.GetAsync(id);
            var enquiryNumber = await enquiryService.GetEnquiryNumber(quote.EnquiryId);
            return Ok(enquiryNumber);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(QuoteModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody] QuoteUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }

            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);

            if (user != null)
            {
                model.CreatedBy = $"{user.Firstname} {user.Lastname}";
            }

            if (model.ReminderDate.HasValue)
            {
                model.ReminderDate = null;
            }

            var enquiry = await enquiryService.GetAsync(model.EnquiryId);
            model.Number = await NumberSequenceUtility.GetNextQuoteNumber(unitOfWork, enquiry.Number);

            model.AccountCode = enquiry.AccountCode;
            if (model.IsClone)
            {
                model.AccountCode = null;
            }
            model.Validity = 30;
            try
            {
                var quote = await quoteService.PostAsync(model, user);

                if (model.IsClone && !string.IsNullOrWhiteSpace(model.OriginalQuoteId))
                {
                    var originalQuoteId = Guid.Parse(model.OriginalQuoteId);

                    if (originalQuoteId.GetType() == typeof(Guid))
                    {
                        var quoteItems = await quoteItemService.GetByQuoteIdAsync(originalQuoteId);

                        if (quoteItems != null && quoteItems.Any())
                        {
                            await quoteItemService.CloneItems(quoteItems.ToList(), quote.QuoteId);

                            var description = quoteItems.Select(item => item.Description).FirstOrDefault();

                            var description_a = description.Replace("<p><br></p>", "<p></p>");

                            var splitDescription = description_a.Split("</p>");

                            var firstLineDescription = $"{splitDescription[0].Replace("<p>", "").Replace("</p>", "").Replace("<em>", "").Replace("</em>", "").Replace("<strong>", "").Replace("</strong>", "").Replace("<ul>", "").Replace("</ul>", "").Replace("<li>", "").Replace("</li>", "").Replace("<u>", "").Replace("</u>", "")}";

                            double? hireItemsValue = 0.0;

                            double? salesItemsValue = 0.0;

                            double discount = 0.0;

                            double totalValue = 0.0;

                            double? collectionChargeValue = quote.Type == "Hire" && quote.CollectionCharge.HasValue ? quote.CollectionCharge.Value : (double?)null;

                            double? deliveryCharge = quote.DeliveryCharge.HasValue ? quote.DeliveryCharge.Value : (double?)null;

                            foreach (var item in quoteItems)
                            {
                                if (quote.Type == "Hire")
                                {
                                    if (quote.HirePeriod.HasValue)
                                    {
                                        var value = item.UnitPrice * item.Quantity * quote.HirePeriod.Value;

                                        discount = item.Discount.HasValue ? value * (item.Discount.Value / 100) : 0;

                                        hireItemsValue += value - discount;
                                    }
                                } else
                                {
                                    deliveryCharge = quote.DeliveryCharge.HasValue ? quote.DeliveryCharge.Value : (double?)null;

                                    var value = item.UnitPrice * item.Quantity;

                                    discount = item.Discount.HasValue ? value * (item.Discount.Value / 100) : 0;

                                    salesItemsValue += value - discount;
                                }
                            }

                            if (hireItemsValue.HasValue && hireItemsValue.Value > 0)
                            {
                                totalValue = totalValue + hireItemsValue.Value;
                            } else if (salesItemsValue.HasValue && salesItemsValue.Value > 0)
                            {
                                totalValue = totalValue + salesItemsValue.Value;
                            }
                            if (collectionChargeValue.HasValue)
                            {
                                totalValue = totalValue + collectionChargeValue.Value;
                            }
                            if (deliveryCharge.HasValue)
                            {
                                totalValue = totalValue + deliveryCharge.Value;
                            }

                            var newQuote = await unitOfWork.Quote.GetAsync(quote.QuoteId);

                            newQuote.ItemDescription = firstLineDescription;

                            newQuote.QuoteValue = totalValue;

                            unitOfWork.Quote.Update(newQuote);
                        }

                        await unitOfWork.SaveChangesAsync();
                    }
                }

                return Ok(quote);

            } catch (Exception ex)
            {
                logger.LogError($"Cannot create Quote - {ex}");
                return BadRequest();
            }
        }

        [HttpPost("sendAttachments")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> SendAttachments(QuoteUpdateModel model)
        {
            try
            {
                List<Tuple<string, string, byte[]>> attachments = new List<Tuple<string, string, byte[]>>();

                var firstName = "";
                var lastName = "";

                var quote = await quoteService.GetByNumberAsync(model.Number);

                var createdBy = quote.CreatedBy;

                string[] createdBySplit = createdBy.Split(" ");
                if (createdBySplit.Length == 2)
                {
                    firstName = !string.IsNullOrWhiteSpace(quote.CreatedBy) ? quote.CreatedBy.Split(" ")[0] : string.Empty;
                    lastName = !string.IsNullOrWhiteSpace(quote.CreatedBy) ? quote.CreatedBy.Split(" ")[1] : string.Empty;
                } else if (createdBySplit.Length == 3)
                {
                    firstName = !string.IsNullOrWhiteSpace(quote.CreatedBy) ? $"{quote.CreatedBy.Split(" ")[0]} {quote.CreatedBy.Split(" ")[1]}" : string.Empty;
                    lastName = !string.IsNullOrWhiteSpace(quote.CreatedBy) ? quote.CreatedBy.Split(" ")[2] : string.Empty;
                }

                var user = await userService.GetByNameAsync(firstName, lastName);

                var quoteReportModel = await quoteService.GetQuoteReportModel(quote.QuoteId, $"{user.Firstname} {user.Lastname}", user.EmailAddress);

                var stream = reportService.GetReport(quoteReportModel, null, quoteReportModel.IsIssued is false, quoteReportModel.Type == "Hire");

                var quotePdfContentType = "application/pdf";
                var quotePdf = new byte[] { };
                if (stream.Length > 0)
                {
                    using (var memoryStream = new MemoryStream())
                    {
                        stream.CopyTo(memoryStream);
                        quotePdf = memoryStream.ToArray();
                    }
                    attachments.Add(new Tuple<string, string, byte[]>(quotePdfContentType, $"{quote.Number}.pdf", quotePdf));
                }

                if (model.AttachmentIds != null && model.AttachmentIds.Any())
                {
                    foreach (var attachmentId in model.AttachmentIds)
                    {
                        bool isGuid = Guid.TryParse(attachmentId, out Guid documentId);
                        if (isGuid && await blobStorage.ExistsAsync(documentId))
                        {
                            var blob = await blobStorage.DownloadAsync(documentId);
                            var document = await documentService.GetByIdAsync(documentId);
                            string fileName = document.FileName;
                            attachments.Add(new Tuple<string, string, byte[]>(blob.Item2, fileName, blob.Item1));
                        }
                    }
                }

                await emailService.SendAttachments(
                    quote.EmailBody.Replace("\n", "<br/>").Replace("<p></p>", "<br/>").Replace("<p>", "<div class='row'>").Replace("</p>", "</div>"),
                    quote.EmailSubject,
                    quote.ContactEmailAddress,
                    attachments);

                return Ok();
            } catch (Exception ex)
            {
                logger.LogError($"Cannot send Quote Attachments - {ex}");
                return BadRequest();
            }
        }

        [HttpPost("reviseQuote")]
        [ProducesResponseType(200, Type = typeof(QuoteModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> ReviseQuote([FromBody] QuoteUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }

            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);

            if (user != null)
            {
                model.CreatedBy = $"{user.Firstname} {user.Lastname}";
            }

            if (model.IsRevisionQuote)
            {
                var enquiry = await enquiryService.GetAsync(model.EnquiryId);
                try
                {
                    model.Number = await NumberSequenceUtility.GetNextRevisedQuoteNumber(
                    unitOfWork,
                    enquiry.Number,
                    model.Number,
                    Guid.Parse(model.OriginalQuoteId));
                } catch (Exception ex)
                {
                    logger.LogError($"Exception while generating revised quote unique number - {ex}");
                    return BadRequest(ex);
                }
            }

            bool numberExists = await quoteService.QuoteNumberExistsAsync(model.Number);

            try
            {
                if (!numberExists)
                {
                    var quote = await quoteService.PostAsync(model, user);
                    var originalQuoteId = Guid.Parse(model.OriginalQuoteId);
                    if (originalQuoteId.GetType() == typeof(Guid))
                    {

                        var originalQuoteDocuments = await documentService.GetQuoteAttachmentsAsync(originalQuoteId);
                        var internalDocuments = originalQuoteDocuments.Where(c => c.IsQuoteInternal).ToList();
                        var nonInternalDocuments = originalQuoteDocuments.Where(c => !c.IsQuoteInternal).ToList();

                        if (nonInternalDocuments != null && nonInternalDocuments.Any())
                        {
                            foreach (var document in nonInternalDocuments)
                            {
                                var newDocument = await documentService.PostAsync(
                                    document.FileName,
                                    document.MimeType,
                                    document.CreatedBy,
                                    document.Type,
                                    quote.QuoteId,
                                    null,
                                    null,
                                    true,
                                    false,
                                    false,
                                    null,
                                    false);

                                var download = await blobStorage.DownloadAsync(document.DocumentId);
                                await blobStorage.UploadAsync(newDocument.DocumentId, download.Item1, download.Item2);
                            }

                            await unitOfWork.SaveChangesAsync();
                        }
                        if (internalDocuments != null && internalDocuments.Any())
                        {
                            foreach (var document in internalDocuments)
                            {
                                var newDocument = await documentService.PostAsync(
                                    document.FileName,
                                    document.MimeType,
                                    document.CreatedBy,
                                    document.Type,
                                    quote.QuoteId,
                                    null,
                                    null,
                                    true,
                                    false,
                                    false,
                                    null,
                                    true);

                                var download = await blobStorage.DownloadAsync(document.DocumentId);
                                await blobStorage.UploadAsync(newDocument.DocumentId, download.Item1, download.Item2);
                            }

                            await unitOfWork.SaveChangesAsync();
                        }

                        var quoteItems = await quoteItemService.GetByQuoteIdAsync(originalQuoteId);
                        if (quoteItems != null && quoteItems.Any())
                        {
                            await quoteItemService.CloneItems(quoteItems.ToList(), quote.QuoteId);


                            double totalValue = 0.0;

                            double? itemsValue = 0.0;

                            double discount = 0.0;

                            string firstLineDescription = "";

                            double? collectionChargeValue = quote.Type == "Hire" && quote.CollectionCharge.HasValue ? quote.CollectionCharge.Value : (double?)null;

                            double? deliveryCharge = quote.DeliveryCharge.HasValue ? quote.DeliveryCharge.Value : (double?)null;

                            var description = quoteItems.Select(item => item.Description).FirstOrDefault();

                            var description_a = description.Replace("<p><br></p>", "<p></p>");

                            var splitDescription = description_a.Split("</p>");

                            firstLineDescription = $"{splitDescription[0].Replace("<p>", "").Replace("</p>", "").Replace("<em>", "").Replace("</em>", "").Replace("<strong>", "").Replace("</strong>", "").Replace("<ul>", ".").Replace("</ul>", "").Replace("<li>", "").Replace("</li>", "").Replace("<u>", "").Replace("</u>", "")}";

                            foreach (var quoteItem in quoteItems)
                            {
                                if (quote.Type == "Hire")
                                {
                                    if (quote.HirePeriod.HasValue)
                                    {
                                        var value = quoteItem.UnitPrice * quoteItem.Quantity * quote.HirePeriod.Value;

                                        discount = quoteItem.Discount.HasValue ? value * (quoteItem.Discount.Value / 100) : 0;

                                        itemsValue = quoteItem.UnitPrice * quoteItem.Quantity * quote.HirePeriod.Value - discount;
                                    }
                                } else
                                {
                                    deliveryCharge = quote.DeliveryCharge.HasValue ? quote.DeliveryCharge.Value : (double?)null;

                                    var value = quoteItem.UnitPrice * quoteItem.Quantity;

                                    discount = quoteItem.Discount.HasValue ? value * (quoteItem.Discount.Value / 100) : 0;

                                    itemsValue = quoteItem.UnitPrice * quoteItem.Quantity - discount;
                                }
                                if (itemsValue.HasValue)
                                {
                                    totalValue = totalValue + itemsValue.Value;
                                }

                                if (collectionChargeValue.HasValue)
                                {
                                    totalValue = totalValue + collectionChargeValue.Value;
                                }

                                if (deliveryCharge.HasValue)
                                {
                                    totalValue = totalValue + deliveryCharge.Value;
                                }
                            }

                            var newQuote = await unitOfWork.Quote.GetAsync(quote.QuoteId);

                            newQuote.QuoteValue = totalValue;

                            newQuote.ItemDescription = firstLineDescription;

                            unitOfWork.Quote.Update(newQuote);

                            await unitOfWork.SaveChangesAsync();
                        }
                    }
                    return Ok(quote);
                } else
                {
                    return BadRequest();
                }
            } catch (Exception ex)
            {
                logger.LogError($"Cannot create Quote Revision - {ex}");
                return BadRequest();
            }
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(QuoteModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Put(Guid id, [FromBody] QuoteUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }

            model.EmailBody = !string.IsNullOrWhiteSpace(model.EmailBody) ? model.EmailBody.Replace("<p><br></p>", "<p></p>") : string.Empty;

            var quote = await quoteService.GetAsync(id);

            if (model.IsClosed && !quote.IsClosed)
            {
                model.ClosedDate = DateTime.UtcNow;
            }

            if (quote is null)
            {
                return BadRequest();
            }

            if (!string.IsNullOrWhiteSpace(quote.AccountCode) && string.IsNullOrWhiteSpace(model.AccountCode))
            {
                model.AccountCode = quote.AccountCode;
            }

            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);
            quote = await quoteService.PutAsync(id, model, user);

            return Ok(quote);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Delete(Guid id)
        {

            await quoteService.DeleteAsync(id);
            return Ok();
        }

        [HttpPut("closeQuote/{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Closed(Guid id)
        {

            var quote = await unitOfWork.Quote.GetAsync(id);

            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);

            quote.IsClosed = true;
            quote.ClosedDate = DateTime.UtcNow;

            unitOfWork.Quote.Update(quote);

            await unitOfWork.SaveChangesAsync();

            return Ok();
        }

        [HttpPost("quoteExport")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Export([FromBody] QuoteExportIdsModel model)
        {
            try
            {
                var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
                if (emailClaim == null)
                {
                    return Unauthorized();
                }

                var user = await userService.GetUserByEmailAsync(emailClaim.Value);
                var currentUser = $"{user.Firstname} {user.Lastname}";

                if (model != null && model.Ids != null && model.Ids.Any())
                {
                    var quotes = await quoteService.GetAllQuotesByIds(model.Ids);
                    byte[] data = ExportUtility.ExportQuotes(quotes.ToList(), currentUser);
                    var file = File(data, "application/vnd.ms-excel");
                    return file;
                }

                return Ok();
            } catch (Exception ex)
            {
                return BadRequest($"{ex}");
            }
        }

        [HttpPost("updateQuote")]
        [ProducesResponseType(200)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> UpdateQuotes()
        {
            try
            {
                var quotes = await unitOfWork.Quote.Query().Where(c => !c.ArchivedDate.HasValue).Include(c => c.QuoteItems).ToListAsync();

                var description = "";

                var newQuotes = new List<Quote>();

                foreach (var quote in quotes)
                {
                    var customer = unitOfWork.Customer.GetAsync(quote.CustomerId).Result;
                    var companyName = customer.Name;
                    var customerCountry = customer.PrimaryInvoiceAddressCountry;

                    List<QuoteItem> quoteItems = quote.QuoteItems.ToList();

                    if (quoteItems.Any())
                    {
                        var quoteItemDescription = quoteItems[0].Description;

                        double totalValue = 0.0;

                        double? hireItemsValue = 0.0;

                        double? salesItemsValue = 0.0;

                        double discount = 0.0;

                        double? collectionChargeValue = quote.Type == "Hire" && quote.CollectionCharge.HasValue ? quote.CollectionCharge.Value : (double?)null;

                        double? deliveryCharge = quote.DeliveryCharge.HasValue ? quote.DeliveryCharge.Value : (double?)null;

                        foreach (var quoteItem in quoteItems)
                        {
                            if (quote.Type == "Hire")
                            {
                                if (quote.HirePeriod.HasValue)
                                {
                                    var value = quoteItem.UnitPrice * quoteItem.Quantity * quote.HirePeriod.Value;

                                    discount = quoteItem.Discount.HasValue ? value * (quoteItem.Discount.Value / 100) : 0;

                                    hireItemsValue = +quoteItem.UnitPrice * quoteItem.Quantity * quote.HirePeriod.Value - discount;
                                }
                            } else
                            {
                                deliveryCharge = quote.DeliveryCharge.HasValue ? quote.DeliveryCharge.Value : (double?)null;

                                var value = quoteItem.UnitPrice * quoteItem.Quantity;

                                discount = quoteItem.Discount.HasValue ? value * (quoteItem.Discount.Value / 100) : 0;

                                salesItemsValue = +quoteItem.UnitPrice * quoteItem.Quantity - discount;
                            }
                        }

                        if (hireItemsValue.HasValue && hireItemsValue.Value > 0)
                        {
                            totalValue = totalValue + hireItemsValue.Value;
                        } else if (salesItemsValue.HasValue && salesItemsValue.Value > 0)
                        {
                            totalValue = totalValue + salesItemsValue.Value;
                        }
                        if (collectionChargeValue.HasValue)
                        {
                            totalValue = totalValue + collectionChargeValue.Value;
                        }
                        if (deliveryCharge.HasValue)
                        {
                            totalValue = totalValue + deliveryCharge.Value;
                        }

                        if (quoteItemDescription != null)
                        {
                            var splitDescription = quoteItemDescription.Split("</p>");

                            description = $"{splitDescription[0].Replace("<p>", "").Replace("</p>", "").Replace("<em>", "").Replace("</em>", "").Replace("<strong>", "").Replace("</strong>", "").Replace("<ul>", "").Replace("</ul>", "").Replace("<li>", ".").Replace("</li>", ".").Replace("<u>", ".").Replace("</u>", ".")}";
                        }

                        quote.ItemDescription = description;

                        quote.QuoteValue = totalValue;
                    }
                    quote.CompanyName = companyName;

                    quote.CompanyCountryName = customerCountry;

                    newQuotes.Add(quote);

                    //unitOfWork.Quote.Update(quote);

                    //await unitOfWork.SaveChangesAsync();
                }

                await unitOfWork.Quote.BulkUpdate(newQuotes);

                await unitOfWork.SaveChangesAsync();

                return Ok();
            } catch (Exception ex)
            {
                return BadRequest($"{ex}");
            }
        }
    }
}