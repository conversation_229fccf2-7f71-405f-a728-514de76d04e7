﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="Content\Templates\CustomerComplaintPdf.docx" />
    <None Remove="Content\Templates\HireDeliveryNoteCustomerCopy.docx" />
    <None Remove="Content\Templates\HireDeliveryNoteCustomerCopyTwoPages.docx" />
    <None Remove="Content\Templates\HireDeliveryNoteOfficeCopy.docx" />
    <None Remove="Content\Templates\HireDeliveryNoteOfficeCopyTwoPages.docx" />
    <None Remove="Content\Templates\HireOrder.docx" />
    <None Remove="Content\Templates\HirePrepSheetAirHoists.docx" />
    <None Remove="Content\Templates\HirePrepSheetLoadCell.docx" />
    <None Remove="Content\Templates\InternalSaleOrder.docx" />
    <None Remove="Content\Templates\JobCosting.docx" />
    <None Remove="Content\Templates\ProformaSaleOrder.docx" />
    <None Remove="Content\Templates\PurchaseOrderOfficePdf.docx" />
    <None Remove="Content\Templates\PurchaseOrderPdf.docx" />
    <None Remove="Content\Templates\PurchaseOrderWarehousePdf.docx" />
    <None Remove="Content\Templates\PurchaseRequisitionPdf.docx" />
    <None Remove="Content\Templates\QuotePreview.docx" />
    <None Remove="Content\Templates\QuotePreviewSale.docx" />
    <None Remove="Content\Templates\SaleDeliveryNote.docx" />
    <None Remove="Content\Templates\SaleDeliveryNoteTwoPages.docx" />
    <None Remove="Content\Templates\SaleOrder.docx" />
    <None Remove="Content\Templates\SalesJobRecordSheet.docx" />
    <None Remove="Content\Templates\SalesJobRecordSheetNoStockItems.docx" />
    <None Remove="Content\Templates\SalesOrder.docx" />
    <None Remove="Content\Templates\SalesWorkSiteSheet.docx" />
    <None Remove="Content\Templates\SupplierComplaintPdf.docx" />
    <None Remove="Content\Templates\WorksJobRecordSheetLC.docx" />
    <None Remove="Content\Templates\~%24oforma.docx" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Content\Templates\CustomerComplaintPdf.docx" />
    <EmbeddedResource Include="Content\Templates\HireDeliveryNoteCustomerCopy.docx" />
    <EmbeddedResource Include="Content\Templates\HireDeliveryNoteCustomerCopyTwoPages.docx" />
    <EmbeddedResource Include="Content\Templates\HireDeliveryNoteOfficeCopy.docx" />
    <EmbeddedResource Include="Content\Templates\HireDeliveryNoteOfficeCopyTwoPages.docx" />
    <EmbeddedResource Include="Content\Templates\HireOrder.docx" />
    <EmbeddedResource Include="Content\Templates\HirePrepSheetAirHoists.docx" />
    <EmbeddedResource Include="Content\Templates\HirePrepSheetLoadCell.docx" />
    <EmbeddedResource Include="Content\Templates\InternalSaleOrder.docx" />
    <EmbeddedResource Include="Content\Templates\JobCosting.docx" />
    <EmbeddedResource Include="Content\Templates\ProformaSaleOrder.docx" />
    <EmbeddedResource Include="Content\Templates\PurchaseOrderOfficePdf.docx" />
    <EmbeddedResource Include="Content\Templates\PurchaseOrderPdf.docx" />
    <EmbeddedResource Include="Content\Templates\PurchaseOrderWarehousePdf.docx" />
    <EmbeddedResource Include="Content\Templates\PurchaseRequisitionPdf.docx" />
    <EmbeddedResource Include="Content\Templates\QuotePreview.docx" />
    <EmbeddedResource Include="Content\Templates\QuotePreviewSale.docx" />
    <EmbeddedResource Include="Content\Templates\SaleDeliveryNoteTwoPages.docx" />
    <EmbeddedResource Include="Content\Templates\SaleOrder.docx" />
    <EmbeddedResource Include="Content\Templates\SaleDeliveryNote.docx" />
    <EmbeddedResource Include="Content\Templates\SalesJobRecordSheet.docx" />
    <EmbeddedResource Include="Content\Templates\SalesJobRecordSheetNoStockItems.docx" />
    <EmbeddedResource Include="Content\Templates\SalesWorkSiteSheet.docx" />
    <EmbeddedResource Include="Content\Templates\SupplierComplaintPdf.docx" />
    <EmbeddedResource Include="Content\Templates\WorksJobRecordSheetLC.docx" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
    <PackageReference Include="ClosedXML" Version="0.104.2" />
    <PackageReference Include="Elmah.Io.AspNetCore" Version="5.0.56" />
    <PackageReference Include="Elmah.Io.AspNetCore.ExtensionsLogging" Version="5.0.44" />
    <PackageReference Include="Elmah.Io.Extensions.Logging" Version="5.0.44" />
    <PackageReference Include="Flurl" Version="4.0.0" />
    <PackageReference Include="Hangfire" Version="1.8.11" />
    <PackageReference Include="MediaTypeMap.Core" Version="2.3.3" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="8.0.0" />
    <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="8.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="6.5.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\AuthMiddleware\AuthMiddleware.csproj" />
    <ProjectReference Include="..\Red20.Data\Red20.Data.csproj" />
    <ProjectReference Include="..\Red20.Email\Red20.Email.csproj" />
    <ProjectReference Include="..\Red20.Excel\Red20.Excel.csproj" />
    <ProjectReference Include="..\Red20.Model\Red20.Model.csproj" />
    <ProjectReference Include="..\Red20.Service\Red20.Service.csproj" />
    <ProjectReference Include="..\Red20.Storage\Red20.Storage.csproj" />
    <ProjectReference Include="..\Red20.Util\Red20.Util.csproj" />
  </ItemGroup>

</Project>
