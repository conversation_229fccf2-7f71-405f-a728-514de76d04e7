﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Red20.Excel.Export;
using Red20.Model.Data.Document;
using Red20.Model.Data.Job;
using Red20.Model.Data.Order;
using Red20.Model.Entity;
using Red20.Service.Data.Interface;
using Red20.Service.Storage.Interface;

namespace Red20.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class JobController : ControllerBase
    {

        private IJobService service;
        private IJobInvoiceService invoiceService;
        private IJobInvoiceItemService invoiceItemService;
        private IOrderService orderService;
        private IPurchaseOrderService poService;
        private IDocumentService documentService;
        private IStorageService blobStorage;
        private ILogger<AuthController> logger;
        private IUserService userService;
        private IUnitOfWork unitOfWork;
        private IMapper mapper;
        private IJobService jobService;

        public JobController(
            IJobService service,
            IJobInvoiceService invoiceService,
            IJobInvoiceItemService invoiceItemService,
            IDocumentService documentService,
            IStorageService blobStorage,
            IOrderService orderService,
            IPurchaseOrderService poService,
            ILogger<AuthController> logger,
            IUserService userService,
            IUnitOfWork unitOfWork,
            IMapper mapper,
            IJobService jobService)
        {

            this.service = service;
            this.invoiceService = invoiceService;
            this.invoiceItemService = invoiceItemService;
            this.orderService = orderService;
            this.blobStorage = blobStorage;
            this.documentService = documentService;
            this.poService = poService;
            this.logger = logger;
            this.userService = userService;
            this.unitOfWork = unitOfWork;
            this.mapper = mapper;
            this.jobService = jobService;
        }

        [HttpGet]
        [ProducesResponseType(200, Type = typeof(IList<JobModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get()
        {
            var jobs = service.GetAllEnumerable();
            return Ok(jobs);
        }

        [HttpGet("archived")]
        [ProducesResponseType(200, Type = typeof(IList<JobModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetArchivedJobs()
        {
            var jobs = await service.GetAllAsync();
            var archivedJobs = jobs.Where(c => c.ArchivedDate.HasValue).ToList();
            return Ok(archivedJobs);
        }

        [HttpPost("updateCompletedDate")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> UpdateCompletedDate()
        {
            var jobs = await unitOfWork.Job.Query().Include(i => i.JobInvoices).ToListAsync();

            if (jobs != null && jobs.Any())
            {
                var jobsWithoutCompletedDate = jobs.Where(w => !w.CompletedDate.HasValue).ToList();
                if (jobsWithoutCompletedDate != null && jobsWithoutCompletedDate.Any())
                {
                    foreach (var job in jobsWithoutCompletedDate)
                    {
                        var jobInvoices = job.JobInvoices;
                        if (jobInvoices != null && jobInvoices.Any(a => a.JobType == "Sale"))
                        {
                            var latestInvoiceDate = jobInvoices.Where(w => w.JobType == "Sale").OrderByDescending(o => o.Created).Select(s => s.Created).FirstOrDefault();
                            if (latestInvoiceDate != null)
                            {
                                try
                                {
                                    var newJob = await unitOfWork.Job.GetAsync(job.JobId);
                                    newJob.CompletedDate = latestInvoiceDate;
                                    unitOfWork.Job.Update(newJob);
                                }
                                catch (Exception ex)
                                {
                                    var x = ex;
                                }
                            }
                        }
                    }

                    await unitOfWork.SaveChangesAsync();
                }
            }

            return Ok();
        }

        [HttpGet("getOrdersForCostTransfer")]
        [ProducesResponseType(200, Type = typeof(IList<OrderModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetOrdersForCostTransfer()
        {
            var orders = await orderService.GetAllForCostTransferAsync();
            return Ok(orders);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(JobModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id)
        {
            var job = await service.GetAsyncForJobDetails(id);
            return Ok(job);
        }

        [HttpGet("getJobInvoices/{id}")]
        [ProducesResponseType(200, Type = typeof(IList<JobInvoiceModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetJobInvoices(Guid id)
        {
            try
            {
                var invoices = await invoiceService.GetByJobIdAsync(id);

                if (invoices != null && invoices.Any())
                {
                    foreach (var invoice in invoices)
                    {
                        if (invoice.JobInvoiceItems != null && invoice.JobInvoiceItems.Any())
                        {
                            invoice.JobInvoiceItems = invoice.JobInvoiceItems.OrderBy(o => o.Created).ThenByDescending(o => !o.IsDeliveryCharge).ToList();
                        }
                    }
                }

                return Ok(invoices);
            }
            catch (Exception ex)
            {
                var x = ex;
                return BadRequest();
            }
        }

        [HttpPost("transferCost")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody] JobCostTransferModel model)
        {
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);
            var time = DateTime.Now.TimeOfDay;

            if (model.DestinationOrderId.HasValue && model.DestinationOrderId.Value.ToString() != Guid.Empty.ToString())
            {
                //Destination Job
                var destinationOrder = await orderService.GetAsync(model.DestinationOrderId.Value);
                if (destinationOrder == null)
                {
                    return BadRequest("Order not found.");
                }

                var destinationJob = await service.GetByOrderIdSimpleAsync(destinationOrder.OrderId);
                if (destinationJob == null)
                {
                    destinationJob = new JobModel
                    {
                        CreatedBy = user != null ? $"{user.Firstname} {user.Lastname}" : null,
                        OrderId = destinationOrder.OrderId,
                        CostCentre = user.Location == "Oldmeldrum" ? "001" : user.Location == "West Bromwich" ? "002" : "",
                        CompletedDate = DateTime.UtcNow,
                        OrderType = destinationOrder.Type,
                        OrderNumber = destinationOrder.Number,
                        OrderCustomerName = destinationOrder.Customer != null ? destinationOrder.Customer.Name : string.Empty,
                        Description = destinationOrder.FirstItemDescription,
                        DateOrderRaised = destinationOrder.Created,
                        Currency = destinationOrder.Currency
                    };

                    destinationJob = await jobService.PostAsync(destinationJob);
                }

                var destinationJobInvoice = new JobInvoiceModel();
                destinationJobInvoice.AdjustmentDate = model.Date.Add(time);
                destinationJobInvoice.JobId = destinationJob.JobId;
                destinationJobInvoice.CreatedBy = $"{user.Firstname} {user.Lastname}";
                destinationJobInvoice.JobType = "ADJUSTMENT IN";
                var currentDestinationJobInvoices = await invoiceService.GetByJobIdAsync(destinationJob.JobId);
                int currentAdjustmentInInvoices = currentDestinationJobInvoices.Any(w => w.JobType == "ADJUSTMENT IN") ?
                                                   currentDestinationJobInvoices.Where(w => w.JobType == "ADJUSTMENT IN").Count() + 1 :
                                                   1;

                destinationJobInvoice.InvoiceNumber = $"{destinationOrder.Number}/Adjustment In/{currentAdjustmentInInvoices}";
                destinationJobInvoice.InvoiceDate = model.Date.Add(time);

                if (!await invoiceService.JobInvoiceNumberExistsAsync(destinationJobInvoice.InvoiceNumber))
                {
                    destinationJobInvoice = await invoiceService.PostAsync(destinationJobInvoice);
                }
                else
                {
                    return BadRequest($"Adjustment In Invoice Number already exists in the system");
                }

                var destinationInvoiceItem = new JobInvoiceItemModel
                {
                    JobId = destinationJob.JobId,
                    JobInvoiceId = destinationJobInvoice.JobInvoiceId,
                    InvoiceNumber = destinationJobInvoice.InvoiceNumber,
                    InvoiceType = "ADJUSTMENT IN",
                    Quantity = 1,
                    UnitPrice = model.Value,
                    StockCategory = null,
                    StockCode = null,
                    Value = model.Value,
                    Currency = "Pounds Sterling",
                    Description = model.Description,
                    OrderItemId = destinationOrder.OrderItems != null && destinationOrder.OrderItems.Any() ? destinationOrder.OrderItems.First().OrderItemId : Guid.NewGuid(),
                    QuantityToDeliver = 1,
                    ToFollow = 0,
                    Vat = null,
                    AccountCode = destinationOrder.AccountCode,
                    IsStockItem = false,
                    IsInitialDeliveryCharge = false,
                    IsDeliveryCharge = false,
                    InvoiceDate = destinationJobInvoice.InvoiceDate,
                    LabourPayType = null,
                    AdjustmentDate = model.Date.Add(time)
                };

                await invoiceItemService.PostAsync(destinationInvoiceItem);

                //Source Job
                var sourceJob = await service.GetAsyncByJobId(model.SourceJobId);
                if (sourceJob == null)
                {
                    return BadRequest("Source Job not found.");
                }

                var sourceOrder = await orderService.GetAsync(sourceJob.OrderId);
                if (sourceOrder == null)
                {
                    return BadRequest();
                }

                var sourceJobInvoice = new JobInvoiceModel();
                sourceJobInvoice.AdjustmentDate = model.Date.Add(time);
                sourceJobInvoice.JobId = sourceJob.JobId;
                sourceJobInvoice.CreatedBy = $"{user.Firstname} {user.Lastname}";
                sourceJobInvoice.JobType = "ADJUSTMENT OUT";
                var currentSourceJobInvoices = await invoiceService.GetByJobIdAsync(sourceJob.JobId);
                int currentAdjustmentOutInvoices = currentSourceJobInvoices.Any(w => w.JobType == "ADJUSTMENT OUT") ?
                                                   currentSourceJobInvoices.Where(w => w.JobType == "ADJUSTMENT OUT").Count() + 1 :
                                                   1;

                sourceJobInvoice.InvoiceNumber = $"{sourceOrder.Number}/Adjustment Out/{currentAdjustmentOutInvoices}";
                sourceJobInvoice.InvoiceDate = model.Date.Add(time);

                if (!await invoiceService.JobInvoiceNumberExistsAsync(sourceJobInvoice.InvoiceNumber))
                {
                    sourceJobInvoice = await invoiceService.PostAsync(sourceJobInvoice);
                }
                else
                {
                    return BadRequest($"Adjustment Out Invoice Number already exists in the system");
                }

                var sourceInvoiceItem = new JobInvoiceItemModel
                {
                    JobId = sourceJob.JobId,
                    JobInvoiceId = sourceJobInvoice.JobInvoiceId,
                    InvoiceNumber = sourceJobInvoice.InvoiceNumber,
                    InvoiceType = "ADJUSTMENT OUT",
                    Quantity = 1,
                    UnitPrice = model.Value,
                    StockCategory = null,
                    StockCode = null,
                    Value = model.Value,
                    Currency = "Pounds Sterling",
                    Description = model.Description,
                    OrderItemId = sourceOrder.OrderItems != null && sourceOrder.OrderItems.Any() ? sourceOrder.OrderItems.First().OrderItemId : Guid.NewGuid(),
                    QuantityToDeliver = 1,
                    ToFollow = 0,
                    Vat = null,
                    AccountCode = sourceOrder.AccountCode,
                    IsStockItem = false,
                    IsInitialDeliveryCharge = false,
                    IsDeliveryCharge = false,
                    InvoiceDate = sourceJobInvoice.InvoiceDate,
                    LabourPayType = null,
                    AdjustmentDate = model.Date.Add(time)
                };

                await invoiceItemService.PostAsync(sourceInvoiceItem);
            }
            else
            {
                //Source Job
                var sourceJob = await service.GetAsyncByJobId(model.SourceJobId);
                if (sourceJob == null)
                {
                    return BadRequest("Source Job not found.");
                }

                var sourceOrder = await orderService.GetAsync(sourceJob.OrderId);
                if (sourceOrder == null)
                {
                    return BadRequest();
                }

                var sourceJobInvoice = new JobInvoiceModel();
                sourceJobInvoice.AdjustmentDate = model.Date;
                sourceJobInvoice.JobId = sourceJob.JobId;
                sourceJobInvoice.CreatedBy = $"{user.Firstname} {user.Lastname}";
                sourceJobInvoice.JobType = $"ADJUSTMENT {model.TransferType}";
                var currentSourceJobInvoices = await invoiceService.GetByJobIdAsync(sourceJob.JobId);
                int currentAdjustmentInInvoices = currentSourceJobInvoices.Any(w => w.JobType == $"ADJUSTMENT {model.TransferType}") ?
                                                   currentSourceJobInvoices.Where(w => w.JobType == $"ADJUSTMENT {model.TransferType}").Count() + 1 :
                                                   1;

                sourceJobInvoice.InvoiceNumber = $"{sourceOrder.Number}/Adjustment {(model.TransferType == "IN" ? "In" : "Out")}/{currentAdjustmentInInvoices}";
                sourceJobInvoice.InvoiceDate = model.Date;

                if (!await invoiceService.JobInvoiceNumberExistsAsync(sourceJobInvoice.InvoiceNumber))
                {
                    sourceJobInvoice = await invoiceService.PostAsync(sourceJobInvoice);
                }
                else
                {
                    return BadRequest($"Adjustment {model.TransferType} Invoice Number already exists in the system");
                }

                var sourceInvoiceItem = new JobInvoiceItemModel
                {
                    JobId = sourceJob.JobId,
                    JobInvoiceId = sourceJobInvoice.JobInvoiceId,
                    InvoiceNumber = sourceJobInvoice.InvoiceNumber,
                    InvoiceType = $"ADJUSTMENT {model.TransferType}",
                    Quantity = 1,
                    UnitPrice = model.Value,
                    StockCategory = null,
                    StockCode = null,
                    Value = model.Value,
                    Currency = "Pounds Sterling",
                    Description = model.Description,
                    OrderItemId = sourceOrder.OrderItems != null && sourceOrder.OrderItems.Any() ? sourceOrder.OrderItems.First().OrderItemId : Guid.NewGuid(),
                    QuantityToDeliver = 1,
                    ToFollow = 0,
                    Vat = null,
                    AccountCode = sourceOrder.AccountCode,
                    IsStockItem = false,
                    IsInitialDeliveryCharge = false,
                    IsDeliveryCharge = false,
                    InvoiceDate = sourceJobInvoice.InvoiceDate,
                    LabourPayType = null,
                    AdjustmentDate = model.Date
                };

                await invoiceItemService.PostAsync(sourceInvoiceItem);
            }

            return Ok();
        }
        [HttpPut("archive/{id}")]
        [ProducesResponseType(200, Type = typeof(JobModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> ArchiveJob(Guid id, [FromBody] JobModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }

            var job = unitOfWork.Job.Query().Where(c => c.JobId == id).FirstOrDefault();

            if (job is null)
            {
                return BadRequest();
            }

            job.ArchivedDate = DateTime.Now;

            try
            {
                unitOfWork.Job.Update(job);
                await unitOfWork.SaveChangesAsync();
                return Ok(job);
            }
            catch (Exception ex)
            {

                var error = ex;
                return BadRequest();
            }
        }

        [HttpPut("unArchive/{id}")]
        [ProducesResponseType(200, Type = typeof(JobModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> UnArchiveJob(Guid id, [FromBody] JobModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }

            var job = unitOfWork.Job.Query().Where(c => c.JobId == id).FirstOrDefault();

            if (job is null)
            {
                return BadRequest();
            }

            job.ArchivedDate = null;

            try
            {
                unitOfWork.Job.Update(job);
                await unitOfWork.SaveChangesAsync();
                return Ok(job);
            }
            catch (Exception ex)
            {

                var error = ex;
                return BadRequest();
            }
        }

        [HttpPost("export")]
        [ProducesResponseType(200)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> PrintJobReport(List<JobModel> models)
        {
            try
            {
                var document = new DocumentModel();
                var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                      c.Type == ClaimTypes.Email);
                if (emailClaim == null)
                {
                    return Unauthorized();
                }

                var user = await userService.GetUserByEmailAsync(emailClaim.Value);
                var currentUser = $"{user.Firstname} {user.Lastname}";
                var jobData = ExportUtility.ExportJobReport(models, currentUser);
                document = await documentService.PostAsync("Job_Report.xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"{user.Firstname} {user.Lastname}", "Jobs", null, null, null, false, false, false);
                var file = File(jobData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                await blobStorage.UploadAsync(document.DocumentId, jobData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                return Ok();
            }
            catch (Exception ex)
            {
                return NotFound();
            }
        }

        [HttpPost("exportJobInvoices")]
        [ProducesResponseType(200)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> PrintJobInvoicesReport(List<JobInvoiceModel> models)
        {
            try
            {
                var document = new DocumentModel();
                var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
                if (emailClaim == null)
                {
                    return Unauthorized();
                }

                var user = await userService.GetUserByEmailAsync(emailClaim.Value);
                var currentUser = $"{user.Firstname} {user.Lastname}";
                var jobData = ExportUtility.ExportJobInvoiceReport(models, currentUser);
                document = await documentService.PostAsync("Job_Report.xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"{user.Firstname} {user.Lastname}", "Job Invoices", null, null, null, false, false, false);
                var file = File(jobData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                await blobStorage.UploadAsync(document.DocumentId, jobData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                return Ok();
            }
            catch (Exception ex)
            {
                return NotFound();
            }
        }
        [HttpGet("downloadReport")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> DownloadReport()
        {
            try
            {
                var lastDocument = await unitOfWork.Document.Query().OrderByDescending(c => c.Created).FirstOrDefaultAsync();
                var download = await blobStorage.DownloadAsync(lastDocument.DocumentId);
                var file = File(download.Item1, download.Item2);
                return file;
            }
            catch (Exception ex)
            {
                return NotFound();
            }
        }

        [HttpPost("saleCost")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> PostSale([FromBody] JobSaleModel model)
        {
            var job = await unitOfWork.Job.Query().Where(c => c.JobId == model.JobId).FirstOrDefaultAsync();
            var order = await unitOfWork.Order.Query().Where(c => c.Number == job.OrderNumber).FirstOrDefaultAsync();
            var jobSalesInvoices = await unitOfWork.JobInvoice.Query().Where(c => c.JobId == model.JobId && c.JobType == "Sale").ToListAsync();
            var jobSalesCreditInvoices = await unitOfWork.JobInvoice.Query().Where(c => c.JobId == model.JobId && c.JobType == "SALE CREDIT").ToListAsync();
            var jobSalesCount = jobSalesInvoices.Count();
            var newCount = jobSalesCount + 1;
            var jobSaleCreditCount = jobSalesCreditInvoices.Count();
            var newSaleCreditCount = jobSaleCreditCount + 1;
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);

            var newJobInvoice = new JobInvoice
            {
                JobInvoiceId = Guid.NewGuid(),
                JobId = model.JobId,
                JobType = model.SaleCredit == "SALE" ? "Sale" : "SALE CREDIT",
                InvoiceNumber = model.SaleCredit == "SALE" ? $"{job.OrderNumber}/{newCount}" : $"{job.OrderNumber}/CR{newSaleCreditCount}",
                InvoiceDate = model.InvoiceDate,
                Created = DateTime.UtcNow,
                CreatedBy = user.Name
            };
            await unitOfWork.JobInvoice.CreateAsync(newJobInvoice);

            await unitOfWork.SaveChangesAsync();

            var newJobInvoiceItem = new JobInvoiceItem
            {
                JobInvoiceItemId = Guid.NewGuid(),
                JobId = model.JobId,
                JobInvoiceId = newJobInvoice.JobInvoiceId,
                UnitPrice = model.UnitPrice,
                InvoiceType = model.SaleCredit == "SALE" ? "INV" : "SALE CREDIT",
                Quantity = model.TotalInvoiced,
                Currency = order.Currency,
                TotalNet = model.TotalInvoiced * model.UnitPrice,
                Vat = model.SaleCredit == "SALE" ? order.TaxCode : String.Empty,
                TotalVat = model.SaleCredit == "SALE" ? order.TaxCode.Contains("20") ? (model.TotalInvoiced * model.UnitPrice *20)/100 : 0 : 0,
                TotalGross = model.SaleCredit == "SALE" ? order.TaxCode.Contains("20") ? ((model.TotalInvoiced * model.UnitPrice * 20) / 100) + (model.TotalInvoiced * model.UnitPrice) : model.TotalInvoiced * model.UnitPrice : model.TotalInvoiced * model.UnitPrice,
                InvoiceDate = DateTime.UtcNow,
                QuantityToDeliver = model.TotalInvoiced,
                Created = model.InvoiceDate,
                AccountCode = order.AccountCode,
                Description = model.Description,
                InvoiceNumber = newJobInvoice.InvoiceNumber,
                Value = model.TotalInvoiced * model.UnitPrice,
                OriginalUnitPrice = model.SaleCredit == "SALE" ? model.UnitPrice : 0
            };

            await unitOfWork.JobInvoiceItem.CreateAsync(newJobInvoiceItem);

            await unitOfWork.SaveChangesAsync();

            return Ok();
        }
    }
}