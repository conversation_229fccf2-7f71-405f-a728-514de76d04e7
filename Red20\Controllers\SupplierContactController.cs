﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Red20.Model.Data;
using Red20.Service.Data.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Red20.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class SupplierContactController : ControllerBase
    {

        private ISupplierContactService service;
        private ISupplierAddressService addressService;
        private ILogger<AuthController> logger;
        IUnitOfWork unitOfWork;

        public SupplierContactController(
            ISupplierContactService service,
            ISupplierAddressService addressService,
            IUnitOfWork unitOfWork,
            ILogger<AuthController> logger)
        {

            this.service = service;
            this.addressService = addressService;
            this.unitOfWork = unitOfWork;
            this.logger = logger;
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(SupplierContactModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id)
        {
            var supplierContact = await service.GetAsync(id);
            return Ok(supplierContact);
        }

        [HttpGet]
        [ProducesResponseType(200, Type = typeof(IList<SupplierContactModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAll()
        {
            var supplierContacts = service.GetAllSupplierContacts();
            return Ok(supplierContacts.ToList());
        }

        [HttpGet("byAddress/{id}")]
        [ProducesResponseType(200, Type = typeof(SupplierContactModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetBySupplierAddress(Guid id)
        {
            var supplierContacts = service.GetBySupplierAddress(id);
            return Ok(supplierContacts.ToList());
        }

        [HttpGet("byAddressFirstCreated/{id}")]
        [ProducesResponseType(200, Type = typeof(SupplierContactModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetBySupplierAddressFirstCreated(Guid id)
        {
            var supplierContacts = service.GetBySupplierAddress(id);
            var result = supplierContacts.Where(c => c.IsDefault).FirstOrDefault();
            return Ok(result);
        }

        [HttpGet("bySupplier/{id}")]
        [ProducesResponseType(200, Type = typeof(IList<SupplierContactModel>))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetBySupplier(Guid id)
        {
            var supplierContact = await service.GetBySupplierAsync(id);
            return Ok(supplierContact.ToList());
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(SupplierContactUpdateModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody] SupplierContactUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }

            var supplierContact = await service.PostAsync(model);
            return Ok(supplierContact);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(SupplierContactUpdateModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Put(Guid id, [FromBody] SupplierContactUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }

            var supplierContact = await service.PutAsync(id, model);

            return Ok(supplierContact);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Delete(Guid id)
        {
            await service.DeleteAsync(id);
            return Ok();
        }
    }
}