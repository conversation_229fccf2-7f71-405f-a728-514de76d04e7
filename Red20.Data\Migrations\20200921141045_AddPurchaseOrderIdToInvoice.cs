﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Red20.Data.Migrations
{
    public partial class AddPurchaseOrderIdToInvoice : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "SupplierId",
                table: "JobInvoices");

            migrationBuilder.AddColumn<Guid>(
                name: "PurchaseOrderId",
                table: "JobInvoices",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "PurchaseOrderId",
                table: "JobInvoices");

            migrationBuilder.AddColumn<Guid>(
                name: "SupplierId",
                table: "JobInvoices",
                type: "uniqueidentifier",
                nullable: true);
        }
    }
}
