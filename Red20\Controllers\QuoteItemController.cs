﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using DocumentFormat.OpenXml.Office2021.Excel.RichDataWebImage;
using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Red20.Data.Model;
using Red20.Model.Data;
using Red20.Model.Data.Quote;
using Red20.Model.Entity;
using Red20.Service.Data;
using Red20.Service.Data.Interface;

namespace Red20.Controllers {
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class QuoteItemController : ControllerBase {

        private IQuoteService quoteService;
        private IQuoteItemService quoteItemService;
        private ILogger<AuthController> logger;
        private IUnitOfWork unitOfWork;

        public QuoteItemController(
            IQuoteService quoteService,
            IQuoteItemService quoteItemService,
            IUnitOfWork unitOfWork,
            ILogger<AuthController> logger) {

            this.quoteService = quoteService;
            this.quoteItemService = quoteItemService;
            this.logger = logger;
            this.unitOfWork = unitOfWork;
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(QuoteItemModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id) {
            var quoteItem = await quoteItemService.GetByIdAsync(id);
            return Ok(quoteItem);
        }

        [HttpGet("byQuote/{id}")]
        [ProducesResponseType(200, Type = typeof(QuoteItemModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetByQuote(Guid id) {
            var quoteItems = await quoteItemService.GetByQuoteIdAsync(id);
            return Ok(quoteItems);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(QuoteItemModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody]QuoteItemUpdateModel model) {

            if (model is null) {
                return BadRequest();
            }

            var quoteItem = await quoteItemService.PostAsync(model);

            await UpdateQuote(quoteItem.QuoteId);

            return Ok(quoteItem);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(QuoteItemModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Put(Guid id, [FromBody]QuoteItemUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }

            var quoteItem = await quoteItemService.GetByIdAsync(id);
            if (quoteItem is null) {
                return BadRequest();
            }

            quoteItem = await quoteItemService.PutAsync(id, model);

            await UpdateQuote(quoteItem.QuoteId);

            return Ok(quoteItem);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Delete(Guid id) {

            var quoteItem = await unitOfWork.QuoteItem.GetAsync(id);

            var quoteId = quoteItem.QuoteId;

            await quoteItemService.DeleteAsync(id);

            await UpdateQuote(quoteId);

            return Ok();
        }
        [HttpPost("updateItems")]
        [ProducesResponseType(200, Type = typeof(QuoteItemModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]

        public async Task<IActionResult> UpdateOrderItems() {
            var quoteList = unitOfWork.Quote.Query().Include(c => c.QuoteItems).ToList();
            foreach (var quote in quoteList) {
                if (quote.QuoteItems != null) {
                    var quoteItems = unitOfWork.QuoteItem.Query(w => w.QuoteId == quote.QuoteId).Include(w => w.Quote).ToList();
                    foreach (var item in quoteItems) {
                        item.Description = item.Description.Replace("<p></p>", "");
                        unitOfWork.QuoteItem.Update(item);
                        await unitOfWork.SaveChangesAsync();
                    }
                }
            }
            return Ok();
        }

        private async Task<IActionResult> UpdateQuote(Guid quoteId)
        {
            var description = "";

            var quote = await unitOfWork.Quote.Query().Include(q => q.QuoteItems).Where(c => c.QuoteId == quoteId).FirstOrDefaultAsync();

            var quoteItems = quote.QuoteItems.Any() ? quote.QuoteItems.OrderBy(c => c.Created).ToList() : null;

            if (quoteItems.Any())
            {
                var quoteItemDescription = quoteItems[0].Description;

                double? hireItemsValue = 0.0;

                double? salesItemsValue = 0.0;

                double discount = 0.0;

                double totalValue = 0.0;

                double? collectionChargeValue = quote.Type == "Hire" && quote.CollectionCharge.HasValue ? quote.CollectionCharge.Value : (double?)null;

                double? deliveryCharge = quote.DeliveryCharge.HasValue ? quote.DeliveryCharge.Value : (double?)null;

                foreach (var item in quoteItems)
                {
                    if (quote.Type == "Hire")
                    {
                        if (quote.HirePeriod.HasValue)
                        {
                            var value = item.UnitPrice * item.Quantity * quote.HirePeriod.Value;

                            discount = item.Discount.HasValue ? value * (item.Discount.Value / 100) : 0;

                            hireItemsValue += value - discount;
                        }
                    }
                    else
                    {
                        deliveryCharge = quote.DeliveryCharge.HasValue ? quote.DeliveryCharge.Value : (double?)null;

                        var value = item.UnitPrice * item.Quantity;

                        discount = item.Discount.HasValue ? value * (item.Discount.Value / 100) : 0;

                        salesItemsValue += value - discount;
                    }
                }

                if (hireItemsValue.HasValue && hireItemsValue.Value > 0)
                {
                    totalValue = totalValue + hireItemsValue.Value;
                }
                else if (salesItemsValue.HasValue && salesItemsValue.Value > 0)
                {
                    totalValue = totalValue + salesItemsValue.Value;
                }
                if (collectionChargeValue.HasValue)
                {
                    totalValue = totalValue + collectionChargeValue.Value;
                }
                if (deliveryCharge.HasValue)
                {
                    totalValue = totalValue + deliveryCharge.Value;
                }

                if (quoteItemDescription != null)
                {
                    var splitDescription = quoteItemDescription.Split("</p>");

                    description = $"{splitDescription[0].Replace("<p>", "").Replace("</p>", "").Replace("<em>", "").Replace("</em>", "").Replace("<strong>", "").Replace("</strong>", "").Replace("<ul>", "").Replace("</ul>", "").Replace("<li>", "").Replace("</li>", "").Replace("<u>", "").Replace("</u>", "")}";
                }
                quote.ItemDescription = description;

                quote.QuoteValue = totalValue;
            }

            unitOfWork.Quote.Update(quote);

            await unitOfWork.SaveChangesAsync();

            return Ok();
        }
    }
}