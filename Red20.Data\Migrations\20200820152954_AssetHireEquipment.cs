﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Red20.Data.Migrations
{
    public partial class AssetHireEquipment : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "HireEquipmentId",
                table: "Assets",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Assets_HireEquipmentId",
                table: "Assets",
                column: "HireEquipmentId");

            migrationBuilder.AddForeignKey(
                name: "FK_Assets_HireEquipments_HireEquipmentId",
                table: "Assets",
                column: "HireEquipmentId",
                principalTable: "HireEquipments",
                principalColumn: "HireEquipmentId",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Assets_HireEquipments_HireEquipmentId",
                table: "Assets");

            migrationBuilder.DropIndex(
                name: "IX_Assets_HireEquipmentId",
                table: "Assets");

            migrationBuilder.DropColumn(
                name: "HireEquipmentId",
                table: "Assets");
        }
    }
}
