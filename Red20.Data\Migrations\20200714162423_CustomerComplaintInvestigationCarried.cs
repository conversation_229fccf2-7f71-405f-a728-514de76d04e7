﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Red20.Data.Migrations
{
    public partial class CustomerComplaintInvestigationCarried : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "InvestigationCarriedOutBy",
                table: "CustomerComplaints",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "InvestigationCarriedOutDate",
                table: "CustomerComplaints",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "InvestigationCarriedOutBy",
                table: "CustomerComplaints");

            migrationBuilder.DropColumn(
                name: "InvestigationCarriedOutDate",
                table: "CustomerComplaints");
        }
    }
}
