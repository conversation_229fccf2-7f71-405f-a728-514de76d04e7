﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Red20.Model.Data;
using Red20.Model.Entity;
using Red20.Service.Data.Interface;

namespace Red20.Controllers {
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class CurrencyRateController : ControllerBase {

        private ICurrencyRateService currencyRateService;
        private ILogger<AuthController> logger;

        public CurrencyRateController(
            ICurrencyRateService currencyRateService,
            IUnitOfWork unitOfWork,
            ILogger<AuthController> logger) {

            this.currencyRateService = currencyRateService;
            this.logger = logger;
        }

        [HttpGet]
        [ProducesResponseType(200, Type = typeof(IList<CurrencyRateModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get() {
            var currencyRates = currencyRateService.GetAllCurrencyRates();
            return Ok(currencyRates);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(CurrencyRateModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id) {

            var currencyRate = await currencyRateService.GetAsync(id);

            return Ok(currencyRate);
        }

        [HttpGet("byCurrency/{name}")]
        [ProducesResponseType(200, Type = typeof(CurrencyRateModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetByCurrency(string name) {

            var currencyRate = await currencyRateService.GetByCurrencyAsync(name);

            return Ok(currencyRate);
        }

        [HttpGet("getRateByCurrency/{name}")]
        [ProducesResponseType(200)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetRateByCurrency(string name) {

            var currencyRate = await currencyRateService.GetRateByCurrencyAsync(name);

            return Ok(currencyRate);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(CurrencyRateModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody]CurrencyRateUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }

            var currencyRate = await currencyRateService.PostAsync(model);

            return Ok(currencyRate);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(CurrencyRateModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Put(Guid id, [FromBody]CurrencyRateUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }

            var currencyRate = await currencyRateService.GetAsync(id);

            if (currencyRate is null) {
                return BadRequest();
            }

            currencyRate = await currencyRateService.PutAsync(id, model);

            return Ok(currencyRate);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Delete(Guid id) {

            await currencyRateService.DeleteAsync(id);
            return Ok();
        }
    }
}