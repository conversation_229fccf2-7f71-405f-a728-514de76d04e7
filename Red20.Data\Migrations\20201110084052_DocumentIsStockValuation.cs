﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Red20.Data.Migrations
{
    public partial class DocumentIsStockValuation : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsStockValuationReport",
                table: "Documents",
                nullable: false,
                defaultValue: false);

            migrationBuilder.CreateTable(
                name: "StockFifos",
                columns: table => new
                {
                    StockFifoId = table.Column<Guid>(nullable: false),
                    StockId = table.Column<Guid>(nullable: false),
                    CostPrice = table.Column<double>(nullable: true),
                    CreatedBy = table.Column<string>(nullable: true),
                    Created = table.Column<DateTime>(nullable: false),
                    Modified = table.Column<DateTime>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_StockFifos", x => x.StockFifoId);
                    table.ForeignKey(
                        name: "FK_StockFifos_Stocks_StockId",
                        column: x => x.StockId,
                        principalTable: "Stocks",
                        principalColumn: "StockId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_StockFifos_StockId",
                table: "StockFifos",
                column: "StockId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "StockFifos");

            migrationBuilder.DropColumn(
                name: "IsStockValuationReport",
                table: "Documents");
        }
    }
}
