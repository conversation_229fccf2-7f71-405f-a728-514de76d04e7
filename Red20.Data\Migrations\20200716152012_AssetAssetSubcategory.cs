﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Red20.Data.Migrations
{
    public partial class AssetAssetSubcategory : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "AssetSubCategoryId",
                table: "Assets",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Assets_AssetSubCategoryId",
                table: "Assets",
                column: "AssetSubCategoryId");

            migrationBuilder.AddForeignKey(
                name: "FK_Assets_AssetSubCategories_AssetSubCategoryId",
                table: "Assets",
                column: "AssetSubCategoryId",
                principalTable: "AssetSubCategories",
                principalColumn: "AssetSubCategoryId",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Assets_AssetSubCategories_AssetSubCategoryId",
                table: "Assets");

            migrationBuilder.DropIndex(
                name: "IX_Assets_AssetSubCategoryId",
                table: "Assets");

            migrationBuilder.DropColumn(
                name: "AssetSubCategoryId",
                table: "Assets");
        }
    }
}
