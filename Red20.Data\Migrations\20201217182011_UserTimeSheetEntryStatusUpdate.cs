﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace Red20.Data.Migrations
{
    public partial class UserTimeSheetEntryStatusUpdate : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "FridayStatus",
                table: "UserTimesheetEntries");

            migrationBuilder.DropColumn(
                name: "MondayStatus",
                table: "UserTimesheetEntries");

            migrationBuilder.DropColumn(
                name: "SaturdayStatus",
                table: "UserTimesheetEntries");

            migrationBuilder.DropColumn(
                name: "SundayStatus",
                table: "UserTimesheetEntries");

            migrationBuilder.DropColumn(
                name: "ThursdayStatus",
                table: "UserTimesheetEntries");

            migrationBuilder.DropColumn(
                name: "TuesdayStatus",
                table: "UserTimesheetEntries");

            migrationBuilder.DropColumn(
                name: "WednesdayStatus",
                table: "UserTimesheetEntries");

            migrationBuilder.AddColumn<string>(
                name: "Status",
                table: "UserTimesheetEntries",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Status",
                table: "UserTimesheetEntries");

            migrationBuilder.AddColumn<string>(
                name: "FridayStatus",
                table: "UserTimesheetEntries",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "MondayStatus",
                table: "UserTimesheetEntries",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SaturdayStatus",
                table: "UserTimesheetEntries",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SundayStatus",
                table: "UserTimesheetEntries",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ThursdayStatus",
                table: "UserTimesheetEntries",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "TuesdayStatus",
                table: "UserTimesheetEntries",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "WednesdayStatus",
                table: "UserTimesheetEntries",
                type: "nvarchar(max)",
                nullable: true);
        }
    }
}
