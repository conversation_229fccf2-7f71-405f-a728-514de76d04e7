﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace Red20.Data.Migrations
{
    public partial class UpdateOrderItemTableForDeliveryChargeItem : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "AccountCode",
                table: "OrderItems",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeliveryChargeItem",
                table: "OrderItems",
                nullable: false,
                defaultValue: false);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AccountCode",
                table: "OrderItems");

            migrationBuilder.DropColumn(
                name: "IsDeliveryChargeItem",
                table: "OrderItems");
        }
    }
}
