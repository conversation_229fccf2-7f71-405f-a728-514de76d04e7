﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace Red20.Data.Migrations
{
    public partial class MoveFlagToInvoiceTableFromJobTable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ShowInCosting",
                table: "Jobs");

            migrationBuilder.AddColumn<bool>(
                name: "ShowInCosting",
                table: "JobInvoices",
                nullable: false,
                defaultValue: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ShowInCosting",
                table: "JobInvoices");

            migrationBuilder.AddColumn<bool>(
                name: "ShowInCosting",
                table: "Jobs",
                type: "bit",
                nullable: false,
                defaultValue: true);
        }
    }
}
