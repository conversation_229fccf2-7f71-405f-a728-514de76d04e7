﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.Extensions.Logging;
using Red20.Data.Model;
using Red20.Model.Data;
using Red20.Model.Data.Job;
using Red20.Model.Data.Lookup;
using Red20.Model.Data.PurchaseOrder;
using Red20.Model.Entity;
using Red20.Service;
using Red20.Service.Data.Interface;
using Red20.Service.Email.Interface;
using Red20.Service.Xero.Interface;
using Red20.Utility;

namespace Red20.Controllers {
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class LookupController : ControllerBase {

        IUnitOfWork unitOfWork;
        IXeroService xeroService;
        ILogger<AuthController> logger;

        public LookupController(
            IUnitOfWork unitOfWork,
             IXeroService xeroService,
            ILogger<AuthController> logger) {

            this.unitOfWork = unitOfWork;
            this.logger = logger;
            this.xeroService = xeroService;
        }


        [HttpGet("taxRates")]
        [ProducesResponseType(200, Type = typeof(IList<TaxRateModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetTaxTypes() {
            var taxRates = await xeroService.GetTaxRatesAsync();
            var taxRateStrings = taxRates.Any() ? taxRates.Select(s => s.Name).ToList() : new List<string>();
            return Ok(taxRateStrings);
        }

        [HttpGet("accountCodes")]
        [ProducesResponseType(200, Type = typeof(IList<AccountCodeModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAccountCodes() {
            var accountCodes = await xeroService.GetAccountsAsync();
            return Ok(accountCodes);
        }

        [HttpGet("assetTypes")]
        [ProducesResponseType(200, Type = typeof(IList<AssetTypeModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAssetTypes() {
            IList<Xero.NetStandard.OAuth2.Model.Asset.AssetType> assetTypes = await xeroService.GetAssetTypesAsync();
            return Ok(assetTypes);
        }
    }
}