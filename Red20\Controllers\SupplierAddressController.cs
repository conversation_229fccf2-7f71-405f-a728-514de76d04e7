﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Red20.Model.Data;
using Red20.Model.Entity;
using Red20.Service.Data.Interface;

namespace Red20.Controllers {
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class SupplierAddressController : ControllerBase {

        private ISupplierAddressService service;
        IUnitOfWork unitOfWork;

        public SupplierAddressController(
            ISupplierAddressService service,
            IUnitOfWork unitOfWork,
            ILogger<AuthController> logger) {
            this.service = service;
            this.unitOfWork = unitOfWork;
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(SupplierAddressModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id) {
            var supplierAddress = await service.GetAsync(id);
            return Ok(supplierAddress);
        }

        [HttpGet("bySupplier/{id}")]
        [ProducesResponseType(200, Type = typeof(IList<SupplierAddressModel>))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetBySupplier(Guid id) {
            var supplierAddress = await service.GetBySupplierId(id);
            return Ok(supplierAddress.ToList());
        }

        [HttpGet("bySupplierFirstCreated/{id}")]
        [ProducesResponseType(200, Type = typeof(IList<SupplierAddressModel>))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetBySupplierFirstCreated(Guid id) {
            var supplierAddress = await service.GetBySupplierAsync(id);
            var result = supplierAddress.FirstOrDefault();
            return Ok(result);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(SupplierAddressUpdateModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody]SupplierAddressUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }

            var supplierAddress = await service.PostAsync(model);
            return Ok(supplierAddress);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(SupplierAddressUpdateModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Put(Guid id, [FromBody]SupplierAddressUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }

            var supplierAddress = await service.GetAsync(id);

            if (!model.EditAddress) {
                if (!model.IsHeadOfficeDefault && supplierAddress.IsHeadOfficeDefault) {
                    var supplierAddresses = unitOfWork.SupplierAddress.Query().Where(c => c.SupplierId == model.SupplierId && c.IsHeadOfficeDefault).ToList();

                    if (supplierAddresses.Count > 1 && supplierAddresses.Any(c => c.SupplierAddressId != supplierAddress.SupplierAddressId && !model.IsHeadOfficeDefault)) {
                        return BadRequest();
                    } else {
                        return BadRequest();
                    }
                }
            }

            if (supplierAddress is null) {
                return BadRequest();
            }

            supplierAddress = await service.PutAsync(id, model);
            return Ok(supplierAddress);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Delete(Guid id) {
            await service.DeleteAsync(id);
            return Ok();
        }
    }
}