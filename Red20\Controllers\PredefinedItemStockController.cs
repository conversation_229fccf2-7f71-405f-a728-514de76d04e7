﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Red20.Model.Data;
using Red20.Model.Entity;
using Red20.Service.Data.Interface;

namespace Red20.Controllers {
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class PredefinedItemStockController : ControllerBase {

        private IPredefinedItemStockService predefinedItemStockService;
        private IStockService stockService;
        private ILogger<AuthController> logger;

        public PredefinedItemStockController(
            IPredefinedItemStockService predefinedItemStockService,
            IStockService stockService,
            IUnitOfWork unitOfWork,
            ILogger<AuthController> logger) {

            this.predefinedItemStockService = predefinedItemStockService;
            this.logger = logger;
        }

        [HttpGet]
        [ProducesResponseType(200, Type = typeof(IList<PredefinedItemStockModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get() {
            var predefinedStockItems = predefinedItemStockService.GetAllPredefinedItems();
            return Ok(predefinedStockItems);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(PredefinedItemStockModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id) {

            var predefinedItem = await predefinedItemStockService.GetAsync(id);

            return Ok(predefinedItem);
        }

        [HttpGet("byPredefinedItem/{id}")]
        [ProducesResponseType(200, Type = typeof(PredefinedItemStockModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetByPredefinedItem(Guid id) {
            var predefinedStockItems = await predefinedItemStockService.GetByPredefinedItemIdAsync(id);
            return Ok(predefinedStockItems);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(PredefinedItemStockModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody]PredefinedItemStockUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }
            try {
                var predefinedItemStock = await predefinedItemStockService.PostAsync(model);
                return Ok(predefinedItemStock);
            } catch(Exception ex) {
                logger.LogError($"Cannot create Purchase Order - {ex}");
                return BadRequest();
            }

        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(PredefinedItemStockModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Put(Guid id, [FromBody]PredefinedItemStockUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }

            var predefinedItem = await predefinedItemStockService.GetAsync(id);

            if (predefinedItem is null) {
                return BadRequest();
            }

            predefinedItem = await predefinedItemStockService.PutAsync(id, model);

            return Ok(predefinedItem);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Delete(Guid id) {

            await predefinedItemStockService.DeleteAsync(id);
            return Ok();
        }
    }
}