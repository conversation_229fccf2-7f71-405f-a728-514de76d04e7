﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Red20.Data.Model;
using Red20.Model.Data;
using Red20.Model.Data.PurchaseOrder;
using Red20.Model.Entity;
using Red20.Service.Data.Interface;

namespace Red20.Controllers {
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class PurchaseOrderItemLogController : ControllerBase {

        private IPurchaseOrderItemLogService purchaseOrderItemLogService;
        private ILogger<AuthController> logger;
        IUnitOfWork unitOfWork;


        public PurchaseOrderItemLogController(
            IPurchaseOrderItemLogService purchaseOrderItemLogService,
            IUnitOfWork unitOfWork,
            ILogger<AuthController> logger) {

            this.purchaseOrderItemLogService = purchaseOrderItemLogService;
            this.logger = logger;
            this.unitOfWork = unitOfWork;
        }

        [HttpGet("byPurchaseOrder/{id}")]
        [ProducesResponseType(200, Type = typeof(PurchaseOrderItemLogModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetByPurchaseOrder(Guid id) {
            var purchaseOrderItems = await purchaseOrderItemLogService.GetByPurchaseOrderIdAsync(id);
            return Ok(purchaseOrderItems);
        }
    }
}