﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Red20.Excel.Export;
using Red20.Model.Data.Account;
using Red20.Model.Data.Document;
using Red20.Model.Data.Lookup;
using Red20.Model.Data.PurchaseOrder;
using Red20.Service;
using Red20.Service.Data.Interface;
using Red20.Service.Email.Interface;
using Red20.Service.Storage.Interface;
using Red20.Service.Xero.Interface;
using Red20.Utility;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Red20.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class PurchaseOrderController : ControllerBase
    {

        private IPurchaseOrderService purchaseOrderService;
        private IPurchaseRequisitionService purchaseRequisitionService;
        private ReportService reportService;
        private ILogger<AuthController> logger;
        private IEmailService emailService;
        private IDocumentService documentService;
        private IStorageService blobStorage;
        private IUserService userService;
        IUnitOfWork unitOfWork;
        IXeroService xeroService;

        public PurchaseOrderController(
            IPurchaseOrderService purchaseOrderService,
            IPurchaseRequisitionService purchaseRequisitionService,
            IUserService userService,
            ReportService reportService,
            IEmailService emailService,
            IUnitOfWork unitOfWork,
            IXeroService xeroService,
            IDocumentService documentService,
            IStorageService blobStorage,
            ILogger<AuthController> logger)
        {

            this.purchaseOrderService = purchaseOrderService;
            this.purchaseRequisitionService = purchaseRequisitionService;
            this.userService = userService;
            this.unitOfWork = unitOfWork;
            this.reportService = reportService;
            this.emailService = emailService;
            this.logger = logger;
            this.xeroService = xeroService;
            this.documentService = documentService;
            this.blobStorage = blobStorage;
        }

        [HttpGet]
        [ProducesResponseType(200, Type = typeof(IList<PurchaseOrderModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get([FromQuery] PurchaseOrderFilterParams filterParams)
        {
            try
            {
                var purchaseOrders = await purchaseOrderService.GetPaginatedPurchaseOrders(filterParams);
                return Ok(purchaseOrders);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot create Purchase Order - {ex}");
                return BadRequest();
            }
        }

        [HttpGet("getAll")]
        [ProducesResponseType(200, Type = typeof(IList<PurchaseOrderModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAllPurchaseOrders()
        {
            try
            {
                var purchaseOrders = await purchaseOrderService.GetAsync();
                return Ok(purchaseOrders);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot create Purchase Order - {ex}");
                return BadRequest();
            }
        }

        [HttpGet("getAllPurchaseOrders")]
        [ProducesResponseType(200, Type = typeof(IList<PurchaseOrderModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAllFilteredPurchaseOrders()
        {
            try
            {
                var purchaseOrders = await purchaseOrderService.GetAllFilteredPurchaseOrders();
                return Ok(purchaseOrders);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot create Purchase Order - {ex}");
                return BadRequest();
            }
        }

        [HttpGet("getPos")]
        [ProducesResponseType(200, Type = typeof(IList<PurchaseOrderModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetPurchaseOrders()
        {
            try
            {
                var purchaseOrders = await purchaseOrderService.GetPurchaseOrders();
                return Ok(purchaseOrders);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot create Purchase Order - {ex}");
                return BadRequest();
            }
        }

        [HttpGet("archived")]
        [ProducesResponseType(200, Type = typeof(IList<PurchaseOrderModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetArchivedPurchaseOrders()
        {
            try
            {
                var purchaseOrders = await purchaseOrderService.GetAllPurchaseOrders();
                var archivedPurchasOrders = purchaseOrders.Where(c => c.ArchivedDate.HasValue);
                return Ok(archivedPurchasOrders);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot create Purchase Order - {ex}");
                return BadRequest();
            }
        }

        [HttpGet("taxRates")]
        [ProducesResponseType(200, Type = typeof(IList<TaxRateModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetTaxTypes()
        {
            var taxRates = await xeroService.GetTaxRatesAsync();
            var taxRateStrings = taxRates.Any() ? taxRates.Select(s => s.Name).ToList() : new List<string>();
            return Ok(taxRateStrings);
        }

        [HttpGet("quoteSaleHireTaxRates")]
        [ProducesResponseType(200, Type = typeof(IList<TaxRateModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetTaxTypesForQuoteSaleHire()
        {
            var requiredTaxRates = new List<string> { "20% (VAT on Income)", "Zero Rated Income", "No VAT" };
            var taxRates = await xeroService.GetFilteredTaxRatesAsync(requiredTaxRates);
            var taxRateStrings = taxRates.Any() ? taxRates.Select(s => s.Name).ToList() : new List<string>();
            return Ok(taxRateStrings);
        }

        [HttpGet("accountCodes")]
        [ProducesResponseType(200, Type = typeof(IList<AccountCodeLookupModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAccountCodes()
        {
            var accountCodes = await xeroService.GetAccountCodesAsync();
            return Ok(accountCodes);
        }


        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(PurchaseOrderModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id)
        {

            var purchaseOrder = await purchaseOrderService.GetAsync(id);

            return Ok(purchaseOrder);
        }

        [HttpGet("supplierEmailAddress/{id}")]
        [ProducesResponseType(200, Type = typeof(PurchaseOrderModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetSupplierEmailAddress(Guid id)
        {

            var purchaseOrder = await purchaseOrderService.GetAsync(id);

            var supplierAddress = unitOfWork.SupplierAddress.Query().Where(s => s.SupplierId == purchaseOrder.SupplierId && s.IsHeadOfficeDefault).Include(x => x.SupplierContacts).FirstOrDefault();
            var supplierContactEmailAddress = supplierAddress.SupplierContacts.Select(c => c.EmailAddress).FirstOrDefault();

            var supplierEmailAddress = unitOfWork.SupplierContact.Query(c => c.SupplierId == purchaseOrder.SupplierId).Select(x => x.EmailAddress).ToList();

            return Ok(supplierContactEmailAddress);
        }

        [HttpGet("emailAddress/{id}")]
        [ProducesResponseType(200, Type = typeof(PurchaseOrderModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetEmailAddresses(Guid id)
        {

            var purchaseOrder = await purchaseOrderService.GetAsync(id);

            var supplierEmailAddress = unitOfWork.SupplierContact.Query(c => c.SupplierId == purchaseOrder.SupplierId).Select(x => x.EmailAddress).ToList();

            return Ok(supplierEmailAddress);
        }

        [HttpGet("bySupplier/{id}")]
        [ProducesResponseType(200, Type = typeof(PurchaseOrderModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetBySupplier(Guid id)
        {

            var purchaseOrders = await purchaseOrderService.GetBySupplier(id);

            return Ok(purchaseOrders);
        }


        [HttpPost]
        [ProducesResponseType(200, Type = typeof(PurchaseOrderModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody] PurchaseOrderUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);
            model.CreatedByEmailAddress = user.EmailAddress;
            model.CreatedBy = user.Name;

            if (model.DeliveryAddress.Contains("Oldmeldrum") || model.DeliveryAddress.Contains("West Bromwich"))
            {
                model.IsInternalDeliveryAddress = true;
            }

            model.Number = await NumberSequenceUtility.GetNextPurchaseOrderNumber(unitOfWork);

            try
            {
                var purchaseOrder = await purchaseOrderService.PostAsync(model);
                return Ok(purchaseOrder);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot create Purchase Order - {ex}");
                return BadRequest();
            }
        }

        [HttpPost("convertToPurchaseOrder/{id}")]
        [ProducesResponseType(200, Type = typeof(PurchaseOrderModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> ConvertToPurchaseOrder(Guid id)
        {

            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);
            var number = await NumberSequenceUtility.GetNextPurchaseOrderNumber(unitOfWork);
            var raisedBy = user.Name;
            var raisedByEmail = user.EmailAddress;

            try
            {
                var purchaseOrder = await purchaseOrderService.ConvertToPurchaseOrder(id, number, raisedBy, raisedByEmail);
                return Ok(purchaseOrder);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot create Purchase Order - {ex}");
                return BadRequest();
            }
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(PurchaseOrderModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Put(Guid id, [FromBody] PurchaseOrderUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }
            var vatCode = "";

            var purchaseOrder = await purchaseOrderService.GetAsync(id);

            vatCode = purchaseOrder.VatCode;

            if (purchaseOrder is null)
            {
                return BadRequest();
            }

            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                      c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);
            var purchaseOrderItems = unitOfWork.PurchaseOrderItem.Query(c => c.PurchaseOrderId == id).ToList();

            try
            {
                purchaseOrder = await purchaseOrderService.PutAsync(id, model);

                if (model.VatCode != vatCode)
                {
                    foreach (var item in purchaseOrderItems)
                    {
                        double? discount = item.Discount.HasValue ? item.Discount.Value / 100 : 0;
                        item.TotalNet = item.Quantity.Value * item.Price.Value;
                        var totalDiscount = item.Discount.HasValue ? item.TotalNet * discount : 0;
                        item.VatCode = model.VatCode;
                        item.Vat = purchaseOrder.VatCode.Contains("20% (VAT on Expenses)") ? item.TotalNet * 0.2 : purchaseOrder.VatCode.Contains("5% (VAT on Expenses)") ? item.TotalNet * 0.05 : 0;
                        item.TotalGross = (item.TotalNet + item.Vat.Value) - totalDiscount.Value;
                        item.Modified = DateTime.UtcNow;

                        unitOfWork.PurchaseOrderItem.Update(item);
                        await unitOfWork.SaveChangesAsync();
                    }
                }

                return Ok(purchaseOrder);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot create Purchase Order - {ex}");
                return BadRequest();
            }
        }

        [HttpPut("cancel/{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Cancel(Guid id)
        {
            if (!await purchaseOrderService.PurchaseOrderIdExistsAsync(id))
            {
                return BadRequest();
            }
            var purchaseOrderItems = unitOfWork.PurchaseOrderItem.Query(c => c.PurchaseOrderId == id && c.StockId.HasValue).Include(x => x.Stock).ToList();

            await purchaseOrderService.CancelAsync(id, purchaseOrderItems);
            return Ok();
        }

        [HttpPut("complete/{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> CompletePo(Guid id)
        {
            if (!await purchaseOrderService.PurchaseOrderIdExistsAsync(id))
            {
                return BadRequest();
            }

            await purchaseOrderService.CompleteAsync(id);
            return Ok();
        }

        [HttpPut("newRevision/{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> CreateRevision(Guid id)
        {
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);
            var number = await NumberSequenceUtility.GetNextPurchaseOrderNumber(unitOfWork);
            var raisedBy = user.Name;
            var raisedByEmail = user.EmailAddress;

            await purchaseOrderService.NewRevision(id, raisedBy, raisedByEmail);

            var purchaseOrder = unitOfWork.PurchaseOrder.Query(c => c.PurchaseOrderId == id).FirstOrDefault();

            purchaseOrder.ArchivedDate = DateTime.UtcNow;

            await unitOfWork.SaveChangesAsync();

            return Ok();
        }

        [HttpPost("emailSupplier")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> EmailSupplier(PurchaseOrderUpdateModel model)
        {
            try
            {
                List<Tuple<string, string, byte[]>> attachments = new List<Tuple<string, string, byte[]>>();
                string address;
                string username;
                string userJobTitle;
                var breaks = "<br/>";
                var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                      c.Type == ClaimTypes.Email);
                if (emailClaim == null)
                {
                    return Unauthorized();
                }

                var user = await userService.GetUserByEmailAsync(emailClaim.Value);
                var purchaseOrder = await purchaseOrderService.GetByNumberAsync(model.Number);
                model.PoEmailDate = DateTime.UtcNow;
                model.PoCcEmailAddress = model.CcAddress;
                model.PoEmailAddress = model.SupplierEmailAddress;
                await purchaseOrderService.PutAsync(purchaseOrder.PurchaseOrderId, model);
                var purchaseOrderReportModel = await purchaseOrderService.GetPurchaseOrderReportModel(purchaseOrder.PurchaseOrderId);
                var stream = reportService.GetPurchaseOrderReport(purchaseOrderReportModel, "Supplier");
                if (user.Location == "Oldmeldrum")
                {
                    var address1 = "Nauta House, Oldmeldrum, Aberdeenshire, AB51 0EZ";
                    var address2 = "T: +44(0)1651 872 101 | www.redroosterlifting.com";
                    address = $"{breaks}{address1}{breaks}{address2}";
                } else
                {
                    var address3 = "Unit 26, Kelvin Way Trading Estate, Kelvin Way, West Bromwich, West Midlands, B70 7TW";
                    var address4 = "T: +44 (0)121 525 4162 | www.redroosterlifting.com";
                    address = $"{breaks}{address3}{breaks}{address4}";
                }
                username = $"{breaks}{breaks}{user.Name}{breaks}";
                userJobTitle = $"{user.JobTitle}{breaks}{breaks}";
                var poPdfContentType = "application/pdf";
                var poPdf = new byte[] { };
                if (stream.Length > 0)
                {
                    using (var memoryStream = new MemoryStream())
                    {
                        stream.CopyTo(memoryStream);
                        poPdf = memoryStream.ToArray();
                    }
                    attachments.Add(new Tuple<string, string, byte[]>(poPdfContentType, $"{purchaseOrder.Number}.pdf", poPdf));
                }
                var supplierAddress = unitOfWork.SupplierAddress.Query().Where(s => s.SupplierId == purchaseOrder.SupplierId && s.IsHeadOfficeDefault).Include(x => x.SupplierContacts).FirstOrDefault();
                var supplierContactEmailAddress = supplierAddress.SupplierContacts.Select(c => c.EmailAddress).FirstOrDefault();

                await emailService.SendPurchaseOrderAttachments(model.EmailBody, model.EmailSubject, model.SupplierEmailAddress, username, userJobTitle, address, attachments, model.CcAddress);

                return Ok();
            } catch (Exception ex)
            {
                logger.LogError($"Cannot send Supplier Attachments - {ex}");
                return BadRequest();
            }
        }

        [HttpPost("targetDispatchReport")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> PrintTargetDispatchReport([FromBody] List<PurchaseOrderModel> pos)
        {
            //var pos = await purchaseOrderService.GetAllPurchaseOrders();
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);
            var currentUser = $"{user.Firstname} {user.Lastname}";
            var itemData = ExportUtility.ExportPurchaseOrderDispatchReport(pos, currentUser);
            return File(itemData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        }

        [HttpPut("archive/{id}")]
        [ProducesResponseType(200, Type = typeof(PurchaseOrderModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> ArchivePO(Guid id, [FromBody] PurchaseOrderUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }

            var po = await purchaseOrderService.GetAsync(id);

            if (po is null)
            {
                return BadRequest();
            }

            model.ArchivedDate = DateTime.Now;

            po = await purchaseOrderService.PutAsync(id, model);

            return Ok(po);
        }

        [HttpPut("unArchive/{id}")]
        [ProducesResponseType(200, Type = typeof(PurchaseOrderModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> UnArchivePO(Guid id, [FromBody] PurchaseOrderUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }

            var po = await purchaseOrderService.GetAsync(id);

            if (po is null)
            {
                return BadRequest();
            }

            model.ArchivedDate = null;

            po = await purchaseOrderService.PutAsync(id, model);

            return Ok(po);
        }

        [HttpPost("export")]
        [ProducesResponseType(200)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> GenerateReport(List<PurchaseOrderModel> models)
        {
            try
            {
                var document = new DocumentModel();
                var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
                if (emailClaim == null)
                {
                    return Unauthorized();
                }

                var user = await userService.GetUserByEmailAsync(emailClaim.Value);
                var currentUser = $"{user.Firstname} {user.Lastname}";
                var itemData = ExportUtility.ExportPurchaseOrderReport(models, currentUser);
                document = await documentService.PostAsync("Purchase_Order_Report.xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"{user.Firstname} {user.Lastname}", "Purchase Orders", null, null, null, false, false, false);
                var file = File(itemData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                await blobStorage.UploadAsync(document.DocumentId, itemData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                return Ok();
            } catch (Exception)
            {
                return NotFound();
            }
        }
        [HttpGet("downloadReport")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> DownloadReport()
        {
            try
            {
                var lastDocument = await unitOfWork.Document.Query().Where(t => t.Type == "Purchase Orders").OrderByDescending(c => c.Created).FirstOrDefaultAsync();
                var download = await blobStorage.DownloadAsync(lastDocument.DocumentId);
                var file = File(download.Item1, download.Item2);
                return file;
            } catch (Exception)
            {
                return NotFound();
            }
        }

        [HttpGet("salesCount/{id}")]
        [ProducesResponseType(200, Type = typeof(int))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetSalesCount(Guid id)
        {
            var total = 0;
            var purchaseOrderItems = unitOfWork.PurchaseOrderItem.Query(c => c.PurchaseOrderId == id && !c.PurchaseOrder.ArchivedDate.HasValue && c.OrderId.HasValue).Include(x => x.Order).ToList();
            if (purchaseOrderItems.Any())
            {
                var groupedOrders = purchaseOrderItems.GroupBy(c => c.OrderId);
                total = groupedOrders.Count();
            }

            return Ok(total);
        }

        [HttpGet("complaintCount/{id}")]
        [ProducesResponseType(200, Type = typeof(int))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetComplaintsCount(Guid id)
        {
            var total = 0;
            var complaints = unitOfWork.CustomerComplaint.Query(c => c.PurchaseOrderId.HasValue && c.PurchaseOrderId == id).Include(x => x.PurchaseOrder).ToList();
            if (complaints.Any())
            {
                var groupedOrders = complaints.GroupBy(c => c.PurchaseOrderId);
                total = groupedOrders.Count();
            }
            return Ok(total);
        }
    }
}