﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Red20.Model.Data.StockMovement;
using Red20.Service.Data.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Red20.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class StockAssemblyMovementItemController : ControllerBase
    {
        private IStockAssemblyMovementItemService stockAssemblyMovementItemService;
        private IStockService stockService;
        private IStockAssemblyService stockAssemblyService;
        private ILogger<AuthController> logger;
        private IUserService userService;

        public StockAssemblyMovementItemController(IStockAssemblyMovementItemService stockAssemblyMovementItemService, IStockService stockService,
            IStockAssemblyService stockAssemblyService, ILogger<AuthController> logger, IUserService userService = null)
        {
            this.stockAssemblyMovementItemService = stockAssemblyMovementItemService;
            this.logger = logger;
            this.stockService = stockService;
            this.stockAssemblyService = stockAssemblyService;
            this.userService = userService;
        }

        [HttpGet]
        [ProducesResponseType(200, Type = typeof(List<StockAssemblyMovementItemModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get()
        {
            var stockAssemblyMovementItems = await stockAssemblyMovementItemService.GetAsync();
            return Ok(stockAssemblyMovementItems);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(StockAssemblyMovementItemModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id)
        {
            var stockAssemblyMovementItem = await stockAssemblyMovementItemService.GetAsync(id);
            return Ok(stockAssemblyMovementItem);
        }

        [HttpGet("byStockAssemblyMovement/{id}")]
        [ProducesResponseType(200, Type = typeof(List<StockAssemblyMovementItemModel>))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetByStockAssemblyMovementAsync(Guid id)
        {
            var stockAssemblyMovementItems = await stockAssemblyMovementItemService.GetByStockAssemblyMovementAsync(id);
            return Ok(stockAssemblyMovementItems);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(StockAssemblyMovementItemModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody] StockAssemblyMovementItemUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }
            var stock = await stockService.GetAsync(model.StockId);
            var stockAssemblies = await stockAssemblyService.GetByStockIdAsync(model.StockId);
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);
            model.CreatedBy = $"{user.Firstname} {user.Lastname}";
            if (model.Type == "Assemble")
            {
                var canBuild = stockAssemblies.Any() ? stockAssemblies.Min(c => (int)(Math.Floor((double)(c.TotalAvailable / c.Quantity)))) : 0;
                if (canBuild < model.Quantity)
                {
                    return BadRequest();
                }
            }
            try
            {
                var stockAssemblyMovementItem = await stockAssemblyMovementItemService.PostAsync_NEW(model);
                return Ok(stockAssemblyMovementItem);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot create Purchase Order - {ex}");
                return BadRequest();
            }
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(StockAssemblyMovementItemModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(404)]
        public async Task<IActionResult> Put(Guid id, [FromBody] StockAssemblyMovementItemUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }

            if (await stockAssemblyMovementItemService.GetAsync(id) is null)
            {
                return NotFound();
            }

            var stockAssemblyMovementItem = await stockAssemblyMovementItemService.PutAsync(id, model);
            return Ok(stockAssemblyMovementItem);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Delete(Guid id)
        {
            if (await stockAssemblyMovementItemService.GetAsync(id) is null)
            {
                return NotFound();
            }

            await stockAssemblyMovementItemService.DeleteAsync(id);
            return Ok();
        }
    }
}
