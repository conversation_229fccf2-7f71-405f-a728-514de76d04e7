﻿using EFCore.BulkExtensions;
using GenFu;
using Red20.Data.Context;
using Red20.Model.Constant;
using Red20.Model.Entity;
using Red20.Util.Security;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Red20.Data.Initializer {
    public class DataInitializer {

        private DataContext context;
        private HashUtility hashUtility;        

        public DataInitializer(DataContext context, HashUtility hashUtility) {
            this.context = context;
            this.hashUtility = hashUtility;            
        }

        public void Initialize() {
            if (!context.Users.Any()) {                
                hashUtility.GetHashAndSaltString("password", out string password, out string salt);

                A.Configure<User>()
                .Fill(x => x.Password, password)
                .Fill(x => x.Salt, salt);

                var users = A.ListOf<User>(500);
                users.ForEach(x => x.UserId = Guid.NewGuid());
                users.Add(CreateUser("Arrash", "Nekonam", "<EMAIL>", "password"));
                users.Add(CreateUser("<PERSON>", "<PERSON>", "<EMAIL>", "password"));
                users.Add(CreateUser("<PERSON>", "Fraser", "<EMAIL>", "password"));
                users.Add(CreateUser("Margaret", "Gibb", "<EMAIL>", "password"));

                context.BulkInsertOrUpdate(users);                
            }

            if (!context.Customers.Any()) {                                
                var customers = A.ListOf<Customer>(500);
                customers.ForEach(x => x.CustomerId = Guid.NewGuid());

                context.BulkInsertOrUpdate(customers);
            }
        }

        private User CreateUser(string firstName, string lastName, string emailAddress, string password) {
            var user = new User {
                UserId = Guid.NewGuid(),
                Firstname = firstName,
                Lastname = lastName,
                EmailAddress = emailAddress,
                Enabled = true,
                Created = DateTime.UtcNow
            };

            hashUtility.GetHashAndSaltString(password, out string passwordHash, out string salt);

            user.Password = passwordHash;
            user.Salt = salt;            

            return user;

        }
    }
}

