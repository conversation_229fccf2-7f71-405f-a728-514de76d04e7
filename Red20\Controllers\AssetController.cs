﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Red20.Excel.Export;
using Red20.Model.Data;
using Red20.Model.Data.Document;
using Red20.Model.Entity;
using Red20.Service.Data.Interface;
using Red20.Service.Storage.Interface;
using Red20.Utility;

namespace Red20.Controllers {
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class AssetController : ControllerBase {

        private IAssetService assetService;
        private IUnitOfWork unitOfWork;
        private IHireEquipmentService hireEquipmentService;
        private IDocumentService documentService;
        private IStorageService blobStorage;
        private IUserService userService;
        private ILogger<AuthController> logger;

        public AssetController(
            IAssetService assetService,
            IHireEquipmentService hireEquipmentService,
            IUnitOfWork unitOfWork,
            IDocumentService documentService,
            IStorageService blobStorage,
            IUserService userService,
            ILogger<AuthController> logger) {

            this.assetService = assetService;
            this.unitOfWork = unitOfWork;
            this.hireEquipmentService = hireEquipmentService;
            this.documentService = documentService;
            this.blobStorage = blobStorage;
            this.userService = userService;
            this.logger = logger;
        }

        [HttpGet]
        [ProducesResponseType(200, Type = typeof(IList<AssetModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get() {
            var assets = assetService.GetAllAssets();
            return Ok(assets);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(AssetModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id) {

            var asset = await assetService.GetAsync(id);

            return Ok(asset);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(AssetModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody]AssetUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }

            model.Number = await NumberSequenceUtility.GetNextAssetNumber(unitOfWork);
            try {
                var asset = await assetService.PostAsync(model, DateTime.UtcNow);
                return Ok(asset);
            } catch(Exception ex) {

                return BadRequest();
            }
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(AssetModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Put(Guid id, [FromBody]AssetUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }

            var asset = await assetService.GetAsync(id);

            if (asset is null) {
                return BadRequest();
            }

            asset = await assetService.PutAsync(id, model);

            if(asset.HireEquipmentId.HasValue && asset.Status == "Disposed") {
                var hireEquipment = await hireEquipmentService.GetAsync(asset.HireEquipmentId.Value);
                hireEquipment.Archived = true;
                await hireEquipmentService.PutAsync(hireEquipment.HireEquipmentId, hireEquipment);
            }

            return Ok(asset);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Delete(Guid id) {

            await assetService.DeleteAsync(id);
            return Ok();
        }

        [HttpPut("pushToXero/{id}")]
        [ProducesResponseType(200, Type = typeof(AssetModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> PushToXero(Guid id) {

            try {
                await assetService.PushToXero(id);
            } catch {
                return BadRequest();
            }

            return Ok();
        }
        [HttpPost("assetReport")]
        [ProducesResponseType(200)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> PrintAssetListReport(List<AssetModel> models) {
            try
            {
                var document = new DocumentModel();
                var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                       c.Type == ClaimTypes.Email);
                if (emailClaim == null)
                {
                    return Unauthorized();
                }

                var user = await userService.GetUserByEmailAsync(emailClaim.Value);
                var currentUser = $"{user.Firstname} {user.Lastname}";
                var assetData = ExportUtility.ExportAssetReport(models);
                document = await documentService.PostAsync("Purchase_Order_Report.xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"{user.Firstname} {user.Lastname}", "Assets", null, null, null, false, false, false);
                var file = File(assetData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                await blobStorage.UploadAsync(document.DocumentId, assetData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                return Ok();
            }
            catch (Exception ex)
            {
                return NotFound();
            }
        }
        [HttpGet("downloadReport")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> DownloadReport()
        {
            try
            {
                var lastDocument = await unitOfWork.Document.Query().Where(t => t.Type == "Assets").OrderByDescending(c => c.Created).FirstOrDefaultAsync();
                var download = await blobStorage.DownloadAsync(lastDocument.DocumentId);
                var file = File(download.Item1, download.Item2);
                return file;
            }
            catch (Exception ex)
            {
                return NotFound();
            }
        }
    }
}