﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Internal;
using Microsoft.Extensions.Logging;
using Red20.Data.Model;
using Red20.Model.Data;
using Red20.Model.Data.PurchaseOrder;
using Red20.Model.Entity;
using Red20.Service.Data.Interface;

namespace Red20.Controllers {
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class PurchaseOrderItemSerialNumberController : ControllerBase {

        private IPurchaseOrderItemSerialNumberService purchaseOrderItemSerialNumberService;
        private IUserService userService;
        private ILogger<AuthController> logger;
        IUnitOfWork unitOfWork;


        public PurchaseOrderItemSerialNumberController(
            IPurchaseOrderItemSerialNumberService purchaseOrderItemSerialNumberService,
            IUserService userService,
            IUnitOfWork unitOfWork,
            ILogger<AuthController> logger) {

            this.purchaseOrderItemSerialNumberService = purchaseOrderItemSerialNumberService;
            this.userService = userService;
            this.logger = logger;
            this.unitOfWork = unitOfWork;
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(PurchaseOrderItemSerialNumberModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id) {
            var purchaseOrderItemSerialNumber = await purchaseOrderItemSerialNumberService.GetByIdAsync(id);
            return Ok(purchaseOrderItemSerialNumber);
        }

        [HttpGet("byPurchaseOrderItem/{id}")]
        [ProducesResponseType(200, Type = typeof(PurchaseOrderItemSerialNumberModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetByPurchaseOrderItem(Guid id) {
            var purchaseOrderItemSerialNumbers = await purchaseOrderItemSerialNumberService.GetByPurchaseOrderItemIdAsync(id);
            return Ok(purchaseOrderItemSerialNumbers);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(PurchaseOrderItemSerialNumberModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody] List<PurchaseOrderItemSerialNumberUpdateModel> models) {

            var serialNumbers = unitOfWork.PurchaseOrderItemSerialNumber.Query().ToList();

            var groupedSerialNumbers = models.GroupBy(g => new { g.PurchaseOrderItemId, g.SerialNumber }).Where(w => w.Count() > 1).ToList();

            if(groupedSerialNumbers.Count > 0) {
                return BadRequest();
            }

            foreach (var model in models) {
                var purchaseOrderItem = unitOfWork.PurchaseOrderItem.Query().Where(c => c.PurchaseOrderItemId == model.PurchaseOrderItemId).Include(x => x.Stock).FirstOrDefault();
                var stock = unitOfWork.Stock.Query().Where(x => x.StockId == purchaseOrderItem.StockId).Include(x => x.StockSerialTrackings).FirstOrDefault();

                if (serialNumbers.Any(c => c.PurchaseOrderItemId == model.PurchaseOrderItemId && c.SerialNumber == model.SerialNumber)) {
                    return BadRequest("Serial Number already exists");
                } else if (stock.StockSerialTrackings.Any()) {
                    if(stock.StockSerialTrackings.Any(c => c.SerialNumber == model.SerialNumber)) {
                        return BadRequest("Serial Number already exists");
                    }
                }
            }

            try {
                await purchaseOrderItemSerialNumberService.PostAsync(models);
                return Ok();
            } catch (Exception ex) {
                logger.LogError($"Cannot create Purchase Order Serial Number - {ex}");
                return BadRequest();
            }
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(PurchaseOrderItemSerialNumberModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Put(Guid id, [FromBody] PurchaseOrderItemSerialNumberUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }
            var purchaseOrderItemSerialNumber = await purchaseOrderItemSerialNumberService.GetByIdAsync(id);
            if (purchaseOrderItemSerialNumber is null) {
                return BadRequest();
            }

            purchaseOrderItemSerialNumber = await purchaseOrderItemSerialNumberService.PutAsync(id, model);

            return Ok(purchaseOrderItemSerialNumber);

        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Delete(Guid id) {

            await purchaseOrderItemSerialNumberService.DeleteAsync(id);
            return Ok();
        }
    }
}

