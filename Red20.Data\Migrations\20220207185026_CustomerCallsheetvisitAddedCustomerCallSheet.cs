﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Red20.Data.Migrations
{
    public partial class CustomerCallsheetvisitAddedCustomerCallSheet : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "CustomerCallSheetId",
                table: "CustomerCallSheetVisits",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.CreateIndex(
                name: "IX_CustomerCallSheetVisits_CustomerCallSheetId",
                table: "CustomerCallSheetVisits",
                column: "CustomerCallSheetId");

            migrationBuilder.AddForeignKey(
                name: "FK_CustomerCallSheetVisits_CustomerCallSheets_CustomerCallSheetId",
                table: "CustomerCallSheetVisits",
                column: "CustomerCallSheetId",
                principalTable: "CustomerCallSheets",
                principalColumn: "CustomerCallSheetId",
                onDelete: ReferentialAction.Cascade);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CustomerCallSheetVisits_CustomerCallSheets_CustomerCallSheetId",
                table: "CustomerCallSheetVisits");

            migrationBuilder.DropIndex(
                name: "IX_CustomerCallSheetVisits_CustomerCallSheetId",
                table: "CustomerCallSheetVisits");

            migrationBuilder.DropColumn(
                name: "CustomerCallSheetId",
                table: "CustomerCallSheetVisits");
        }
    }
}
