﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Red20.Model.Data;
using Red20.Model.Entity;
using Red20.Service.Data.Interface;

namespace Red20.Controllers {
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class AssetCategoryController : ControllerBase {

        private IAssetCategoryService assetCategoryService;
        private ILogger<AuthController> logger;

        public AssetCategoryController(
            IAssetCategoryService assetCategoryService,
            IUnitOfWork unitOfWork,
            ILogger<AuthController> logger) {

            this.assetCategoryService = assetCategoryService;
            this.logger = logger;
        }

        [HttpGet]
        [ProducesResponseType(200, Type = typeof(IList<AssetCategoryModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get() {
            var categories = assetCategoryService.GetAllAssetCategories();
            return Ok(categories);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(AssetCategoryModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id) {

            var assetCategory = await assetCategoryService.GetAsync(id);

            return Ok(assetCategory);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(AssetCategoryModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody]AssetCategoryUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }

            var assetCategory = await assetCategoryService.PostAsync(model);

            return Ok(assetCategory);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(AssetCategoryModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Put(Guid id, [FromBody]AssetCategoryUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }

            var assetCategory = await assetCategoryService.GetAsync(id);

            if (assetCategory is null) {
                return BadRequest();
            }

            assetCategory = await assetCategoryService.PutAsync(id, model);

            return Ok(assetCategory);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Delete(Guid id) {

            await assetCategoryService.DeleteAsync(id);
            return Ok();
        }
    }
}