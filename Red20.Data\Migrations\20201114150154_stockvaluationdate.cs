﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Red20.Data.Migrations
{
    public partial class stockvaluationdate : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "ValuationDate",
                table: "Stocks",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "ValuationPhysicalStock",
                table: "Stocks",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_StockTakes_CategoryId",
                table: "StockTakes",
                column: "CategoryId");

            migrationBuilder.CreateIndex(
                name: "IX_StockTakes_StockId",
                table: "StockTakes",
                column: "StockId");

            migrationBuilder.AddForeignKey(
                name: "FK_StockTakes_Categories_CategoryId",
                table: "StockTakes",
                column: "CategoryId",
                principalTable: "Categories",
                principalColumn: "CategoryId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_StockTakes_Stocks_StockId",
                table: "StockTakes",
                column: "StockId",
                principalTable: "Stocks",
                principalColumn: "StockId",
                onDelete: ReferentialAction.Cascade);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_StockTakes_Categories_CategoryId",
                table: "StockTakes");

            migrationBuilder.DropForeignKey(
                name: "FK_StockTakes_Stocks_StockId",
                table: "StockTakes");

            migrationBuilder.DropIndex(
                name: "IX_StockTakes_CategoryId",
                table: "StockTakes");

            migrationBuilder.DropIndex(
                name: "IX_StockTakes_StockId",
                table: "StockTakes");

            migrationBuilder.DropColumn(
                name: "ValuationDate",
                table: "Stocks");

            migrationBuilder.DropColumn(
                name: "ValuationPhysicalStock",
                table: "Stocks");
        }
    }
}
