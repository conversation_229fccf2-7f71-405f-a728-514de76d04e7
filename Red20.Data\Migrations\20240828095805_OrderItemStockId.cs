﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Red20.Data.Migrations
{
    public partial class OrderItemStockId : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "StockId",
                table: "OrderItems",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_OrderItems_StockId",
                table: "OrderItems",
                column: "StockId");

            migrationBuilder.AddForeignKey(
                name: "FK_OrderItems_Stocks_StockId",
                table: "OrderItems",
                column: "StockId",
                principalTable: "Stocks",
                principalColumn: "StockId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_OrderItems_Stocks_StockId",
                table: "OrderItems");

            migrationBuilder.DropIndex(
                name: "IX_OrderItems_StockId",
                table: "OrderItems");

            migrationBuilder.DropColumn(
                name: "StockId",
                table: "OrderItems");
        }
    }
}
