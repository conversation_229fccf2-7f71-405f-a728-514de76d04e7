﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Red20.Data.Migrations
{
    public partial class Callsheetvisitbranch : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "<PERSON>",
                table: "CustomerCallSheetVisits",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Email",
                table: "CustomerCallSheetVisits",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Name",
                table: "CustomerCallSheetVisits",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Phone",
                table: "CustomerCallSheetVisits",
                type: "nvarchar(max)",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "<PERSON>",
                table: "CustomerCallSheetVisits");

            migrationBuilder.DropColumn(
                name: "Email",
                table: "CustomerCallSheetVisits");

            migrationBuilder.DropColumn(
                name: "Name",
                table: "CustomerCallSheetVisits");

            migrationBuilder.DropColumn(
                name: "Phone",
                table: "CustomerCallSheetVisits");
        }
    }
}
