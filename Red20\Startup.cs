using AutoMapper;
using Elmah.Io.AspNetCore;
using Elmah.Io.Extensions.Logging;
using Hangfire;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.ResponseCompression;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using Red20.Data.Context;
using Red20.Data.Initializer;
using Red20.Email;
using Red20.Email.Interface;
using Red20.Model.Mapping;
using Red20.Service;
using Red20.Service.Data;
using Red20.Service.Data.Interface;
using Red20.Service.Email;
using Red20.Service.Email.Interface;
using Red20.Service.Security;
using Red20.Service.Security.Interface;
using Red20.Service.Storage;
using Red20.Service.Storage.Interface;
using Red20.Service.Utility;
using Red20.Service.Xero;
using Red20.Service.Xero.Interface;
using Red20.Settings;
using Red20.Storage;
using Red20.Storage.Interface;
using Red20.Util.Security;
using Red20.Util.Settings;
using Red20.Utility;
using Swashbuckle.AspNetCore.SwaggerUI;
using System;
using System.Collections.Generic;
using System.IO.Compression;
using System.Text;
using System.Threading.Tasks;
using Xero.NetStandard.OAuth2.Config;
using Elmah.Io.AspNetCore;
using Elmah.Io.Extensions.Logging;
using Microsoft.Extensions.Options;
using AuthMiddleware;

namespace Red20
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        public void ConfigureServices(IServiceCollection services)
        {

            // controllers and routing
            services.AddControllers();
            services.AddRouting(s => s.LowercaseUrls = true);
            services.AddHttpClient();

            services.AddCors();

            services.AddMvc()
                .SetCompatibilityVersion(CompatibilityVersion.Version_3_0)
                .AddNewtonsoftJson(opt => opt.SerializerSettings.ReferenceLoopHandling = ReferenceLoopHandling.Ignore);

            // settings
            var storageSettingsSection = services.Configure<AzureBlobStorageSettings>(Configuration.GetSection("AzureBlobStorage"));
            var emailSettingsSection = services.Configure<EmailSettings>(Configuration.GetSection("Email"));
            var securitySection = Configuration.GetSection("Security");
            var securitySettings = securitySection.Get<SecuritySettings>();
            var securitySettingsSection = services.Configure<SecuritySettings>(securitySection);
            services.Configure<ClientSettings>(Configuration.GetSection("Client"));
            services.Configure<XeroConfiguration>(Configuration.GetSection("XeroConfiguration"));
            services.AddOptions();

            // data context
            services.AddDbContext<DataContext>(cfg =>
            {
                cfg.UseSqlServer(Configuration.GetConnectionString("ConnectionString"));
            });

            services.AddTransient<DataInitializer>();

            // elmah
            services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
            services.AddSingleton<IConfigureOptions<ElmahIoProviderOptions>, DecorateElmahIoMessages>();

            services.Configure<ElmahIoOptions>(Configuration.GetSection("ElmahIo"));
            services.AddElmahIo();

            // email service
            services.AddTransient<IEmailManager, EmailManager>();
            services.AddScoped<IEmailService, EmailService>();

            // xero serivce
            services.AddScoped<XeroTokenUtility>();
            services.AddScoped<IXeroService, XeroService>();

            // storage service
            services.AddTransient<IStorage, AzureBlobStorage>();
            services.AddScoped<IStorageService, StorageService>();

            // unit of work
            services.AddScoped<IUnitOfWork, UnitOfWork>();

            // data services
            services.AddScoped<IAuth0UserService, Auth0UserService>();
            services.AddScoped<IAuthService, AuthService>();
            services.AddScoped<ICustomerService, CustomeService>();
            services.AddScoped<ICustomerCallSheetService, CustomerCallSheetService>();
            services.AddScoped<ICustomerCallSheetVisitService, CustomerCallSheetVisitService>();
            services.AddScoped<ICustomerAddressService, CustomerAddressService>();
            services.AddScoped<ICustomerContactService, CustomerContactService>();
            services.AddScoped<IDocumentService, DocumentService>();
            services.AddScoped<ISupplierService, SupplierService>();
            services.AddScoped<ISupplierAddressService, SupplierAddressService>();
            services.AddScoped<ISupplierActionLogService, SupplierActionLogService>();
            services.AddScoped<ISupplierContactService, SupplierContactService>();
            services.AddScoped<IUserService, UserService>();
            services.AddScoped<IUserCheckInService, UserCheckInService>();
            services.AddScoped<IUserCheckInCheckOutService, UserCheckInCheckOutService>();
            services.AddScoped<IUserPayrollAdjustmentItemService, UserPayrollAdjustmentItemService>();
            services.AddScoped<IUserPayrollAdjustmentService, UserPayrollAdjustmentService>();
            services.AddScoped<IUserPayrollRateService, UserPayrollRateService>();
            services.AddScoped<IUserRed20RateService, UserRed20RateService>();
            services.AddScoped<IUserTimesheetService, UserTimesheetService>();
            services.AddScoped<IUserTimesheetEntryService, UserTimesheetEntryService>();
            services.AddScoped<ICategoryService, CategoryService>();
            services.AddScoped<IAnalysisCodeService, AnalysisCodeService>();
            services.AddScoped<IPredefinedItemService, PredefinedItemService>();
            services.AddScoped<IPredefinedItemStockService, PredefinedItemStockService>();
            services.AddScoped<IEnquiryService, EnquiryService>();
            services.AddScoped<IStockService, StockService>();
            services.AddScoped<IStockFifoService, StockFifoService>();
            services.AddScoped<IStockTakeService, StockTakeService>();
            services.AddScoped<IStockAssemblyService, StockAssemblyService>();
            services.AddScoped<IStockSerialTrackingService, StockSerialTrackingService>();
            services.AddScoped<IStockSerialTrackingLogService, StockSerialTrackingLogService>();
            services.AddScoped<IStockTransactionService, StockTransactionService>();
            services.AddScoped<IStockMovementService, StockMovementService>();
            services.AddScoped<IStockMovementItemService, StockMovementItemService>();
            services.AddScoped<IStockAssemblyMovementItemService, StockAssemblyMovementItemService>();
            services.AddScoped<IStockAssemblyMovementService, StockAssemblyMovementService>();
            services.AddScoped<IQuoteService, QuoteService>();
            services.AddScoped<IQuoteItemService, QuoteItemService>();
            services.AddScoped<IPurchaseRequisitionService, PurchaseRequisitionService>();
            services.AddScoped<IPurchaseOrderService, PurchaseOrderService>();
            services.AddScoped<IPurchaseRequisitionItemService, PurchaseRequisitionItemService>();
            services.AddScoped<IPurchaseOrderItemService, PurchaseOrderItemService>();
            services.AddScoped<IPurchaseOrderCreditNoteItemService, PurchaseOrderCreditNoteItemService>();
            services.AddScoped<IPurchaseOrderItemLogService, PurchaseOrderItemLogService>();
            services.AddScoped<IPurchaseOrderItemSerialNumberService, PurchaseOrderItemSerialNumberService>();
            services.AddScoped<IHireEquipmentService, HireEquipmentService>();
            services.AddScoped<IHireEquipmentJobCardService, HireEquipmentJobCardService>();
            services.AddScoped<IOrderService, OrderService>();
            services.AddScoped<IOrderItemService, OrderItemService>();
            services.AddScoped<IOrderCreditNoteItemService, OrderCreditNoteItemService>();
            services.AddScoped<IOrderItemStockItemService, OrderItemStockItemService>();
            services.AddScoped<IOrderAssemblyService, OrderAssemblyService>();
            services.AddScoped<IOrderItemJobRecordSheetService, OrderItemJobRecordSheetService>();
            services.AddScoped<IOrderItemWorkSiteSheetService, OrderItemWorkSiteSheetService>();
            services.AddScoped<IOrderAssemblyHirePreparationSheetService, OrderAssemblyHirePreparationSheetService>();
            services.AddScoped<IOrderAssemblyHireEquipmentService, OrderAssemblyHireEquipmentService>();
            services.AddScoped<IEquipmentServiceHistoryService, EquipmentServiceHistoryService>();
            services.AddScoped<IEquipmentServiceStockItemService, EquipmentServiceStockItemService>();
            services.AddScoped<IJobService, JobService>();
            services.AddScoped<IJobInvoiceService, JobInvoiceService>();
            services.AddScoped<IJobInvoiceItemService, JobInvoiceItemService>();
            services.AddScoped<IAssetService, AssetService>();
            services.AddScoped<IAssetCategoryService, AssetCategoryService>();
            services.AddScoped<IAssetSubCategoryService, AssetSubCategoryService>();
            services.AddScoped<IOrderStockAllocationService, OrderStockAllocationService>();
            services.AddScoped<ICustomerComplaintService, CustomerComplaintService>();
            services.AddScoped<ICurrencyRateService, CurrencyRateService>();
            services.AddScoped<IAccountCodeService, AccountCodeService>();
            services.AddScoped<IOrderAssemblyDayRateService, OrderAssemblyDayRateService>();
            services.AddScoped<IPurchaseOrderStockService, PurchaseOrderStockService>();
            services.AddScoped<IStockTakeStockService, StockTakeStockService>();
            services.AddScoped<ISaleOrderItemStockService, SaleOrderItemStockService>();

            services.AddScoped<ReportService>();

            // singleton services
            services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();

            // transient services
            services.AddTransient<HashUtility>();
            services.AddTransient<TokenUtility>();

            // compression and caching
            services.Configure<GzipCompressionProviderOptions>(options =>
            {
                options.Level = CompressionLevel.Fastest;
            });

            services.AddResponseCompression();
            services.AddMemoryCache();
            services.AddResponseCaching();

            // auto mapper
            services.AddAutoMapper(typeof(Startup),
                typeof(CustomerMappingProfile),
                typeof(SupplierMappingProfile),
                typeof(UserMappingProfile));

            // authentication jwt
            var key = Encoding.ASCII.GetBytes(securitySettings.Key);

            services.AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(options =>
            {
                options.RequireHttpsMetadata = false;
                options.SaveToken = true;
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = false,
                    ValidateAudience = false,
                    ValidateLifetime = false,
                    RequireExpirationTime = true
                };

                options.Events = new JwtBearerEvents
                {
                    OnAuthenticationFailed = context =>
                    {
                        if (context.Exception.GetType() == typeof(SecurityTokenExpiredException))
                        {
                            context.Response.Headers.Add("Token-Expired", "true");
                        }
                        return Task.CompletedTask;
                    }
                };
            });

            //hangfire
            services.AddHangfire(hangfire =>
                hangfire.UseSqlServerStorage(Configuration.GetConnectionString("ConnectionString")));

            // Json Ignore Nulls
            services.AddControllers().AddJsonOptions(o => { o.JsonSerializerOptions.IgnoreNullValues = true; });

            // swagger
            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new Microsoft.OpenApi.Models.OpenApiInfo { Title = "Red 20 Api", Version = "v1" });

                c.EnableAnnotations();

                c.AddSecurityDefinition("Bearer", new Microsoft.OpenApi.Models.OpenApiSecurityScheme
                {
                    Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
                    Name = "Authorization",
                    In = ParameterLocation.Header,
                    Type = SecuritySchemeType.ApiKey
                });

                c.AddSecurityRequirement(new OpenApiSecurityRequirement() {
                    {
                        new OpenApiSecurityScheme {
                            Reference = new OpenApiReference {
                                Type = ReferenceType.SecurityScheme,
                                Id = "Bearer"
                            },
                            Scheme = "oauth2",
                            Name = "Bearer",
                            In = ParameterLocation.Header,
                    },
                        new List<string>()
                    }
                });
            });
        }

        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }

            if (env.IsProduction())
            {
                app.UseElmahIoExtensionsLogging();
            }

            using (var scope = app.ApplicationServices.CreateScope())
            {
                var context = scope.ServiceProvider.GetRequiredService<DataContext>();

                context.Database.Migrate();

                var dataInitializer = scope.ServiceProvider.GetService<DataInitializer>();
                dataInitializer.Initialize();
            }

            app.UseElmahIo();
            app.UseStaticFiles();
            app.UseHttpsRedirection();

            app.UseCors(x => x
                .AllowAnyOrigin()
                .AllowAnyMethod()
                .AllowAnyHeader());

            app.UseSwagger();

            //app.UseSwaggerUI();

            app.UseSwaggerUI(c =>
            {
                c.SwaggerEndpoint("/swagger/v1/swagger.json", "Red 20 API");
                c.RoutePrefix = string.Empty;
                c.DocExpansion(DocExpansion.None);
                c.DisplayRequestDuration();
            });

            app.UseRouting();

            app.UseMiddleware<AuthenticationMiddleware>();

            app.UseAuthentication();
            app.UseAuthorization();
            app.UseResponseCompression();
            app.UseResponseCaching();

            app.UseHangfireServer();
            app.UseHangfireDashboard("/jobs", new DashboardOptions
            {
                Authorization = new[] { new HangfireAuthorizationFilter() }
            });

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
            });

            app.Use(async (context, next) =>
            {
                context.Response.GetTypedHeaders().CacheControl =
                    new Microsoft.Net.Http.Headers.CacheControlHeaderValue()
                    {
                        Public = true,
                        MaxAge = TimeSpan.FromSeconds(60)
                    };
                context.Response.Headers[Microsoft.Net.Http.Headers.HeaderNames.Vary] =
                    new string[] { "Accept-Encoding" };

                await next();
            });

            // Stagger job timing to reduce concurrent processing
            RecurringJob.AddOrUpdate<TaskUtility>("Send Certificate Expiry Email", job => job.SendCertificateExpiryEmail(), "5 0 * * *");
            RecurringJob.AddOrUpdate<TaskUtility>("Send Insurance Certificate Expiry Email", job => job.SendInsuranceCertificateExpiryEmail(), "10 0 * * *");
            RecurringJob.AddOrUpdate<TaskUtility>("Send Currency Reminder Email", job => job.SendInsuranceCertificateExpiryEmail(), "15 0 * * *");
            // Space out 00:30 jobs
            RecurringJob.AddOrUpdate<TaskUtility>("Send Quote Reminder Email", job => job.SendQuoteReminderEmail(), "30 0 * * *");
            RecurringJob.AddOrUpdate<RecurringHireInvoice>("Recurring Invoice On Hire", job => job.OnHireAssembliesRecurringInvoice(), "35 0 * * *");
            RecurringJob.AddOrUpdate<TaskUtility>("Archive Purchase Orders", job => job.ArchivePurchaseOrders(), "40 0 * * *");
            RecurringJob.AddOrUpdate<TaskUtility>("Create Check In Users", job => job.CreateCheckInUsers(), "45 0 * * *");
            RecurringJob.AddOrUpdate<TaskUtility>("Check Follow up Call Sheets", job => job.CreateCheckFollowupCallSheets(), "50 0 * * *");
            Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense("Ngo9BigBOggjHTQxAR8/V1NAaF5cWWRCf1FpRmJGdld5fUVHYVZUTXxaS00DNHVRdkdmWXteeHVVQ2RYWEF0V0RWYU0=");
        }
    }
}
