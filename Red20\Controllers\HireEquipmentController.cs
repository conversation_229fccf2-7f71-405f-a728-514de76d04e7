﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Red20.Excel.Export;
using Red20.Model.Data;
using Red20.Model.Data.Document;
using Red20.Model.Data.HireEquipment;
using Red20.Service.Data.Interface;
using Red20.Service.Storage.Interface;
using Red20.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Red20.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class HireEquipmentController : ControllerBase
    {
        private IHireEquipmentService service;
        private IEquipmentServiceHistoryService serviceHistoryService;
        private IEquipmentServiceStockItemService serviceStockItemService;
        private IOrderService orderService;
        private IUserService userService;
        private IOrderAssemblyHireEquipmentService assemblyHireEquipmentService;
        private IAssetService assetService;
        private IDocumentService documentService;
        private IStorageService blobStorage;
        private ILogger<AuthController> logger;
        private IUnitOfWork unitOfWork;

        public HireEquipmentController(
            IUnitOfWork unitOfWork,
            IHireEquipmentService service,
            IEquipmentServiceHistoryService serviceHistoryService,
            IEquipmentServiceStockItemService serviceStockItemService,
            IOrderService orderService,
        IOrderAssemblyHireEquipmentService assemblyHireEquipmentService,
        IUserService userService,
        IAssetService assetService,
        IDocumentService documentService,
        IStorageService blobStorage,
        ILogger<AuthController> logger)
        {


            this.service = service;
            this.serviceHistoryService = serviceHistoryService;
            this.serviceStockItemService = serviceStockItemService;
            this.orderService = orderService;
            this.assemblyHireEquipmentService = assemblyHireEquipmentService;
            this.assetService = assetService;
            this.userService = userService;
            this.documentService = documentService;
            this.blobStorage = blobStorage;
            this.logger = logger;
            this.unitOfWork = unitOfWork;
        }

        [HttpGet]
        [ProducesResponseType(200, Type = typeof(IList<HireEquipmentModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get()
        {
            var equipments = await service.GetAllAsync();
            return Ok(equipments);
        }

        [HttpGet("getArchivedEquipments")]
        [ProducesResponseType(200, Type = typeof(IList<HireEquipmentModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetArchived()
        {
            var equipments = await service.GetAllArchivedAsync();
            return Ok(equipments);
        }

        [HttpGet("getAvailables")]
        [ProducesResponseType(200, Type = typeof(IList<HireEquipmentModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAvailableEquipments()
        {
            var equipments = await service.GetAllAvailableAsync();
            return Ok(equipments);
        }

        [HttpGet("byOrder/{id}")]
        [ProducesResponseType(200, Type = typeof(IList<HireEquipmentModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetByOrder(Guid id)
        {
            var result = new List<HireEquipmentModel>();

            var order = await orderService.GetAsync(id);
            if (order is null)
            {
                return BadRequest();
            }

            if (order.OrderAssemblies != null && order.OrderAssemblies.Any())
            {
                foreach (var assembly in order.OrderAssemblies)
                {
                    var assembyHireEquipments = await assemblyHireEquipmentService.GetByOrderAssembly(assembly.OrderAssemblyId);
                    if (assembyHireEquipments != null && assembyHireEquipments.Any())
                    {
                        var serialNumbers = assembyHireEquipments.Select(s => s.Serial).Distinct().ToList();

                        foreach (var serialNumber in serialNumbers)
                        {
                            var equipment = await service.GetBySerialNumber(serialNumber);
                            if (equipment != null)
                            {
                                equipment.OrderAssemblyId = assembly.OrderAssemblyId;
                                result.Add(equipment);
                            }
                        }
                    }
                }
            }

            return Ok(result);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(HireEquipmentModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id)
        {
            var serviceHistoryModels = new List<HireEquipmentServiceHistoryModel>();

            var equipment = await service.GetAsync(id);
            if (equipment != null)
            {
                var serviceHistories = await serviceHistoryService.GetByHireEquipmentId(equipment.HireEquipmentId);
                if (serviceHistories != null && serviceHistories.Any())
                {
                    serviceHistories = serviceHistories.OrderByDescending(o => o.Created).ToList();
                    int count = 0;
                    foreach (var serviceHistory in serviceHistories)
                    {
                        var model = new HireEquipmentServiceHistoryModel();
                        model.HireEquipmentSerialNumber = equipment.SerialNumber;
                        model.InspectedBy = serviceHistory.InspectedBy;
                        model.InspectedDate = serviceHistory.InspectedDateString;
                        model.Notes = serviceHistory.Notes;
                        model.OrderId = serviceHistory.OrderId.ToString();
                        model.OrderNumber = serviceHistory.OrderNumber;
                        model.OrderCreatedDate = serviceHistory.Order != null ? serviceHistory.Order.Created.ToString("dd/MM/yyyy") : string.Empty;
                        model.Id = count + 1;

                        var stockItems = await serviceStockItemService.GetByEquipmentServiceHistoryAsync(serviceHistory.EquipmentServiceHistoryId);
                        if (stockItems != null && stockItems.Any())
                        {
                            model.HireEquipmentStockItems = stockItems.Select(s => new HireEquipmentStockItemsModel
                            {
                                Id = s.EquipmentServiceStockItemId.ToString(),
                                StockCode = s.StockCode,
                                Quantity = s.Quantity.ToString()
                            }).ToList();
                        }
                        count++;
                        serviceHistoryModels.Add(model);
                    }
                    equipment.HireEquipmentServiceHistories = serviceHistoryModels;
                }
            }
            return Ok(equipment);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(HireEquipmentModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody] HireEquipmentUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }

            if (await service.HireEquipmentSerialNumberExistsAsync(model.SerialNumber))
            {
                return BadRequest("Serial Number already exists");
            }

            try
            {
                var equipment = await service.PostAsync(model);
                var asset = new AssetUpdateModel
                {
                    HireEquipmentId = equipment.HireEquipmentId,
                    SerialNumber = equipment.SerialNumber,
                    Description = equipment.Model,
                    Status = "Draft",
                    AssetSubCategoryId = model.AssetSubCategoryId,
                    BoughtDate = equipment.Date,
                    Number = await NumberSequenceUtility.GetNextAssetNumber(unitOfWork)
                };

                await assetService.PostAsync(asset, model.Date);

                return Ok(equipment);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot create Hire Equipment - {ex}");
                return BadRequest();
            }
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(HireEquipmentModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Put(Guid id, [FromBody] HireEquipmentUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }

            var equipment = await service.GetAsync(id);
            if (equipment is null)
            {
                return BadRequest();
            }

            if (equipment.Date != model.Date)
            {
                var asset = await unitOfWork.Asset.Query(q => q.HireEquipmentId == equipment.HireEquipmentId).FirstOrDefaultAsync();

                if (asset != null)
                {
                    asset.Created = model.Date.Value;
                    asset.BoughtDate = model.Date;
                    unitOfWork.Asset.Update(asset);
                    await unitOfWork.SaveChangesAsync();
                }
            }

            if (equipment.SerialNumber != model.SerialNumber && await service.HireEquipmentSerialNumberExistsAsync(model.SerialNumber))
            {
                return BadRequest();
            }

            if (model.Status == "Available" && !string.IsNullOrWhiteSpace(equipment.OrderNumber))
            {
                model.OrderNumber = null;
            }

            if (model.Status == "Sold/Scrapped")
            {
                var asset = await unitOfWork.Asset.Query(q => q.HireEquipmentId == equipment.HireEquipmentId).FirstOrDefaultAsync();

                if (asset != null)
                {
                    asset.Status = "Disposed";
                    asset.DisposedDate = DateTime.UtcNow;
                    unitOfWork.Asset.Update(asset);
                    await unitOfWork.SaveChangesAsync();
                }
            }
            try
            {
                equipment = await service.PutAsync(id, model);
                return Ok(equipment);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot update Hire Equipment - {ex}");
                return BadRequest();
            }
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Delete(Guid id)
        {
            var asset = await assetService.GetByHireEquipmentAsync(id);
            if (asset != null)
            {
                await assetService.DeleteAsync(asset.AssetId);
            }
            await service.DeleteAsync(id);
            return Ok();
        }

        [HttpPut("archive/{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> ArchiveEquipment(Guid id, [FromBody] HireEquipmentUpdateModel model)
        {
            var asset = await assetService.GetByHireEquipmentAsync(id);
            var hireEquipment = await service.GetAsync(id);
            hireEquipment.Archived = true;
            hireEquipment.ArchivedDate = DateTime.Now;
            hireEquipment.ArchiveComments = model.ArchiveComments;
            hireEquipment.AssetSubCategoryId = model.AssetSubCategoryId;
            await service.PutAsync(hireEquipment.HireEquipmentId, hireEquipment);
            asset.Status = "Disposed";
            asset.ArchivedDate = DateTime.Now;
            await assetService.PutAsync(asset.AssetId, asset);

            return Ok();
        }
        [HttpPut("unArchive/{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> UnArchiveEquipment(Guid id, [FromBody] HireEquipmentUpdateModel model)
        {
            var hireEquipment = await service.GetAsync(id);
            hireEquipment.Archived = false;
            hireEquipment.ArchivedDate = null;
            hireEquipment.ArchiveComments = null;
            await service.PutAsync(id, hireEquipment);
            var asset = await assetService.GetByHireEquipmentAsync(id);
            asset.Status = "Draft";
            asset.ArchivedDate = null;
            await assetService.PutAsync(asset.AssetId, asset);

            return Ok();
        }

        [HttpGet("getAsset/{id}")]
        [ProducesResponseType(200, Type = typeof(IList<HireEquipmentModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAssetHireEquipment(Guid id)
        {
            var asset = await assetService.GetByHireEquipmentAsync(id);
            return Ok(asset);
        }

        [HttpPost("hireEquipmentExport")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Export([FromBody] HireEquipmentExportIdsModel model)
        {
            try
            {
                var equipments = new List<HireEquipmentModel>();

                if (model != null && model.Ids != null && model.Ids.Any())
                {

                    foreach (var id in model.Ids)
                    {
                        var equipment = await service.GetForExportAsync(id);
                        var asset = unitOfWork.Asset.Query().Where(c => c.HireEquipmentId.HasValue && c.HireEquipment.HireEquipmentId == id).FirstOrDefault();
                        if (equipment != null)
                        {
                            equipment.AssetNumber = asset != null ? asset.Number : "N/A";
                            equipments.Add(equipment);
                        }
                    }

                    byte[] data = ExportUtility.ExportHireEquipments(equipments);
                    var file = File(data, "application/vnd.ms-excel");
                    return file;
                }

                return Ok();
            } catch (Exception ex)
            {
                return BadRequest($"{ex}");
            }
        }


        [HttpGet("printServiceHistory/{id}")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> PrintServiceHistory(Guid id)
        {
            try
            {
                var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
                if (emailClaim == null)
                {
                    return Unauthorized();
                }

                var user = await userService.GetUserByEmailAsync(emailClaim.Value);
                var document = new DocumentModel();
                var userName = $"{user.Firstname} {user.Lastname}";
                var equipmentServiceHistories = unitOfWork.EquipmentServiceHistory.Query(c => c.HireEquipmentId == id).Include(x => x.Equipment).Include(x => x.EquipmentServiceStockItems).ThenInclude(c => c.Stock).OrderByDescending(c => c.Created).ToList();

                var data = ExportUtility.ExportEquipmentServiceHistoryReport(equipmentServiceHistories, userName);

                document = await documentService.PostAsync("Equipment_Service_History_Report.xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"{user.Firstname} {user.Lastname}", "Import", null, null, null, false, false, true, DateTime.Now);
                var file = File(data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                await blobStorage.UploadAsync(document.DocumentId, data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                return file;
            } catch (Exception ex)
            {
                return NotFound();
            }
        }
    }
}

