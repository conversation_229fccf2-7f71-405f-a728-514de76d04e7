﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Red20.Data.Migrations
{
    public partial class ADdADjustmentDateToJobInvoiceAndJobInvoiceItemTables : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "AdjustmentDate",
                table: "JobInvoices",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "AdjustmentDate",
                table: "JobInvoiceItem",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AdjustmentDate",
                table: "JobInvoices");

            migrationBuilder.DropColumn(
                name: "AdjustmentDate",
                table: "JobInvoiceItem");
        }
    }
}
