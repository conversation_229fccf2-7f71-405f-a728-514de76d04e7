﻿using AutoMapper;
using Red20.Model.Data.Order;
using Syncfusion.DocIO;
using Syncfusion.DocIO.DLS;
using Syncfusion.DocIORenderer;
using Syncfusion.Drawing;
using Syncfusion.Pdf;
using Syncfusion.Pdf.Graphics;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;

namespace Red20.Service {
    public class ReportService {
        private IMapper mapper;

        public ReportService(IMapper mapper) {
            this.mapper = mapper;
        }

        public MemoryStream GetReport<T>(
            T model,
            Dictionary<string, byte[]> images = null,
            bool isPreview = false,
            bool isHire = false,
            bool isPurchaseReq = false,
            bool isPurchaseOrder = false) {

            Assembly assembly = Assembly.GetExecutingAssembly();
            MemoryStream pdfStream = new MemoryStream();
            var quotePreviewSale = assembly.GetManifestResourceStream("Red20.Content.Templates.QuotePreviewSale.docx");
            try {
                using (var fileStream = isPurchaseOrder ? assembly.GetManifestResourceStream("Red20.Content.Templates.PurchaseOrderPdf.docx") : isPurchaseReq ? assembly.GetManifestResourceStream("Red20.Content.Templates.PurchaseRequisitionPdf.docx") : isHire ? assembly.GetManifestResourceStream("Red20.Content.Templates.QuotePreview.docx") : quotePreviewSale) {

                    using (WordDocument document = new WordDocument(fileStream, FormatType.Docx)) {
                        document.EnsureMinimal();

                        var fields = GetNames(model);
                        var values = GetValues(model);
                        var lists = GetLists(model);

                        document.MailMerge.RemoveEmptyParagraphs = true;
                        document.MailMerge.ClearFields = true;

                        document.MailMerge.MergeField += MailMerge_MergeField;
                        //document.MailMerge.MergeField += CheckEmptyFieldValue;

                        //B: Try to loop through each type of list in the model, to merge the data table automatically
                        foreach (var list in lists) {
                            MailMergeDataTable dataTable = new MailMergeDataTable(list.Key, list.Value);
                            document.MailMerge.ExecuteNestedGroup(dataTable);
                        }

                        //RemoveMergeField(document, "Street1");
                        document.MailMerge.Execute(fields.ToArray(), values.ToArray());

                        MemoryStream stream = new MemoryStream();
                        document.Save(stream, FormatType.Docx);

                        stream.Position = 0;

                        DocIORenderer render = new DocIORenderer();
                        Syncfusion.Pdf.PdfDocument pdfDocument = render.ConvertToPDF(document);
                        render.Dispose();

                        if (isPreview) {
                            var pagesCount = pdfDocument.Pages.Count;
                            var pages = pdfDocument.Pages;
                            for (var x = 0; x < pages.Count; x++) {
                                Syncfusion.Pdf.Graphics.PdfGraphics graphics = pages[x].Graphics;
                                Syncfusion.Pdf.Graphics.PdfFont font = new PdfStandardFont(PdfFontFamily.Helvetica, 36);
                                Syncfusion.Pdf.Graphics.PdfGraphicsState state = graphics.Save();
                                graphics.SetTransparency(0.25f);
                                graphics.RotateTransform(-40);
                                graphics.DrawString("PREVIEW ONLY NOT FOR ISSUE", font, PdfPens.Red, PdfBrushes.Red, new PointF(-350, 450));
                            }
                        }

                        pdfDocument.Save(pdfStream);

                        document.Close();
                        pdfDocument.Close(true);
                        pdfStream.Position = 0;
                    }
                }
            } catch (Exception ex) {
                var x = ex;
            }
            return pdfStream;
        }

        public MemoryStream GetPurchaseOrderReport<T>(
         T model, string type) {

            Assembly assembly = Assembly.GetExecutingAssembly();
            MemoryStream pdfStream = new MemoryStream();

            try {
                using (var fileStream = type =="Supplier" ? assembly.GetManifestResourceStream("Red20.Content.Templates.PurchaseOrderPdf.docx") : type == "Office" ? assembly.GetManifestResourceStream("Red20.Content.Templates.PurchaseOrderOfficePdf.docx") : assembly.GetManifestResourceStream("Red20.Content.Templates.PurchaseOrderWarehousePdf.docx")) {

                    using (WordDocument document = new WordDocument(fileStream, FormatType.Docx)) {
                        document.EnsureMinimal();

                        var fields = GetNames(model);
                        var values = GetValues(model);
                        var lists = GetLists(model);

                        document.MailMerge.RemoveEmptyParagraphs = true;
                        document.MailMerge.ClearFields = true;

                        document.MailMerge.MergeField += MailMerge_MergeField;

                        //B: Try to loop through each type of list in the model, to merge the data table automatically
                        foreach (var list in lists) {
                            MailMergeDataTable dataTable = new MailMergeDataTable(list.Key, list.Value);
                            document.MailMerge.ExecuteNestedGroup(dataTable);
                        }

                        //RemoveMergeField(document, "Street1");
                        document.MailMerge.Execute(fields.ToArray(), values.ToArray());

                        MemoryStream stream = new MemoryStream();
                        document.Save(stream, FormatType.Docx);

                        stream.Position = 0;

                        DocIORenderer render = new DocIORenderer();
                        Syncfusion.Pdf.PdfDocument pdfDocument = render.ConvertToPDF(document);
                        render.Dispose();

                        pdfDocument.Save(pdfStream);

                        document.Close();
                        pdfDocument.Close(true);
                        pdfStream.Position = 0;
                    }
                }

            } catch (Exception ex) {
                var x = ex;
            }
            return pdfStream;
        }

        public MemoryStream GetAllPurchaseOrderReport<T>(
         T model) {

            Assembly assembly = Assembly.GetExecutingAssembly();
            PdfDocument finalDoc = new PdfDocument();
            MemoryStream pdfStream = new MemoryStream();
            MemoryStream pdfOfficeStream = new MemoryStream();
            MemoryStream pdfWarehouseStream = new MemoryStream();
            MemoryStream finalStream = new MemoryStream();
            Stream[] streams = { pdfStream, pdfOfficeStream, pdfWarehouseStream };

            try {
                using (var fileStream = assembly.GetManifestResourceStream("Red20.Content.Templates.PurchaseOrderPdf.docx")) {

                    using (WordDocument document = new WordDocument(fileStream, FormatType.Docx)) {
                        document.EnsureMinimal();

                        var fields = GetNames(model);
                        var values = GetValues(model);
                        var lists = GetLists(model);

                        document.MailMerge.RemoveEmptyParagraphs = true;
                        document.MailMerge.ClearFields = true;

                        document.MailMerge.MergeField += MailMerge_MergeField;

                        //B: Try to loop through each type of list in the model, to merge the data table automatically
                        foreach (var list in lists) {
                            MailMergeDataTable dataTable = new MailMergeDataTable(list.Key, list.Value);
                            document.MailMerge.ExecuteNestedGroup(dataTable);
                        }

                        //RemoveMergeField(document, "Street1");
                        document.MailMerge.Execute(fields.ToArray(), values.ToArray());

                        MemoryStream stream = new MemoryStream();
                        document.Save(stream, FormatType.Docx);

                        stream.Position = 0;

                        DocIORenderer render = new DocIORenderer();
                        Syncfusion.Pdf.PdfDocument pdfDocument = render.ConvertToPDF(document);
                        render.Dispose();

                        pdfDocument.Save(pdfStream);

                        document.Close();
                        pdfDocument.Close(true);
                        pdfStream.Position = 0;
                    }
                }
                using (var fileStream = assembly.GetManifestResourceStream("Red20.Content.Templates.PurchaseOrderOfficePdf.docx")) {

                    using (WordDocument document = new WordDocument(fileStream, FormatType.Docx)) {
                        document.EnsureMinimal();

                        var fields = GetNames(model);
                        var values = GetValues(model);
                        var lists = GetLists(model);

                        document.MailMerge.RemoveEmptyParagraphs = true;
                        document.MailMerge.ClearFields = true;

                        document.MailMerge.MergeField += MailMerge_MergeField;

                        //B: Try to loop through each type of list in the model, to merge the data table automatically
                        foreach (var list in lists) {
                            MailMergeDataTable dataTable = new MailMergeDataTable(list.Key, list.Value);
                            document.MailMerge.ExecuteNestedGroup(dataTable);
                        }

                        //RemoveMergeField(document, "Street1");
                        document.MailMerge.Execute(fields.ToArray(), values.ToArray());

                        MemoryStream stream = new MemoryStream();
                        document.Save(stream, FormatType.Docx);

                        stream.Position = 0;

                        DocIORenderer render = new DocIORenderer();
                        Syncfusion.Pdf.PdfDocument pdfDocument = render.ConvertToPDF(document);
                        render.Dispose();

                        pdfDocument.Save(pdfOfficeStream);

                        document.Close();
                        pdfDocument.Close(true);
                        pdfOfficeStream.Position = 0;

                    }
                }
                using (var fileStream = assembly.GetManifestResourceStream("Red20.Content.Templates.PurchaseOrderWarehousePdf.docx")) {

                    using (WordDocument document = new WordDocument(fileStream, FormatType.Docx)) {
                        document.EnsureMinimal();

                        var fields = GetNames(model);
                        var values = GetValues(model);
                        var lists = GetLists(model);

                        document.MailMerge.RemoveEmptyParagraphs = true;
                        document.MailMerge.ClearFields = true;

                        document.MailMerge.MergeField += MailMerge_MergeField;

                        //B: Try to loop through each type of list in the model, to merge the data table automatically
                        foreach (var list in lists) {
                            MailMergeDataTable dataTable = new MailMergeDataTable(list.Key, list.Value);
                            document.MailMerge.ExecuteNestedGroup(dataTable);
                        }

                        //RemoveMergeField(document, "Street1");
                        document.MailMerge.Execute(fields.ToArray(), values.ToArray());

                        MemoryStream stream = new MemoryStream();
                        document.Save(stream, FormatType.Docx);

                        stream.Position = 0;

                        DocIORenderer render = new DocIORenderer();
                        Syncfusion.Pdf.PdfDocument pdfDocument = render.ConvertToPDF(document);
                        render.Dispose();

                        pdfDocument.Save(pdfWarehouseStream);

                        document.Close();
                        pdfDocument.Close(true);
                        pdfWarehouseStream.Position = 0;

                    }
                }

                PdfDocumentBase.Merge(finalDoc, streams);

                finalDoc.Save(finalStream);
                finalStream.Position = 0;
                //finalDoc.Close(true);

                pdfStream.Dispose();
                pdfOfficeStream.Dispose();
                pdfWarehouseStream.Dispose();

            } catch (Exception ex) {
                var x = ex;
            }
            return finalStream;
        }


        public MemoryStream GetOrderReport<T>(
            T model,
            Dictionary<string, byte[]> images = null,
            bool isPreview = false,
            bool isHire = false,
            bool isInternal = false) {

            Assembly assembly = Assembly.GetExecutingAssembly();
            MemoryStream pdfStream = new MemoryStream();
            try {
                using (var fileStream = 
                    isHire && !isInternal ? assembly.GetManifestResourceStream("Red20.Content.Templates.HireOrder.docx") : 
                    !isHire && !isInternal ? assembly.GetManifestResourceStream("Red20.Content.Templates.SaleOrder.docx") : 
                    !isHire && isInternal ? assembly.GetManifestResourceStream("Red20.Content.Templates.InternalSaleOrder.docx") : 
                    assembly.GetManifestResourceStream("Red20.Content.Templates.SaleOrder.docx")) {

                    using (WordDocument document = new WordDocument(fileStream, FormatType.Docx)) {
                        document.EnsureMinimal();

                        var fields = GetNames(model);
                        var values = GetValues(model);
                        var lists = GetLists(model);

                        document.MailMerge.RemoveEmptyParagraphs = true;
                        document.MailMerge.ClearFields = true;

                        document.MailMerge.MergeField += MailMerge_MergeField;

                        foreach (var list in lists) {
                            MailMergeDataTable dataTable = new MailMergeDataTable(list.Key, list.Value);
                            document.MailMerge.ExecuteNestedGroup(dataTable);
                        }

                        document.MailMerge.Execute(fields.ToArray(), values.ToArray());

                        document.UpdateDocumentFields();

                        MemoryStream stream = new MemoryStream();
                        document.Save(stream, FormatType.Docx);

                        stream.Position = 0;

                        DocIORenderer render = new DocIORenderer();
                        Syncfusion.Pdf.PdfDocument pdfDocument = render.ConvertToPDF(document);
                        render.Dispose();

                        if (isPreview) {
                            var pagesCount = pdfDocument.Pages.Count;
                            var pages = pdfDocument.Pages;
                            for (var x = 0; x < pages.Count; x++) {
                                Syncfusion.Pdf.Graphics.PdfGraphics graphics = pages[x].Graphics;
                                Syncfusion.Pdf.Graphics.PdfFont font = new PdfStandardFont(PdfFontFamily.Helvetica, 36);
                                Syncfusion.Pdf.Graphics.PdfGraphicsState state = graphics.Save();
                                graphics.SetTransparency(0.25f);
                                graphics.RotateTransform(-40);
                                graphics.DrawString("PREVIEW ONLY NOT FOR ISSUE", font, PdfPens.Red, PdfBrushes.Red, new PointF(-350, 450));
                            }
                        }

                        pdfDocument.Save(pdfStream);

                        document.Close();
                        pdfDocument.Close(true);
                        pdfStream.Position = 0;
                    }
                }
            } catch (Exception ex) {
                var x = ex;
            }
            return pdfStream;
        }

        public MemoryStream GetProformaOrderReport<T>(T model) {

            Assembly assembly = Assembly.GetExecutingAssembly();
            MemoryStream pdfStream = new MemoryStream();
            try {
                using (var fileStream = assembly.GetManifestResourceStream("Red20.Content.Templates.ProformaSaleOrder.docx")) {

                    using (WordDocument document = new WordDocument(fileStream, FormatType.Docx)) {
                        document.EnsureMinimal();

                        var fields = GetNames(model);
                        var values = GetValues(model);
                        var lists = GetLists(model);

                        document.MailMerge.RemoveEmptyParagraphs = true;
                        document.MailMerge.ClearFields = true;

                        document.MailMerge.MergeField += MailMerge_MergeField;

                        foreach (var list in lists) {
                            MailMergeDataTable dataTable = new MailMergeDataTable(list.Key, list.Value);
                            document.MailMerge.ExecuteNestedGroup(dataTable);
                        }

                        document.MailMerge.Execute(fields.ToArray(), values.ToArray());

                        MemoryStream stream = new MemoryStream();
                        document.Save(stream, FormatType.Docx);

                        stream.Position = 0;

                        DocIORenderer render = new DocIORenderer();
                        Syncfusion.Pdf.PdfDocument pdfDocument = render.ConvertToPDF(document);
                        render.Dispose();

                        pdfDocument.Save(pdfStream);

                        document.Close();
                        pdfDocument.Close(true);
                        pdfStream.Position = 0;
                    }
                }
            } catch (Exception ex) {
                var x = ex;
            }
            return pdfStream;
        }

        public MemoryStream GetWorksJobRecordSheetLC<T>(T model)
        {

            Assembly assembly = Assembly.GetExecutingAssembly();
            MemoryStream pdfStream = new MemoryStream();
            try
            {
                using (var fileStream = assembly.GetManifestResourceStream("Red20.Content.Templates.WorksJobRecordSheetLC.docx"))
                {

                    using (WordDocument document = new WordDocument(fileStream, FormatType.Docx))
                    {
                        document.EnsureMinimal();

                        var fields = GetNames(model);
                        var values = GetValues(model);
                        var lists = GetLists(model);

                        document.MailMerge.RemoveEmptyParagraphs = true;
                        document.MailMerge.ClearFields = true;

                        document.MailMerge.MergeField += MailMerge_MergeField;

                        foreach (var list in lists)
                        {
                            MailMergeDataTable dataTable = new MailMergeDataTable(list.Key, list.Value);
                            document.MailMerge.ExecuteNestedGroup(dataTable);
                        }

                        document.MailMerge.Execute(fields.ToArray(), values.ToArray());

                        MemoryStream stream = new MemoryStream();
                        document.Save(stream, FormatType.Docx);

                        stream.Position = 0;

                        DocIORenderer render = new DocIORenderer();
                        Syncfusion.Pdf.PdfDocument pdfDocument = render.ConvertToPDF(document);
                        render.Dispose();

                        pdfDocument.Save(pdfStream);

                        document.Close();
                        pdfDocument.Close(true);
                        pdfStream.Position = 0;
                    }
                }
            }
            catch (Exception ex)
            {
                var x = ex;
            }
            return pdfStream;
        }

        public MemoryStream GetJobRecordSheetReport<T>(T model, Dictionary<string, byte[]> images = null, bool hasStockItems = true, string sheetType = "") {

            Assembly assembly = Assembly.GetExecutingAssembly();
            MemoryStream pdfStream = new MemoryStream();
            try {
                using (var fileStream = !string.IsNullOrWhiteSpace(sheetType) ?
                                        assembly.GetManifestResourceStream("Red20.Content.Templates.SalesWorkSiteSheet.docx") :
                                        hasStockItems ? 
                                            assembly.GetManifestResourceStream("Red20.Content.Templates.SalesJobRecordSheet.docx") : 
                                            assembly.GetManifestResourceStream("Red20.Content.Templates.SalesJobRecordSheetNoStockItems.docx")) {

                    using (WordDocument document = new WordDocument(fileStream, FormatType.Docx)) {
                        document.EnsureMinimal();

                        var fields = GetNames(model);
                        var values = GetValues(model);
                        var lists = GetLists(model);

                        document.MailMerge.RemoveEmptyParagraphs = true;
                        document.MailMerge.ClearFields = true;

                        document.MailMerge.MergeField += MailMerge_MergeField;

                        //B: Try to loop through each type of list in the model, to merge the data table automatically
                        foreach (var list in lists) {
                            MailMergeDataTable dataTable = new MailMergeDataTable(list.Key, list.Value);
                            document.MailMerge.ExecuteNestedGroup(dataTable);
                        }

                        document.MailMerge.Execute(fields.ToArray(), values.ToArray());

                        MemoryStream stream = new MemoryStream();
                        document.Save(stream, FormatType.Docx);

                        stream.Position = 0;

                        DocIORenderer render = new DocIORenderer();
                        Syncfusion.Pdf.PdfDocument pdfDocument = render.ConvertToPDF(document);
                        render.Dispose();

                        pdfDocument.Save(pdfStream);

                        document.Close();
                        pdfDocument.Close(true);
                        pdfStream.Position = 0;
                    }
                }
            } catch (Exception ex) {
                var x = ex;
            }
            return pdfStream;
        }

        public MemoryStream GetWorkSiteSheetReport<T>(T model, Dictionary<string, byte[]> images = null) {

            Assembly assembly = Assembly.GetExecutingAssembly();
            MemoryStream resultStream = new MemoryStream();
            try {
                using (var fileStream = assembly.GetManifestResourceStream("Red20.Content.Templates.SalesWorkSiteSheet.docx")) {

                    using (WordDocument document = new WordDocument(fileStream, FormatType.Docx)) {
                        document.EnsureMinimal();

                        var fields = GetNames(model);
                        var values = GetValues(model);
                        var lists = GetLists(model);

                        document.MailMerge.RemoveEmptyParagraphs = true;
                        document.MailMerge.ClearFields = true;

                        document.MailMerge.MergeField += MailMerge_MergeField;

                        //B: Try to loop through each type of list in the model, to merge the data table automatically
                        foreach (var list in lists) {
                            MailMergeDataTable dataTable = new MailMergeDataTable(list.Key, list.Value);
                            document.MailMerge.ExecuteNestedGroup(dataTable);
                        }

                        document.MailMerge.Execute(fields.ToArray(), values.ToArray());

                        MemoryStream stream = new MemoryStream();
                        document.Save(resultStream, FormatType.Docx);

                        resultStream.Position = 0;
                        document.Close();
                    }
                }
            } catch (Exception ex) {
                var x = ex;
            }
            return resultStream;
        }


        public MemoryStream GetHirePrepSheetReport<T>(T model, string type, Dictionary<string, byte[]> images = null) {

            Assembly assembly = Assembly.GetExecutingAssembly();
            MemoryStream pdfStream = new MemoryStream();
            try {
                using (var fileStream =type == "AirHoist" ? assembly.GetManifestResourceStream("Red20.Content.Templates.HirePrepSheetAirHoists.docx") :
                                                            assembly.GetManifestResourceStream("Red20.Content.Templates.HirePrepSheetLoadCell.docx")) {

                    using (WordDocument document = new WordDocument(fileStream, FormatType.Docx)) {
                        document.EnsureMinimal();

                        var fields = GetNames(model);
                        var values = GetValues(model);
                        var lists = GetLists(model);

                        document.MailMerge.RemoveEmptyParagraphs = true;
                        document.MailMerge.ClearFields = true;

                        document.MailMerge.MergeField += MailMerge_MergeField;

                        //B: Try to loop through each type of list in the model, to merge the data table automatically
                        foreach (var list in lists) {
                            MailMergeDataTable dataTable = new MailMergeDataTable(list.Key, list.Value);
                            document.MailMerge.ExecuteNestedGroup(dataTable);
                        }

                        document.MailMerge.Execute(fields.ToArray(), values.ToArray());

                        MemoryStream stream = new MemoryStream();
                        document.Save(stream, FormatType.Docx);

                        stream.Position = 0;

                        DocIORenderer render = new DocIORenderer();
                        Syncfusion.Pdf.PdfDocument pdfDocument = render.ConvertToPDF(document);
                        render.Dispose();

                        pdfDocument.Save(pdfStream);

                        document.Close();
                        pdfDocument.Close(true);
                        pdfStream.Position = 0;
                    }
                }
            } catch (Exception ex) {
                var x = ex;
            }
            return pdfStream;
        }

        public MemoryStream GetSaleDeliveryNote<T>(T model, Dictionary<string, byte[]> images = null) {

            Assembly assembly = Assembly.GetExecutingAssembly();
            MemoryStream pdfStream = new MemoryStream();
            try {
                using (var fileStream = assembly.GetManifestResourceStream("Red20.Content.Templates.SaleDeliveryNote.docx")) {
                    using (WordDocument document = new WordDocument(fileStream, FormatType.Docx)) {
                        document.EnsureMinimal();

                        var fields = GetNames(model);
                        var values = GetValues(model);
                        var lists = GetLists(model);

                        document.MailMerge.RemoveEmptyParagraphs = true;
                        document.MailMerge.ClearFields = true;

                        document.MailMerge.MergeField += MailMerge_MergeField;

                        //B: Try to loop through each type of list in the model, to merge the data table automatically
                        foreach (var list in lists) {
                            MailMergeDataTable dataTable = new MailMergeDataTable(list.Key, list.Value);
                            document.MailMerge.ExecuteNestedGroup(dataTable);
                        }

                        document.MailMerge.Execute(fields.ToArray(), values.ToArray());

                        MemoryStream stream = new MemoryStream();
                        document.Save(stream, FormatType.Docx);

                        stream.Position = 0;

                        DocIORenderer render = new DocIORenderer();
                        Syncfusion.Pdf.PdfDocument pdfDocument = render.ConvertToPDF(document);
                        render.Dispose();

                        pdfDocument.Save(pdfStream);

                        document.Close();
                        pdfDocument.Close(true);
                        pdfStream.Position = 0;
                    }
                }
            } catch (Exception ex) {
                var x = ex;
            }
            return pdfStream;
        }

        public MemoryStream GetSaleDeliveryNoteTwoPages<T>(T model, Dictionary<string, byte[]> images = null) {

            Assembly assembly = Assembly.GetExecutingAssembly();
            MemoryStream pdfStream = new MemoryStream();
            try {
                using (var fileStream = assembly.GetManifestResourceStream("Red20.Content.Templates.SaleDeliveryNoteTwoPages.docx")) {
                    using (WordDocument document = new WordDocument(fileStream, FormatType.Docx)) {
                        document.EnsureMinimal();

                        var fields = GetNames(model);
                        var values = GetValues(model);
                        var lists = GetLists(model);

                        document.MailMerge.RemoveEmptyParagraphs = true;
                        document.MailMerge.ClearFields = true;

                        document.MailMerge.MergeField += MailMerge_MergeField;

                        //B: Try to loop through each type of list in the model, to merge the data table automatically
                        foreach (var list in lists) {
                            MailMergeDataTable dataTable = new MailMergeDataTable(list.Key, list.Value);
                            document.MailMerge.ExecuteNestedGroup(dataTable);
                        }

                        document.MailMerge.Execute(fields.ToArray(), values.ToArray());

                        MemoryStream stream = new MemoryStream();
                        document.Save(stream, FormatType.Docx);

                        stream.Position = 0;

                        DocIORenderer render = new DocIORenderer();
                        Syncfusion.Pdf.PdfDocument pdfDocument = render.ConvertToPDF(document);
                        render.Dispose();

                        pdfDocument.Save(pdfStream);

                        document.Close();
                        pdfDocument.Close(true);
                        pdfStream.Position = 0;
                    }
                }
            } catch (Exception ex) {
                var x = ex;
            }
            return pdfStream;
        }

        public MemoryStream GetDeliveryNote<T>(T model, string type, Dictionary<string, byte[]> images = null) {

            Assembly assembly = Assembly.GetExecutingAssembly();
            MemoryStream pdfStream = new MemoryStream();
            try {
                using (var fileStream = type == "customer" ?
                    assembly.GetManifestResourceStream("Red20.Content.Templates.HireDeliveryNoteCustomerCopy.docx") :
                    assembly.GetManifestResourceStream("Red20.Content.Templates.HireDeliveryNoteOfficeCopy.docx")) {
                    using (WordDocument document = new WordDocument(fileStream, FormatType.Docx)) {
                        document.EnsureMinimal();

                        var fields = GetNames(model);
                        var values = GetValues(model);
                        var lists = GetLists(model);

                        document.MailMerge.RemoveEmptyParagraphs = true;
                        document.MailMerge.ClearFields = true;

                        document.MailMerge.MergeField += MailMerge_MergeField;

                        //B: Try to loop through each type of list in the model, to merge the data table automatically
                        foreach (var list in lists) {
                            MailMergeDataTable dataTable = new MailMergeDataTable(list.Key, list.Value);
                            document.MailMerge.ExecuteNestedGroup(dataTable);
                        }

                        document.MailMerge.Execute(fields.ToArray(), values.ToArray());

                        MemoryStream stream = new MemoryStream();
                        document.Save(stream, FormatType.Docx);

                        stream.Position = 0;

                        DocIORenderer render = new DocIORenderer();
                        Syncfusion.Pdf.PdfDocument pdfDocument = render.ConvertToPDF(document);
                        render.Dispose();

                        pdfDocument.Save(pdfStream);

                        document.Close();
                        pdfDocument.Close(true);
                        pdfStream.Position = 0;
                    }
                }
            } catch (Exception ex) {
                var x = ex;
            }
            return pdfStream;
        }

        public MemoryStream GetDeliveryNoteTwoPages<T>(T model, string type, Dictionary<string, byte[]> images = null) {

            Assembly assembly = Assembly.GetExecutingAssembly();
            MemoryStream pdfStream = new MemoryStream();
            try {
                using (var fileStream = type == "customer" ?
                    assembly.GetManifestResourceStream("Red20.Content.Templates.HireDeliveryNoteCustomerCopyTwoPages.docx") :
                    assembly.GetManifestResourceStream("Red20.Content.Templates.HireDeliveryNoteOfficeCopyTwoPages.docx")) {
                    using (WordDocument document = new WordDocument(fileStream, FormatType.Docx)) {
                        document.EnsureMinimal();

                        var fields = GetNames(model);
                        var values = GetValues(model);
                        var lists = GetLists(model);

                        document.MailMerge.RemoveEmptyParagraphs = true;
                        document.MailMerge.ClearFields = true;

                        document.MailMerge.MergeField += MailMerge_MergeField;

                        //B: Try to loop through each type of list in the model, to merge the data table automatically
                        foreach (var list in lists) {
                            MailMergeDataTable dataTable = new MailMergeDataTable(list.Key, list.Value);
                            document.MailMerge.ExecuteNestedGroup(dataTable);
                        }

                        document.MailMerge.Execute(fields.ToArray(), values.ToArray());

                        MemoryStream stream = new MemoryStream();
                        document.Save(stream, FormatType.Docx);

                        stream.Position = 0;

                        DocIORenderer render = new DocIORenderer();
                        Syncfusion.Pdf.PdfDocument pdfDocument = render.ConvertToPDF(document);
                        render.Dispose();

                        pdfDocument.Save(pdfStream);

                        document.Close();
                        pdfDocument.Close(true);
                        pdfStream.Position = 0;
                    }
                }
            } catch (Exception ex) {
                var x = ex;
            }
            return pdfStream;
        }

        public MemoryStream GetComplaintReport<T>(
        T model, string type) {

            Assembly assembly = Assembly.GetExecutingAssembly();
            MemoryStream pdfStream = new MemoryStream();

            try {
                using (var fileStream = type == "Customer" ? assembly.GetManifestResourceStream("Red20.Content.Templates.CustomerComplaintPdf.docx") : assembly.GetManifestResourceStream("Red20.Content.Templates.SupplierComplaintPdf.docx")) {

                    using (WordDocument document = new WordDocument(fileStream, FormatType.Docx)) {
                        document.EnsureMinimal();

                        var fields = GetNames(model);
                        var values = GetValues(model);
                        var lists = GetLists(model);

                        document.MailMerge.RemoveEmptyParagraphs = true;
                        document.MailMerge.ClearFields = true;

                        document.MailMerge.MergeField += MailMerge_MergeField;

                        //B: Try to loop through each type of list in the model, to merge the data table automatically
                        foreach (var list in lists) {
                            MailMergeDataTable dataTable = new MailMergeDataTable(list.Key, list.Value);
                            document.MailMerge.ExecuteNestedGroup(dataTable);
                        }

                        //RemoveMergeField(document, "Street1");
                        document.MailMerge.Execute(fields.ToArray(), values.ToArray());

                        MemoryStream stream = new MemoryStream();
                        document.Save(stream, FormatType.Docx);

                        stream.Position = 0;

                        DocIORenderer render = new DocIORenderer();
                        Syncfusion.Pdf.PdfDocument pdfDocument = render.ConvertToPDF(document);
                        render.Dispose();

                        pdfDocument.Save(pdfStream);

                        document.Close();
                        pdfDocument.Close(true);
                        pdfStream.Position = 0;
                    }
                }

            } catch (Exception ex) {
                var x = ex;
            }
            return pdfStream;
        }

        public MemoryStream GetJobCostingReport<T>(T model) {

            Assembly assembly = Assembly.GetExecutingAssembly();
            MemoryStream pdfStream = new MemoryStream();
            try {
                using (var fileStream = assembly.GetManifestResourceStream("Red20.Content.Templates.JobCosting.docx")) {
                    using (WordDocument document = new WordDocument(fileStream, FormatType.Docx)) {
                        document.EnsureMinimal();

                        var fields = GetNames(model);
                        var values = GetValues(model);
                        var lists = GetLists(model);

                        document.MailMerge.RemoveEmptyParagraphs = true;
                        document.MailMerge.ClearFields = true;

                        document.MailMerge.MergeField += MailMerge_MergeField;

                        //B: Try to loop through each type of list in the model, to merge the data table automatically
                        foreach (var list in lists) {
                            MailMergeDataTable dataTable = new MailMergeDataTable(list.Key, list.Value);
                            document.MailMerge.ExecuteNestedGroup(dataTable);
                        }

                        document.MailMerge.Execute(fields.ToArray(), values.ToArray());

                        MemoryStream stream = new MemoryStream();
                        document.Save(stream, FormatType.Docx);

                        stream.Position = 0;

                        DocIORenderer render = new DocIORenderer();
                        Syncfusion.Pdf.PdfDocument pdfDocument = render.ConvertToPDF(document);
                        render.Dispose();

                        pdfDocument.Save(pdfStream);

                        document.Close();
                        pdfDocument.Close(true);
                        pdfStream.Position = 0;
                    }
                }
            } catch (Exception ex) {
                var x = ex;
            }
            return pdfStream;
        }

        public IEnumerable<string> GetNames<T>(T model) {
            List<string> propertyList = new List<string>();
            foreach (var prop in model.GetType().GetProperties()) {
                if (prop.PropertyType == typeof(string) || prop.PropertyType == typeof(int) || prop.PropertyType == typeof(bool)) {
                    propertyList.Add(prop.Name);
                }
            }
            return propertyList;
        }

        public IEnumerable<string> GetValues<T>(T model) {
            List<string> propertyValues = new List<string>();
            foreach (var prop in model.GetType().GetProperties()) {
                if (prop.PropertyType == typeof(string) || prop.PropertyType == typeof(int) || prop.PropertyType == typeof(bool)) {
                    propertyValues.Add(prop.GetValue(model)?.ToString() ?? "");
                }
            }
            return propertyValues;
        }

        public IDictionary<string, List<object>> GetLists<T>(T model) {
            Dictionary<string, List<object>> listValues = new Dictionary<string, List<object>>();
            foreach (var prop in model.GetType().GetProperties()) {
                if (prop.PropertyType.GetInterface(nameof(IList)) != null) {
                    var listName = prop.Name;
                    var list = new List<object>();
                    foreach (var listValue in (IList)prop.GetValue(model)) {
                        list.Add(listValue);
                    }
                    listValues.Add(listName, list);
                }
            }
            return listValues;
        }

        private static void MergeFieldImage(object sender, MergeImageFieldEventArgs args) {
            // Get the image from disk during Merge.
            if (args.FieldName == "Image") {
                var imageName = args.FieldValue.ToString();
                var byteArray = System.IO.File.ReadAllBytes(AppContext.BaseDirectory.Replace("\\bin\\Debug\\netcoreapp2.0\\", "") + @"/Data/" + imageName);
                args.ImageStream = new MemoryStream(byteArray);
            }
        }

        private void MailMerge_MergeField(object sender, MergeFieldEventArgs args) {
            if (!string.IsNullOrWhiteSpace(args.FieldName) && (args.FieldName == "Description" || args.FieldName == "AdditionalInfo" || args.FieldName == "JobSheetDescription" || args.FieldName == "DetailsOfWork")) {
                WParagraph paragraph = args.CurrentMergeField.OwnerParagraph;
                int mergeFieldIndex = paragraph.ChildEntities.IndexOf(args.CurrentMergeField);
                int mergeFieldParaIndex = paragraph.OwnerTextBody.ChildEntities.IndexOf(paragraph);
                paragraph.ChildEntities.Remove(args.CurrentMergeField);
                paragraph.Document.XHTMLValidateOption = XHTMLValidationType.None;

                paragraph.OwnerTextBody.InsertXHTML(args.Text, mergeFieldParaIndex, mergeFieldIndex);
            }
        }

        public void RemoveMergeField(WordDocument wordDocument, string fieldName) {
            //Find the field by its name using Find method.
            TextSelection selection = wordDocument.Find(fieldName, true, true);
            //Get owner paragraph.
            WParagraph ownerParagraph = selection.GetAsOneRange().OwnerParagraph;

            if (ownerParagraph != null) {
                for (int index = 0; index < ownerParagraph.ChildEntities.Count; index++) {
                    //Remove the Merge field from its owner paragraph child entities colection.
                    if (ownerParagraph.ChildEntities[index] is WMergeField
                        && (ownerParagraph.ChildEntities[index] as WMergeField).FieldName == fieldName)
                        ownerParagraph.ChildEntities.RemoveAt(index);
                }
            }
        }

        public void CheckEmptyFieldValue(object sender, MergeFieldEventArgs args) {
            if (args.CurrentMergeField.FieldName == "Street1" && args.FieldName == "Street1" && args.FieldValue.ToString() == "") {
                //Get the paragraph of current merge field.
                WParagraph ownerParagraph = args.CurrentMergeField.OwnerParagraph;
                ownerParagraph.ChildEntities.Remove(args.CurrentMergeField);
                WTextBody ownerTextBody = ownerParagraph.OwnerTextBody;
                ownerTextBody.ChildEntities.Remove(ownerParagraph);
            }
        }
    }
}
