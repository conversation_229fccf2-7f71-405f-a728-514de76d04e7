﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Red20.Excel.Export;
using Red20.Model.Data;
using Red20.Model.Data.Customer;
using Red20.Model.Data.Document;
using Red20.Model.Data.Order;
using Red20.Model.Entity;
using Red20.Service.Data.Interface;
using Red20.Service.Storage.Interface;
using Red20.Service.Xero.Interface;
using Xero.NetStandard.OAuth2.Model.Accounting;

namespace Red20.Controllers {
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class CustomerController : ControllerBase {

        private ICustomerService service;
        private ICustomerAddressService addressService;
        private ICustomerContactService contactService;
        private IXeroService xeroService;
        private IDocumentService documentService;
        private IStorageService blobStorage;
        private IUserService userService;
        private ILogger<AuthController> logger;
        IUnitOfWork unitOfWork;

        public CustomerController(
            ICustomerService service,
            ICustomerAddressService addressService,
            ICustomerContactService contactService,
            IDocumentService documentService,
            IStorageService blobStorage,
            IUserService userService,
            ILogger<AuthController> logger,
            IXeroService xeroService,
            IUnitOfWork unitOfWork ) {

            this.service = service;
            this.addressService = addressService;
            this.contactService = contactService;
            this.logger = logger;
            this.unitOfWork = unitOfWork;
            this.xeroService = xeroService;
            this.documentService = documentService;
            this.blobStorage = blobStorage;
            this.userService = userService;
        }

        [HttpGet]
        [ProducesResponseType(200, Type = typeof(IList<CustomerModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get() {
            var customers = await service.GetAllCustomers();
            return Ok(customers);
        }

        [HttpGet("getAllCustomers")]
        [ProducesResponseType(200, Type = typeof(IList<AllCustomerModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAllCustomers()
        {
            var customers = await service.GetCustomers();
            return Ok(customers);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(CustomerModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id) {
            if (!await service.CustomerIdExistsAsync(id)) {
                return NotFound();
            }

            var customer = await service.GetAsync(id);

            return Ok(customer);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(CustomerModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody]CustomerUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }

            var customerExists = await service.CustomerNameExistsAsync(model.Name);

            if (customerExists) {
                return BadRequest();
            }
            var customerModel = new CustomerModel {
                Name = model.Name,
                Currency = model.Currency,
                CreditLimit = model.CreditLimit,
                IsActive = true,
                Status = "Pro-Forma",
                Notes = model.Notes,
                VatRegistrationNumber = model.VatRegistrationNumber,
                TaxCode = model.TaxCode,
                PrimaryInvoiceAddressName = model.PrimaryInvoiceAddressName,
                PrimaryInvoiceAddressStreet = model.PrimaryInvoiceAddressStreet,
                PrimaryInvoiceAddressStreet1 = model.PrimaryInvoiceAddressStreet1,
                PrimaryInvoiceAddressTown = model.PrimaryInvoiceAddressTown,
                PrimaryInvoiceAddressCountry = model.PrimaryInvoiceAddressCountry,
                PrimaryInvoiceAddressCounty = model.PrimaryInvoiceAddressCounty,
                PrimaryInvoiceAddressPostCode = model.PrimaryInvoiceAddressPostCode,
                IsShipping = model.IsShipping,
                PrimaryShippingAddressName = model.IsShipping ? model.PrimaryInvoiceAddressName : model.PrimaryShippingAddressName,
                PrimaryShippingAddressStreet = model.IsShipping ? model.PrimaryInvoiceAddressStreet : model.PrimaryShippingAddressStreet,
                PrimaryShippingAddressStreet1 = model.IsShipping ? model.PrimaryInvoiceAddressStreet1 : model.PrimaryShippingAddressStreet1,
                PrimaryShippingAddressTown = model.IsShipping ? model.PrimaryInvoiceAddressTown : model.PrimaryShippingAddressTown,
                PrimaryShippingAddressCounty = model.IsShipping ? model.PrimaryInvoiceAddressCounty : model.PrimaryShippingAddressCounty,
                PrimaryShippingAddressCountry = model.IsShipping ? model.PrimaryInvoiceAddressCountry : model.PrimaryShippingAddressCountry,
                PrimaryShippingAddressPostCode = model.IsShipping ? model.PrimaryInvoiceAddressPostCode : model.PrimaryShippingAddressPostCode,
                PrimaryInvoiceContactFirstName = model.PrimaryContactFirstName,
                PrimaryInvoiceContactSurname = model.PrimaryContactSurname,
                PrimaryInvoiceContactPhone = model.PrimaryContactPhone,
                PrimaryInvoiceContactFax = model.PrimaryContactFax,
                PrimaryInvoiceContactEmailAddress = model.PrimaryContactEmailAddress,
                PrimaryInvoiceContactDescription = model.PrimaryContactDescription,
            };

            var customer = await service.PostAsync(customerModel);

            return Ok(customer);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(CustomerModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Put(Guid id, [FromBody]CustomerUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }

            if (!await service.CustomerIdExistsAsync(id)) {
                return BadRequest();
            }

            var Customer = await service.PutAsync(id, model);

            return Ok(Customer);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Delete(Guid id) {
            if (!await service.CustomerIdExistsAsync(id)) {
                return NotFound();
            }

            await service.DeleteAsync(id);
            return Ok();
        }

        [HttpGet("xero")]
        [ProducesResponseType(200, Type = typeof(IList<UserModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetXero() {
            var users = await xeroService.GetContactsAsync();
            var customersList = users.Where(c => c.IsCustomer.Value).ToList();

            return Ok();
        }

        [HttpGet("xeroCustomers")]
        [ProducesResponseType(200, Type = typeof(IList<UserModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetXeroCustomers() {
            var users = await xeroService.GetContactsAsync();
            var customersList = users.Where(c => c.IsCustomer.Value).ToList();
            foreach (var contact in customersList) {
                var address = new Address();
                if (contact.Addresses.Any()) {
                    address = contact.Addresses.Where(c => c.AddressType == Address.AddressTypeEnum.POBOX).FirstOrDefault();
                }
                var newCustomer = new Customer {
                    CustomerId = Guid.NewGuid(),
                    XeroContactId = contact.ContactID,
                    Currency = contact.DefaultCurrency == CurrencyCode.GBP ? "Pounds Sterling" : contact.DefaultCurrency == CurrencyCode.EUR ? "Euro" : contact.DefaultCurrency == CurrencyCode.USD ? "US Dollar" : "Pounds Sterling",
                    Name = contact.Name,
                    VatRegistrationNumber = contact.TaxNumber,
                    PrimaryInvoiceAddressCountry = address != null ? address.Country : string.Empty,
                    PrimaryInvoiceAddressName = contact.Name,
                    PrimaryInvoiceAddressTown = address != null ? address.City : string.Empty,
                    PrimaryInvoiceAddressStreet = address != null ? address.AddressLine1 : string.Empty,
                    PrimaryInvoiceAddressStreet1 = address != null ? address.AddressLine2 : string.Empty,
                    PrimaryInvoiceAddressPostCode = address != null ? address.PostalCode : string.Empty,
                    Created = DateTime.Now
                };

                await unitOfWork.Customer.CreateAsync(newCustomer);

                if (address != null) {
                    var customerAddress = new CustomerAddress {
                        CustomerId = newCustomer.CustomerId,
                        IsInvoiceDefault = true,
                        Country = address.Country,
                        Name = contact.Name,
                        Town = address.City,
                        Street = address.AddressLine1,
                        Street1 = address.AddressLine2,
                        PostCode = address.PostalCode
                    };

                    await unitOfWork.CustomerAddress.CreateAsync(customerAddress);
                }

                await unitOfWork.SaveChangesAsync();
            }
            return Ok();
        }

        [HttpGet("xeroSuppliers")]
        [ProducesResponseType(200, Type = typeof(IList<UserModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetXeroSuppliers() {
            var users = await xeroService.GetContactsAsync();
            var customersList = users.Where(c => c.IsSupplier.Value).ToList();
            foreach (var contact in customersList) {
                var address = new Address();
                if (contact.Addresses.Any()) {
                    address = contact.Addresses.Where(c => c.AddressType == Address.AddressTypeEnum.POBOX).FirstOrDefault();
                }
                var newCustomer = new Supplier {
                    SupplierId = Guid.NewGuid(),
                    XeroContactId = contact.ContactID,
                    Name = contact.Name,
                    Created = DateTime.Now
                };

                await unitOfWork.Supplier.CreateAsync(newCustomer);

                if (address != null) {
                    var customerAddress = new SupplierAddress {
                        SupplierId = newCustomer.SupplierId,
                        Country = address.Country,
                        Name = contact.Name,
                        Town = address.City,
                        Street = address.AddressLine1,
                        Street1 = address.AddressLine2,
                        PostCode = address.PostalCode
                    };

                    await unitOfWork.SupplierAddress.CreateAsync(customerAddress);
                }

                await unitOfWork.SaveChangesAsync();
            }
            return Ok();
        }

        [HttpPost("export")]
        [ProducesResponseType(200)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> GenerateReport(List<CustomerModel> models) {
            try {
                var document = new DocumentModel();
                var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
                if (emailClaim == null)
                {
                    return Unauthorized();
                }

                var user = await userService.GetUserByEmailAsync(emailClaim.Value);
                var currentUser = $"{user.Firstname} {user.Lastname}";
                var itemData = ExportUtility.ExportCustomers(models, currentUser);
                document = await documentService.PostAsync("Customer_Report.xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"{user.Firstname} {user.Lastname}", "Customers", null, null, null, false, false, false);
                var file = File(itemData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                await blobStorage.UploadAsync(document.DocumentId, itemData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                return Ok();
            } catch (Exception ex) {
                return NotFound();
            }
        }
        [HttpGet("downloadReport")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> DownloadReport() {
            try {
                var lastDocument = await unitOfWork.Document.Query().Where(t => t.Type == "Customers").OrderByDescending(c => c.Created).FirstOrDefaultAsync();
                var download = await blobStorage.DownloadAsync(lastDocument.DocumentId);
                var file = File(download.Item1, download.Item2);
                return file;
            } catch (Exception ex) {
                return NotFound();
            }
        }
    }
}