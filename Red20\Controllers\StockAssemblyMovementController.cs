﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Red20.Model.Data.StockMovement;
using Red20.Service.Data.Interface;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Red20.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class StockAssemblyMovementController : ControllerBase
    {
        private IStockAssemblyMovementService stockAssemblyMovementService;
        private ILogger<AuthController> logger;

        public StockAssemblyMovementController(IStockAssemblyMovementService stockAssemblyMovementService, ILogger<AuthController> logger)
        {
            this.stockAssemblyMovementService = stockAssemblyMovementService;
            this.logger = logger;
        }

        [HttpGet]
        [ProducesResponseType(200, Type = typeof(List<StockAssemblyMovementModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get()
        {
            var stockAssemblyMovements = await stockAssemblyMovementService.GetAsync();
            return Ok(stockAssemblyMovements);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(StockAssemblyMovementModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id)
        {
            var stockAssemblyMovement = await stockAssemblyMovementService.GetAsync(id);
            return Ok(stockAssemblyMovement);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(StockAssemblyMovementModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody] StockAssemblyMovementUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }
            try
            {
                var stockAssemblyMovement = await stockAssemblyMovementService.PostAsync(model);
                return Ok(stockAssemblyMovement);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot create Purchase Order - {ex}");
                return BadRequest();
            }
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(StockAssemblyMovementModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(404)]
        public async Task<IActionResult> Put(Guid id, [FromBody] StockAssemblyMovementUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }

            if (await stockAssemblyMovementService.GetAsync(id) is null)
            {
                return NotFound();
            }

            var stockAssemblyMovement = await stockAssemblyMovementService.PutAsync(id, model);
            return Ok(stockAssemblyMovement);
        }
        [HttpDelete("{id}")]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Delete(Guid id)
        {
            if (await stockAssemblyMovementService.GetAsync(id) is null)
            {
                return NotFound();
            }

            await stockAssemblyMovementService.DeleteAsync(id);
            return Ok();
        }
    }
}
