﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Red20.Data.Migrations
{
    public partial class UserRed20Rates : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "UserRed20Rates",
                columns: table => new
                {
                    UserRed20RateId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    HourlyRate = table.Column<double>(type: "float", nullable: true),
                    Overtime1Rate = table.Column<double>(type: "float", nullable: true),
                    Overtime2Rate = table.Column<double>(type: "float", nullable: true),
                    OffshoreRateWeekday = table.Column<double>(type: "float", nullable: true),
                    OffshoreRateWeekend = table.Column<double>(type: "float", nullable: true),
                    StandardHours = table.Column<double>(type: "float", nullable: true),
                    Created = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Modified = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserRed20Rates", x => x.UserRed20RateId);
                    table.ForeignKey(
                        name: "FK_UserRed20Rates_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "UserId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_UserRed20Rates_UserId",
                table: "UserRed20Rates",
                column: "UserId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "UserRed20Rates");
        }
    }
}
