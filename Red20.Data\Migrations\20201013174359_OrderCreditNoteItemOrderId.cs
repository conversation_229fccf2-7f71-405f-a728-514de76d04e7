﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Red20.Data.Migrations
{
    public partial class OrderCreditNoteItemOrderId : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_OrderCreditNoteItems_OrderItems_OrderItemId",
                table: "OrderCreditNoteItems");

            migrationBuilder.AlterColumn<Guid>(
                name: "OrderItemId",
                table: "OrderCreditNoteItems",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");

            migrationBuilder.AddColumn<Guid>(
                name: "OrderId",
                table: "OrderCreditNoteItems",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "TotalValue",
                table: "OrderCreditNoteItems",
                nullable: false,
                defaultValue: 0.0);

            migrationBuilder.AddColumn<Guid>(
                name: "XeroCreditNoteId",
                table: "OrderCreditNoteItems",
                nullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_OrderCreditNoteItems_OrderItems_OrderItemId",
                table: "OrderCreditNoteItems",
                column: "OrderItemId",
                principalTable: "OrderItems",
                principalColumn: "OrderItemId",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_OrderCreditNoteItems_OrderItems_OrderItemId",
                table: "OrderCreditNoteItems");

            migrationBuilder.DropColumn(
                name: "OrderId",
                table: "OrderCreditNoteItems");

            migrationBuilder.DropColumn(
                name: "TotalValue",
                table: "OrderCreditNoteItems");

            migrationBuilder.DropColumn(
                name: "XeroCreditNoteId",
                table: "OrderCreditNoteItems");

            migrationBuilder.AlterColumn<Guid>(
                name: "OrderItemId",
                table: "OrderCreditNoteItems",
                type: "uniqueidentifier",
                nullable: false,
                oldClrType: typeof(Guid),
                oldNullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_OrderCreditNoteItems_OrderItems_OrderItemId",
                table: "OrderCreditNoteItems",
                column: "OrderItemId",
                principalTable: "OrderItems",
                principalColumn: "OrderItemId",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
