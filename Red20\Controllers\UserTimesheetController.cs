﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Red20.Excel.Export;
using Red20.Model.Data;
using Red20.Model.Data.Document;
using Red20.Model.Data.Job;
using Red20.Model.Data.Order;
using Red20.Model.Data.User;
using Red20.Model.Entity;
using Red20.Service.Data.Interface;
using Red20.Service.Email.Interface;
using Red20.Service.Storage.Interface;
using Red20.Settings;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Red20.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class UserTimesheetController : ControllerBase
    {

        private IUserTimesheetService service;
        private IUserTimesheetEntryService userTimesheetEntryService;
        private IUserService userService;
        private IOrderService orderService;
        private ILogger<AuthController> logger;
        private IEmailService emailService;
        private ClientSettings settings;
        private IUnitOfWork unitOfWork;
        private IDocumentService documentService;
        private IStorageService blobStorage;
        private IJobService jobService;
        private IJobInvoiceService jobInvoiceService;
        private IJobInvoiceItemService jobInvoiceItemService;
        private IMapper mapper;

        public UserTimesheetController(
            IUserTimesheetService service,
             IUserService userService,
             IOrderService orderService,
            IEmailService emailService,
            IUnitOfWork unitOfWork,
            IOptions<ClientSettings> settings,
            ILogger<AuthController> logger,
            IUserTimesheetEntryService userTimesheetEntryService,
            IDocumentService documentService,
            IStorageService blobStorage,
            IJobService jobService,
            IJobInvoiceService jobInvoiceService,
            IJobInvoiceItemService jobInvoiceItemService,
            IMapper mapper)
        {
            this.service = service;
            this.userService = userService;
            this.orderService = orderService;
            this.emailService = emailService;
            this.settings = settings.Value;
            this.logger = logger;
            this.unitOfWork = unitOfWork;
            this.userTimesheetEntryService = userTimesheetEntryService;
            this.documentService = documentService;
            this.blobStorage = blobStorage;
            this.jobService = jobService;
            this.jobInvoiceService = jobInvoiceService;
            this.jobInvoiceItemService = jobInvoiceItemService;
            this.mapper = mapper;
        }

        [HttpGet]
        [ProducesResponseType(200, Type = typeof(IList<UserTimesheetModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get()
        {
            var userTimesheets = await service.GetAsync();
            return Ok(userTimesheets);
        }

        [HttpGet("currentWeek")]
        [ProducesResponseType(200, Type = typeof(IList<UserTimesheetGridModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAllCurrentWeek()
        {
            var userTimesheets = await service.GetCurrentWeekAsync();
            return Ok(userTimesheets);
        }

        [HttpGet("previousWeek")]
        [ProducesResponseType(200, Type = typeof(IList<UserTimesheetGridModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAllPreviousWeeks()
        {
            var userTimesheets = await service.GetPreviousWeekAsync();
            return Ok(userTimesheets);
        }

        [HttpGet("archived")]
        [ProducesResponseType(200, Type = typeof(IList<UserTimesheetGridModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAllArchivedWeeks()
        {
            var userTimesheets = await service.GetArchivedWeekAsync();
            return Ok(userTimesheets);
        }

        [HttpGet("engineer")]
        [ProducesResponseType(200, Type = typeof(IList<UserModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetEngineers()
        {
            var users = await userService.GetEngineersAsync();
            return Ok(users);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id)
        {

            var userTimesheet = await service.GetByIdAsync(id);

            return Ok(userTimesheet);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(UserTimesheetModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody] UserTimesheetUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }
            if (model.IsPortal)
            {
                model.Date = model.WeekendDate.AddDays(-6);
            }


            if (model.Date.HasValue)
            {

                //bool isDaylightSavingsInEffect = TimeZoneInfo.Local.IsDaylightSavingTime(model.Date.Value);

                //if (isDaylightSavingsInEffect && !model.IsPortal) {
                //    model.Date = model.Date.Value.AddHours(1);
                //    model.WeekendDate = model.WeekendDate.AddHours(1);
                //}

                if (await service.UserTimesheetExistsAsync(model.Date.Value, model.WeekendDate, model.UserId))
                {
                    return BadRequest();
                }
            }

            var user = new UserModel();
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                var nameClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);
                user = await userService.GetAsync(Guid.Parse(nameClaim.Value));
            } else
            {
                user = await userService.GetUserByEmailAsync(emailClaim.Value);
            }

            model.CreatedBy = $"{user.Firstname} {user.Lastname}";
            model.ModifiedBy = $"{user.Firstname} {user.Lastname}";
            model.ResourceCode = user.ResourceCode;
            model.Location = user.Location;


            var userTimesheet = await service.PostAsync(model);
            return Ok(userTimesheet);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(UserTimesheetModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Put(Guid id, [FromBody] UserTimesheetUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }
            var user = new UserModel();
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                var nameClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);
                user = await userService.GetAsync(Guid.Parse(nameClaim.Value));
            } else
            {
                user = await userService.GetUserByEmailAsync(emailClaim.Value);
            }

            model.ModifiedBy = $"{user.Firstname} {user.Lastname}";

            var userTimesheet = await service.PutAsync(id, model);
            return Ok(userTimesheet);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Delete(Guid id)
        {
            await service.DeleteAsync(id);
            return Ok();
        }


        #region Timesheet Entries

        [HttpGet("getOrders")]
        [ProducesResponseType(200, Type = typeof(IList<UserTimesheetOrderModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetOrders()
        {
            var orders = await orderService.GetAllOrdersWithLineItemsAsync();
            return Ok(orders);
        }

        [HttpGet("getAllWeekStartDates")]
        [ProducesResponseType(200, Type = typeof(IList<WeekStartDatesModel>))]
        [ProducesResponseType(401)]
        public IActionResult GetWeekStartDates()
        {
            var jan1 = new DateTime(DateTime.Today.Year, 1, 1);
            var startOfFirstWeek = jan1.AddDays(1 - (int)(jan1.DayOfWeek));

            var weekStartDates =
                Enumerable
                    .Range(0, 54)
                    .Select(i => new
                    {
                        weekStart = startOfFirstWeek.AddDays(i * 7)
                    })
                    .TakeWhile(x => x.weekStart.Year <= jan1.Year)
                    .Select(x => new
                    {
                        x.weekStart,
                        weekFinish = x.weekStart.AddDays(4)
                    })
                    .SkipWhile(x => x.weekFinish < jan1.AddDays(1))
                    .Select((x, i) => new
                    {
                        x.weekStart,
                        x.weekFinish,
                        weekNum = i + 1
                    }).Select(s => new WeekStartDatesModel { WeekStartDate = s.weekStart, CurrentWeekStartDate = StartOfWeek(DateTime.UtcNow, DayOfWeek.Monday) }).ToList();

            return Ok(weekStartDates);
        }

        private DateTime StartOfWeek(DateTime dt, DayOfWeek startOfWeek)
        {
            int diff = (7 + (dt.DayOfWeek - startOfWeek)) % 7;
            return dt.AddDays(-1 * diff).Date;
        }

        public static DateTime FirstDayOfWeek(DateTime date)
        {
            DayOfWeek fdow = CultureInfo.CurrentCulture.DateTimeFormat.FirstDayOfWeek;
            int offset = fdow - date.DayOfWeek;
            DateTime fdowDate = date.AddDays(offset);
            return fdowDate;
        }

        [HttpGet("lastDate")]
        public static DateTime LastDayOfWeek()
        {
            DateTime ldowDate = FirstDayOfWeek(DateTime.Now).AddDays(6);
            return ldowDate;
        }

        [HttpGet("getByWeekForPortal/{fromDate}/{toDate}/{userId}")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> GetUserTimesheetByWeekForPortal(DateTime fromDate, DateTime toDate, Guid userId)
        {
            var userTimesheet = await service.GetByWeekAsync(fromDate, toDate, userId);
            return Ok(userTimesheet);
        }

        [HttpGet("getByWeek/{fromDate}/{toDate}")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> GetUserTimesheetByWeek(DateTime fromDate, DateTime toDate)
        {
            var user = new UserModel();
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                var nameClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);
                user = await userService.GetAsync(Guid.Parse(nameClaim.Value));
            } else
            {
                user = await userService.GetUserByEmailAsync(emailClaim.Value);
            }
            var userTimesheet = await service.GetByWeekAsync(fromDate, toDate, user.UserId);
            return Ok(userTimesheet);
        }


        [HttpGet("getByPortalWeek/{id}")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> GetUserTimesheetByWeek(Guid id)
        {
            var userTimesheet = await service.GetByIdAsync(id);
            var data = await service.GetByWeekAsync(userTimesheet.Date.Value, userTimesheet.WeekendDate, userTimesheet.UserId);
            return Ok(data);
        }

        [HttpGet("getAdminUserTimesheetEntriesByUserTimesheetId/{id}")]
        [ProducesResponseType(200, Type = typeof(IList<UserTimesheetEntryModel>))]
        [ProducesResponseType(400)]
        public async Task<IActionResult> GetAdminUserTimesheetEntriesByTimesheetId(Guid id)
        {
            var userTimesheetEntries = await userTimesheetEntryService.GetAdminUserTimesheetEntriesByTimesheetId(id);
            return Ok(userTimesheetEntries);
        }

        [HttpGet("getAdminUserTimesheetEntriesByDaily/{id}")]
        [ProducesResponseType(200, Type = typeof(IList<UserTimesheetEntryModel>))]
        [ProducesResponseType(400)]
        public async Task<IActionResult> GetAdminUserTimesheetEntriesByDailyId(Guid id)
        {
            var userTimesheetEntries = await userTimesheetEntryService.GetByUserTimesheetIdAsync(id);
            return Ok(userTimesheetEntries);
        }

        [HttpGet("getUserTimesheetEntriesByUserTimesheetId/{id}")]
        [ProducesResponseType(200, Type = typeof(IList<UserTimesheetEntryModel>))]
        [ProducesResponseType(400)]
        public async Task<IActionResult> GetUserTimesheetEntriesByTimesheetId(Guid id)
        {
            var userTimesheetEntries = await userTimesheetEntryService.GetByUserTimesheetIdAsync(id);
            return Ok(userTimesheetEntries);
        }

        [HttpPost("getUserTimesheetEntriesByTimesheetIdAndDate")]
        [ProducesResponseType(200, Type = typeof(IList<UserTimesheetEntryModel>))]
        [ProducesResponseType(400)]
        public async Task<IActionResult> GetUserTimesheetEntriesByTimesheetAndDate([FromBody] UserTimesheetEntryQueryModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }

            DateTime filterDateValue;
            if (!DateTime.TryParse(model.Date.ToString(), out filterDateValue) || model.UserTimesheetId == Guid.Empty)
            {
                return BadRequest();
            }

            var userTimesheetEntries = await userTimesheetEntryService.GetByUserTimesheetIdAsync(model.UserTimesheetId);

            //userTimesheetEntries = userTimesheetEntries.Where(w => w.Created.Date == filterDateValue.Date && w.Created <= filterDateValue.AddDays(1).AddTicks(-1)).ToList();

            return Ok(userTimesheetEntries);
        }

        [HttpPost("getExistingTimesheetEntry")]
        [ProducesResponseType(200, Type = typeof(IList<UserTimesheetEntryModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetExistingTimesheetEntry([FromBody] UserTimesheetLookupModel model)
        {
            var userTimesheetEntry = await service.GetExistingAsync(model);
            return Ok(userTimesheetEntry);
        }

        [HttpPost("createUserTimesheetEntry")]
        [ProducesResponseType(200, Type = typeof(UserTimesheetEntryModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> CreateTimesheetEntry([FromBody] UserTimesheetEntryUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }

            var user = new UserModel();
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                var nameClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);
                user = await userService.GetAsync(Guid.Parse(nameClaim.Value));
            } else
            {
                user = await userService.GetUserByEmailAsync(emailClaim.Value);
            }
            var usertimesheet = await service.GetByIdAsync(model.UserTimesheetId);

            model.ModifiedBy = $"{user.Firstname} {user.Lastname}";
            model.CreatedBy = $"{user.Firstname} {user.Lastname}";
            model.EmployeeCode = user.EmployeeCode;

            var weekendDate = usertimesheet.WeekendDate;

            if (model.Monday.HasValue)
            {
                model.Created = weekendDate.AddDays(-6);
            } else if (model.Tuesday.HasValue)
            {
                model.Created = weekendDate.AddDays(-5);
            } else if (model.Wednesday.HasValue)
            {
                model.Created = weekendDate.AddDays(-4);
            } else if (model.Thursday.HasValue)
            {
                model.Created = weekendDate.AddDays(-3);
            } else if (model.Friday.HasValue)
            {
                model.Created = weekendDate.AddDays(-2);
            } else if (model.Saturday.HasValue)
            {
                model.Created = weekendDate.AddDays(-1);
            } else
            {
                model.Created = DateTime.Now;
            }
            var userTimesheetEntry = await userTimesheetEntryService.PostAsync(model);
            return Ok(userTimesheetEntry);
        }

        [HttpPost("createUserTimesheetEntryPortal")]
        [ProducesResponseType(200, Type = typeof(UserTimesheetEntryModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> CreateTimesheetEntryPortal([FromBody] UserTimesheetEntryUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }

            var user = new UserModel();
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                var nameClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);
                user = await userService.GetAsync(Guid.Parse(nameClaim.Value));
            } else
            {
                user = await userService.GetUserByEmailAsync(emailClaim.Value);
            }
            var usertimesheet = await service.GetByIdAsync(model.UserTimesheetId);

            model.ModifiedBy = $"{user.Firstname} {user.Lastname}";
            model.CreatedBy = $"{user.Firstname} {user.Lastname}";
            model.EmployeeCode = user.EmployeeCode;

            var weekendDate = usertimesheet.WeekendDate;

            var models = new List<UserTimesheetEntryUpdateModel>();

            if (model.Monday.HasValue)
            {
                var mondayModel = new UserTimesheetEntryUpdateModel
                {
                    UserTimesheetId = model.UserTimesheetId,
                    OrderId = model.OrderId,
                    CostCode = model.CostCode,
                    PayType = model.PayType,
                    Monday = model.Monday,
                    Created = weekendDate.AddDays(-6),
                    CreatedBy = user.Name,
                    ModifiedBy = user.Name
                };
                models.Add(mondayModel);
            }
            if (model.Tuesday.HasValue)
            {
                var tuesdayModel = new UserTimesheetEntryUpdateModel
                {
                    UserTimesheetId = model.UserTimesheetId,
                    OrderId = model.OrderId,
                    CostCode = model.CostCode,
                    PayType = model.PayType,
                    Tuesday = model.Tuesday,
                    Created = weekendDate.AddDays(-5),
                    CreatedBy = user.Name,
                    ModifiedBy = user.Name
                };
                models.Add(tuesdayModel);
            }
            if (model.Wednesday.HasValue)
            {
                var wednesdayModel = new UserTimesheetEntryUpdateModel
                {
                    UserTimesheetId = model.UserTimesheetId,
                    OrderId = model.OrderId,
                    CostCode = model.CostCode,
                    PayType = model.PayType,
                    Wednesday = model.Wednesday,
                    Created = weekendDate.AddDays(-4),
                    CreatedBy = user.Name,
                    ModifiedBy = user.Name
                };
                models.Add(wednesdayModel);
            }
            if (model.Thursday.HasValue)
            {
                var thursdayModel = new UserTimesheetEntryUpdateModel
                {
                    UserTimesheetId = model.UserTimesheetId,
                    OrderId = model.OrderId,
                    CostCode = model.CostCode,
                    PayType = model.PayType,
                    Thursday = model.Thursday,
                    Created = weekendDate.AddDays(-3),
                    CreatedBy = user.Name,
                    ModifiedBy = user.Name
                };
                models.Add(thursdayModel);
            }
            if (model.Friday.HasValue)
            {
                var fridayModel = new UserTimesheetEntryUpdateModel
                {
                    UserTimesheetId = model.UserTimesheetId,
                    OrderId = model.OrderId,
                    CostCode = model.CostCode,
                    PayType = model.PayType,
                    Friday = model.Friday,
                    Created = weekendDate.AddDays(-2),
                    CreatedBy = user.Name,
                    ModifiedBy = user.Name
                };
                models.Add(fridayModel);
            }
            if (model.Saturday.HasValue)
            {
                var saturdayModel = new UserTimesheetEntryUpdateModel
                {
                    UserTimesheetId = model.UserTimesheetId,
                    OrderId = model.OrderId,
                    CostCode = model.CostCode,
                    PayType = model.PayType,
                    Saturday = model.Saturday,
                    Created = weekendDate.AddDays(-1),
                    CreatedBy = user.Name,
                    ModifiedBy = user.Name
                };
                models.Add(saturdayModel);
            }
            if (model.Sunday.HasValue)
            {
                var sundayModel = new UserTimesheetEntryUpdateModel
                {
                    UserTimesheetId = model.UserTimesheetId,
                    OrderId = model.OrderId,
                    CostCode = model.CostCode,
                    PayType = model.PayType,
                    Sunday = model.Sunday,
                    Created = weekendDate,
                    CreatedBy = user.Name,
                    ModifiedBy = user.Name
                };
                models.Add(sundayModel);
            }

            foreach (var entryModel in models)
            {
                await userTimesheetEntryService.PostAsync(entryModel);
            }
            return Ok();
        }

        [HttpPut("editUserTimesheetEntry/{id}")]
        [ProducesResponseType(200, Type = typeof(UserTimesheetEntryModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> EditTimesheetEntry(Guid id, [FromBody] UserTimesheetEntryUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }

            var timesheetEntry = await userTimesheetEntryService.GetByIdAsync(id);
            var usertimesheet = await service.GetByIdAsync(model.UserTimesheetId);

            if (timesheetEntry is null)
            {
                return BadRequest();
            }

            if (timesheetEntry.Status != model.Status)
            {
                if (model.Status == "Approved")
                {
                    model.ApprovedDate = DateTime.Now;
                } else if (model.Status == "Completed")
                {
                    model.CompletedDate = DateTime.Now;
                }
            }

            var weekendDate = usertimesheet.WeekendDate;

            if (model.Monday.HasValue)
            {
                model.Created = weekendDate.AddDays(-6);
            } else if (model.Tuesday.HasValue)
            {
                model.Created = weekendDate.AddDays(-5);
            } else if (model.Wednesday.HasValue)
            {
                model.Created = weekendDate.AddDays(-4);
            } else if (model.Thursday.HasValue)
            {
                model.Created = weekendDate.AddDays(-3);
            } else if (model.Friday.HasValue)
            {
                model.Created = weekendDate.AddDays(-2);
            } else if (model.Saturday.HasValue)
            {
                model.Created = weekendDate.AddDays(-1);
            }

            var user = new UserModel();
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                var nameClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);
                user = await userService.GetAsync(Guid.Parse(nameClaim.Value));
            } else
            {
                user = await userService.GetUserByEmailAsync(emailClaim.Value);
            }

            model.EmployeeCode = timesheetEntry.EmployeeCode;
            model.ModifiedBy = $"{user.Firstname} {user.Lastname}";

            try
            {
                await userTimesheetEntryService.PutAsync(timesheetEntry.UserTimesheetEntryId, model, true);
                return Ok();
            } catch (Exception ex)
            {
                return BadRequest($"{ex}");
            }
        }
        [HttpPut("updateStatus/{id}/{day}")]
        [ProducesResponseType(200, Type = typeof(UserTimesheetEntryModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> UpdateStatus(Guid id, string day)
        {

            List<Model.Entity.UserTimesheetEntry> timesheetEntries = new List<Model.Entity.UserTimesheetEntry>();

            var timesheet = await service.GetByIdAsync(id);
            if (day == "Monday")
            {
                timesheetEntries = unitOfWork.UserTimesheetEntry.Query(c => c.UserTimesheetId == id && c.Monday.HasValue).ToList();
            } else if (day == "Tuesday")
            {
                timesheetEntries = unitOfWork.UserTimesheetEntry.Query(c => c.UserTimesheetId == id && c.Tuesday.HasValue).ToList();
            } else if (day == "Wednesday")
            {
                timesheetEntries = unitOfWork.UserTimesheetEntry.Query(c => c.UserTimesheetId == id && c.Wednesday.HasValue).ToList();
            } else if (day == "Thursday")
            {
                timesheetEntries = unitOfWork.UserTimesheetEntry.Query(c => c.UserTimesheetId == id && c.Thursday.HasValue).ToList();
            } else if (day == "Friday")
            {
                timesheetEntries = unitOfWork.UserTimesheetEntry.Query(c => c.UserTimesheetId == id && c.Friday.HasValue).ToList();
            } else if (day == "Saturday")
            {
                timesheetEntries = unitOfWork.UserTimesheetEntry.Query(c => c.UserTimesheetId == id && c.Saturday.HasValue).ToList();
            } else if (day == "Sunday")
            {
                timesheetEntries = unitOfWork.UserTimesheetEntry.Query(c => c.UserTimesheetId == id && c.Sunday.HasValue).ToList();
            }

            foreach (var entry in timesheetEntries)
            {
                var timesheetEntry = unitOfWork.UserTimesheetEntry.Query(c => c.UserTimesheetEntryId == entry.UserTimesheetEntryId).Include(i => i.UserTimesheet).FirstOrDefault();

                timesheetEntry.Status = "Approved";
                timesheetEntry.ApprovedDate = DateTime.Now;
                if (day == "Monday")
                {
                    timesheetEntry.IsMondayApproved = true;
                } else if (day == "Tuesday")
                {
                    timesheetEntry.IsTuesdayApproved = true;
                } else if (day == "Wednesday")
                {
                    timesheetEntry.IsWednesdayApproved = true;
                } else if (day == "Thursday")
                {
                    timesheetEntry.IsThursdayApproved = true;
                } else if (day == "Friday")
                {
                    timesheetEntry.IsFridayApproved = true;
                } else if (day == "Saturday")
                {
                    timesheetEntry.IsSaturdayApproved = true;
                } else if (day == "Sunday")
                {
                    timesheetEntry.IsSundayApproved = true;
                }

                try
                {
                    unitOfWork.UserTimesheetEntry.Update(timesheetEntry);
                    await unitOfWork.SaveChangesAsync();

                } catch (Exception ex)
                {
                    return BadRequest($"{ex}");
                }
            }
            return Ok();
        }

        [HttpPut("completeTimesheet/{id}/")]
        [ProducesResponseType(200, Type = typeof(UserTimesheetEntryModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> CompleteTimesheet(Guid id)
        {

            var timesheetEntries = unitOfWork.UserTimesheetEntry.Query(c => c.UserTimesheetId == id && !c.CompletedDate.HasValue).ToList();

            foreach (var entry in timesheetEntries)
            {
                var timesheetEntry = unitOfWork.UserTimesheetEntry.Query(c => c.UserTimesheetEntryId == entry.UserTimesheetEntryId).Include(i => i.UserTimesheet).ThenInclude(i => i.User).FirstOrDefault();

                timesheetEntry.Status = "Completed";
                timesheetEntry.CompletedDate = DateTime.Now;

                try
                {

                    unitOfWork.UserTimesheetEntry.Update(timesheetEntry);

                    //await userTimesheetEntryService.PutAsync(entry.UserTimesheetEntryId, mapper.Map<UserTimesheetEntryUpdateModel>(timesheetEntry));

                    //once timesheet entry is approved, create a Job (if not already exists) + Invoice + Invoice Item => all tied to the Order Id.
                    var user = new UserModel();
                    var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                             c.Type == ClaimTypes.Email);
                    if (emailClaim == null)
                    {
                        var nameClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);
                        user = await userService.GetAsync(Guid.Parse(nameClaim.Value));
                    } else
                    {
                        user = await userService.GetUserByEmailAsync(emailClaim.Value);
                    }
                    var currentUser = $"{user.Firstname} {user.Lastname}";
                    var job = new JobModel();
                    var labourInvoice = new JobInvoiceModel();

                    var order = await orderService.GetAsync(entry.OrderId);
                    if (order != null)
                    {
                        job = await jobService.GetByOrderIdAsync(entry.OrderId);
                        if (job == null)
                        {
                            job = await jobService.PostAsync(new JobModel
                            {
                                CreatedBy = user != null ? $"{user.Firstname} {user.Lastname}" : null,
                                OrderId = order.OrderId,
                                CostCentre = user.Location == "Oldmeldrum" ? "001" : user.Location == "West Bromwich" ? "002" : "",
                                OrderType = order.Type,
                                OrderNumber = order.Number,
                                OrderCustomerName = order.Customer != null ? order.Customer.Name : string.Empty,
                                Description = order.FirstItemDescription,
                                DateOrderRaised = order.Created,
                                Currency = order.Currency
                            });
                        }

                        labourInvoice.JobId = job.JobId;
                        labourInvoice.CreatedBy = $"{user.Firstname} {user.Lastname}";
                        labourInvoice.JobType = "LABOUR";
                        var jobInvoices = await jobInvoiceService.GetByJobIdAsync(job.JobId);
                        int currentLabourInvoices = jobInvoices.Any(w => w.JobType == "LABOUR") ? jobInvoices.Where(w => w.JobType == "LABOUR").Count() + 1 : 1;
                        labourInvoice.InvoiceNumber = $"{order.Number}/Labour/{currentLabourInvoices}";

                        DateTime? weekStartDate = timesheetEntry.UserTimesheet != null ? timesheetEntry.UserTimesheet.Date : (DateTime?)null;
                        DateTime? weekEndDate = timesheetEntry.UserTimesheet != null ? timesheetEntry.UserTimesheet.WeekendDate : (DateTime?)null;

                        DateTime? invoiceDate = weekStartDate.HasValue && weekEndDate.HasValue ?
                                                    timesheetEntry.Monday.HasValue ? weekStartDate.Value :
                                                    timesheetEntry.Tuesday.HasValue ? weekStartDate.Value.AddDays(1) :
                                                    timesheetEntry.Wednesday.HasValue ? weekStartDate.Value.AddDays(2) :
                                                    timesheetEntry.Thursday.HasValue ? weekStartDate.Value.AddDays(3) :
                                                    timesheetEntry.Friday.HasValue ? weekStartDate.Value.AddDays(4) :
                                                    timesheetEntry.Saturday.HasValue ? weekStartDate.Value.AddDays(5) :
                                                    weekStartDate.Value.AddDays(6) :
                                                (DateTime?)null;

                        labourInvoice.InvoiceDate = invoiceDate.HasValue ? invoiceDate.Value : DateTime.UtcNow;

                        labourInvoice.CustomerPO = order != null ? order.CustomerRef : string.Empty;

                        if (!await jobInvoiceService.JobInvoiceNumberExistsAsync(labourInvoice.InvoiceNumber))
                        {
                            labourInvoice = await jobInvoiceService.PostAsync(labourInvoice);
                        } else
                        {
                            return BadRequest($"Invoice Number already exists in the system");
                        }

                        var numberToInvoice = entry.Monday.HasValue ? entry.Monday.Value :
                                              entry.Tuesday.HasValue ? entry.Tuesday.Value :
                                              entry.Wednesday.HasValue ? entry.Wednesday.Value :
                                              entry.Thursday.HasValue ? entry.Thursday.Value :
                                              entry.Friday.HasValue ? entry.Friday.Value :
                                              entry.Saturday.HasValue ? entry.Saturday.Value :
                                              entry.Sunday.HasValue ? entry.Sunday.Value : 0.0;

                        var timesheetUser = entry.UserTimesheet != null ? await userService.GetAsync(entry.UserTimesheet.UserId) : null;
                        var red20PayRate = unitOfWork.UserRed20Rate.Query().Where(c => c.UserId == timesheetUser.UserId).OrderByDescending(c => c.Date).FirstOrDefault();
                        var labourInvoiceItem = new JobInvoiceItemModel
                        {
                            JobId = job.JobId,
                            JobInvoiceId = labourInvoice.JobInvoiceId,
                            InvoiceNumber = labourInvoice.InvoiceNumber,
                            InvoiceType = "LABOUR",
                            Quantity = numberToInvoice,
                            UnitPrice = entry.PayType == "Hourly Rate" && red20PayRate.HourlyRate.HasValue ? red20PayRate.HourlyRate.Value :
                                    entry.PayType == "Overtime 1" && red20PayRate.Overtime1Rate.HasValue ? red20PayRate.Overtime1Rate.Value :
                                    entry.PayType == "Overtime 2" && red20PayRate.Overtime2Rate.HasValue ? red20PayRate.Overtime2Rate.Value :
                                    entry.PayType == "Offshore Rate Weekday" && red20PayRate.OffshoreRateWeekday.HasValue ? red20PayRate.OffshoreRateWeekday.Value :
                                    entry.PayType == "Offshore Rate Weekend" && red20PayRate.OffshoreRateWeekend.HasValue ? red20PayRate.OffshoreRateWeekend.Value : 0.0,
                            StockCategory = null,
                            StockCode = null,
                            Value = entry.PayType == "Hourly Rate" && red20PayRate.HourlyRate.HasValue ? red20PayRate.HourlyRate.Value * numberToInvoice :
                                    entry.PayType == "Overtime 1" && red20PayRate.Overtime1Rate.HasValue ? red20PayRate.Overtime1Rate.Value * numberToInvoice :
                                    entry.PayType == "Overtime 2" && red20PayRate.Overtime2Rate.HasValue ? red20PayRate.Overtime2Rate.Value * numberToInvoice :
                                    entry.PayType == "Offshore Rate Weekday" && red20PayRate.OffshoreRateWeekday.HasValue ? red20PayRate.OffshoreRateWeekday.Value * numberToInvoice :
                                    entry.PayType == "Offshore Rate Weekend" && red20PayRate.OffshoreRateWeekend.HasValue ? red20PayRate.OffshoreRateWeekend.Value * numberToInvoice :
                                    0.0,
                            Currency = "Pounds Sterling",
                            Description = timesheetUser != null ? entry.EngineerDescription : "Labour",
                            OrderItemId = timesheetEntry.UserTimesheetEntryId, //for LABOUR invoices we are setting OrderItemId to be the Timesheet Entry Id so we can have a link between the invoice item and the timesheet entry item.
                            QuantityToDeliver = numberToInvoice,
                            ToFollow = 0,
                            Vat = order.TaxCode,
                            AccountCode = order.AccountCode,
                            IsStockItem = false,
                            IsInitialDeliveryCharge = false,
                            IsDeliveryCharge = false,
                            InvoiceDate = labourInvoice.InvoiceDate,
                            LabourPayType = entry.PayType,
                            EmployeeCode = timesheetEntry.UserTimesheet != null && timesheetEntry.UserTimesheet.User != null ? timesheetEntry.UserTimesheet.User.EmployeeCode : entry.EmployeeCode
                        };

                        await jobInvoiceItemService.PostAsync(labourInvoiceItem);
                    }
                } catch (Exception ex)
                {
                    return BadRequest($"{ex}");
                }
            }

            await unitOfWork.SaveChangesAsync();

            return Ok();
        }

        [HttpDelete("deleteTimesheetEntry/{id}/{dayToDelete}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> DeleteTimesheetEntry(Guid id, string dayToDelete)
        {
            if (!await userTimesheetEntryService.TimesheetEntryExistsAsync(id))
            {
                return BadRequest();
            }
            await userTimesheetEntryService.DeleteAsync(id, dayToDelete);
            return Ok();
        }

        [HttpDelete("deleteUserTimesheetEntry/{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> DeleteUserTimesheetEntry(Guid id)
        {
            if (!await userTimesheetEntryService.TimesheetEntryExistsAsync(id))
            {
                return BadRequest();
            }
            var userTimesheetEntry = await userTimesheetEntryService.GetByIdAsync(id);

            var userTimesheet = await service.GetByIdAsync(userTimesheetEntry.UserTimesheetId);

            var userTimesheetEntries = unitOfWork.UserTimesheetEntry.Query(c => c.UserTimesheetId == userTimesheetEntry.UserTimesheetId && c.PayType == userTimesheetEntry.PayType && c.OrderId == userTimesheetEntry.OrderId).ToList();

            foreach (var timesheetEntry in userTimesheetEntries)
            {
                await userTimesheetEntryService.DeleteUserTimesheetEntryAsync(timesheetEntry.UserTimesheetEntryId);
            }

            return Ok();
        }

        [HttpGet("printTimesheet/{id}/{from}/{to}")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> PrintUserTimesheet(Guid id, DateTime from, DateTime to)
        {
            try
            {
                var userTimesheetEntries = new List<UserTimesheetEntry>();
                var user = new UserModel();
                var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                         c.Type == ClaimTypes.Email);
                if (emailClaim == null)
                {
                    var nameClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);
                    user = await userService.GetAsync(Guid.Parse(nameClaim.Value));
                } else
                {
                    user = await userService.GetUserByEmailAsync(emailClaim.Value);
                }
                var document = new DocumentModel();
                var userName = $"{user.Firstname} {user.Lastname}";
                var userTimesheets = unitOfWork.UserTimesheet.Query(c => c.UserId == id).Include(x => x.User).Include(x => x.UserTimesheetEntries).ToList();

                foreach (var item in userTimesheets)
                {
                    if (item.UserTimesheetEntries.Any())
                    {
                        var entries = unitOfWork.UserTimesheetEntry.Query().Include(x => x.Order).Include(x => x.UserTimesheet).ThenInclude(x => x.User).Where(c => c.UserTimesheetId == item.UserTimesheetId && c.Created.Date >= from.Date && c.Created.Date <= to.Date && c.CompletedDate.HasValue).ToList();
                        foreach (var entry in entries)
                        {
                            userTimesheetEntries.Add(entry);
                        }
                    }
                }

                var userPayRollRates = unitOfWork.UserPayrollRate.Query().Include(c => c.User).ToList();
                var userRed20Rates = unitOfWork.UserRed20Rate.Query().Include(c => c.User).ToList();

                var data = ExportUtility.ExportUserTimehseetReport(userTimesheetEntries, userName, userPayRollRates, userRed20Rates, from, to);

                document = await documentService.PostAsync("User_Timehsheet_Report.xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"{user.Firstname} {user.Lastname}", "User Timesheet", null, null, null, false, false, true, DateTime.Now);
                var file = File(data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                await blobStorage.UploadAsync(document.DocumentId, data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                return file;
            } catch (Exception ex)
            {
                return NotFound();
            }
        }
        #endregion

        [HttpGet("updateinvoicenumbers")]
        public async Task<IActionResult> UpdateInvoiceNumbers()
        {
            var jobInvoices = await unitOfWork.JobInvoice.Query(c => c.JobId == Guid.Parse("efce7820-e064-4256-9ebd-53abc7ff7ad8") && c.JobType == "LABOUR" && c.Created > new DateTime(2021, 09, 14, 14, 55, 49)).OrderBy(c => c.Created).ToListAsync();

            int count = 50;

            foreach (var invoice in jobInvoices)
            {
                invoice.InvoiceNumber = $"SS000648/Labour/{count}";
                count++;
                unitOfWork.JobInvoice.Update(invoice);
            }
            await unitOfWork.SaveChangesAsync();

            return Ok();
        }
    }
}