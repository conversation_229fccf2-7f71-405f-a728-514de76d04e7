﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace Red20.Data.Migrations
{
    public partial class OrderItemCurrencyRate : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "PoundValue",
                table: "PurchaseOrderItems");

            migrationBuilder.AddColumn<double>(
                name: "GrossPoundValue",
                table: "PurchaseOrderItems",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "NettPoundValue",
                table: "PurchaseOrderItems",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "VatPoundValue",
                table: "PurchaseOrderItems",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "CurrencyRate",
                table: "OrderItems",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "GrossPoundValue",
                table: "PurchaseOrderItems");

            migrationBuilder.DropColumn(
                name: "NettPoundValue",
                table: "PurchaseOrderItems");

            migrationBuilder.DropColumn(
                name: "VatPoundValue",
                table: "PurchaseOrderItems");

            migrationBuilder.DropColumn(
                name: "CurrencyRate",
                table: "OrderItems");

            migrationBuilder.AddColumn<double>(
                name: "PoundValue",
                table: "PurchaseOrderItems",
                type: "float",
                nullable: true);
        }
    }
}
