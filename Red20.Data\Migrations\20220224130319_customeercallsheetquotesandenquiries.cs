﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Red20.Data.Migrations
{
    public partial class customeercallsheetquotesandenquiries : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "CustomerCallSheetId",
                table: "Quotes",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "CustomerCallSheetId",
                table: "Enquiries",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Quotes_CustomerCallSheetId",
                table: "Quotes",
                column: "CustomerCallSheetId");

            migrationBuilder.CreateIndex(
                name: "IX_Enquiries_CustomerCallSheetId",
                table: "Enquiries",
                column: "CustomerCallSheetId");

            migrationBuilder.AddForeignKey(
                name: "FK_Enquiries_CustomerCallSheets_CustomerCallSheetId",
                table: "Enquiries",
                column: "CustomerCallSheetId",
                principalTable: "CustomerCallSheets",
                principalColumn: "CustomerCallSheetId");

            migrationBuilder.AddForeignKey(
                name: "FK_Quotes_CustomerCallSheets_CustomerCallSheetId",
                table: "Quotes",
                column: "CustomerCallSheetId",
                principalTable: "CustomerCallSheets",
                principalColumn: "CustomerCallSheetId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Enquiries_CustomerCallSheets_CustomerCallSheetId",
                table: "Enquiries");

            migrationBuilder.DropForeignKey(
                name: "FK_Quotes_CustomerCallSheets_CustomerCallSheetId",
                table: "Quotes");

            migrationBuilder.DropIndex(
                name: "IX_Quotes_CustomerCallSheetId",
                table: "Quotes");

            migrationBuilder.DropIndex(
                name: "IX_Enquiries_CustomerCallSheetId",
                table: "Enquiries");

            migrationBuilder.DropColumn(
                name: "CustomerCallSheetId",
                table: "Quotes");

            migrationBuilder.DropColumn(
                name: "CustomerCallSheetId",
                table: "Enquiries");
        }
    }
}
