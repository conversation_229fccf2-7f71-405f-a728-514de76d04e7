﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Red20.Model.Data.Job;
using Red20.Model.Data.PurchaseOrder;
using Red20.Service.Data.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Red20.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class PurchaseOrderCreditNoteItemController : ControllerBase
    {

        private IPurchaseOrderService purchaseOrderService;
        private IPurchaseOrderItemService purchaseOrderItemService;
        private IPurchaseOrderCreditNoteItemService purchaseOrderCreditNoteItemService;
        private IUserService userService;
        private IJobService jobService;
        private IJobInvoiceService jobInvoiceService;
        private IJobInvoiceItemService jobInvoiceItemService;
        private IOrderService orderService;
        private ICurrencyRateService currencyRateService;
        private ILogger<AuthController> logger;
        IUnitOfWork unitOfWork;


        public PurchaseOrderCreditNoteItemController(
            IPurchaseOrderService purchaseOrderService,
            IPurchaseOrderItemService purchaseOrderItemService,
            IPurchaseOrderCreditNoteItemService purchaseOrderCreditNoteItemService,
            IUserService userService,
            IJobService jobService,
            IJobInvoiceService jobInvoiceService,
            IJobInvoiceItemService jobInvoiceItemService,
            IOrderService orderService,
            ICurrencyRateService currencyRateService,
            IUnitOfWork unitOfWork,
            ILogger<AuthController> logger)
        {

            this.purchaseOrderService = purchaseOrderService;
            this.purchaseOrderItemService = purchaseOrderItemService;
            this.purchaseOrderCreditNoteItemService = purchaseOrderCreditNoteItemService;
            this.userService = userService;
            this.jobService = jobService;
            this.jobInvoiceService = jobInvoiceService;
            this.jobInvoiceItemService = jobInvoiceItemService;
            this.orderService = orderService;
            this.currencyRateService = currencyRateService;
            this.logger = logger;
            this.unitOfWork = unitOfWork;
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(PurchaseOrderCreditNoteItemModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id)
        {
            var purchaseOrderItem = await purchaseOrderCreditNoteItemService.GetByIdAsync(id);
            return Ok(purchaseOrderItem);
        }

        [HttpGet("purchaseOrder/{id}")]
        [ProducesResponseType(200, Type = typeof(PurchaseOrderCreditNoteItemModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetItemsByPurchaseOrder(Guid id)
        {
            var purchaseOrderCreditNoteItems = await purchaseOrderCreditNoteItemService.GetItemsByPurchaseOrderIdAsync(id);
            return Ok(purchaseOrderCreditNoteItems);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(PurchaseOrderCreditNoteItemModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody] PurchaseOrderCreditNoteItemUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }
            try
            {

                var purchaseOrderCreditNoteItem = await purchaseOrderCreditNoteItemService.PostAsync(model);
                var purchaseOrderItem = await purchaseOrderItemService.GetByIdAsync(model.PurchaseOrderItemId);
                var purchaseOrder = purchaseOrderItem.PurchaseOrder;
                var currencyRateCurrency = await currencyRateService.GetByCurrencyAsync(purchaseOrderItem.Currency);
                var currencyRate = currencyRateCurrency != null ? currencyRateCurrency.Rate : 0;

                if (purchaseOrderItem.Currency == "Euro" || purchaseOrderItem.Currency == "US Dollar")
                {
                    purchaseOrderCreditNoteItem.TotalNet = purchaseOrderCreditNoteItem.TotalNet / currencyRate;
                }

                if (!string.IsNullOrWhiteSpace(purchaseOrderItem.JobNumber))
                {
                    var order = await orderService.GetByNumber(purchaseOrderItem.JobNumber);
                    if (order == null)
                    {
                        return BadRequest();
                    }

                    var job = await jobService.GetByOrderIdAsync(order.OrderId);
                    var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
                    if (emailClaim == null)
                    {
                        return Unauthorized();
                    }

                    var user = await userService.GetUserByEmailAsync(emailClaim.Value);
                    var jobInvoice = new JobInvoiceModel();

                    if (job == null)
                    {
                        job = await jobService.PostAsync(new JobModel
                        {
                            CreatedBy = user != null ? $"{user.Firstname} {user.Lastname}" : null,
                            OrderId = order.OrderId,
                            CostCentre = user.Location == "Oldmeldrum" ? "001" : user.Location == "West Bromwich" ? "002" : "001",
                            OrderType = order.Type,
                            OrderNumber = order.Number,
                            OrderCustomerName = order.Customer != null ? order.Customer.Name : string.Empty,
                            Description = order.FirstItemDescription,
                            DateOrderRaised = order.Created,
                            Currency = order.Currency
                        });
                    }

                    var existingJobInvoices = await jobInvoiceService.GetByJobIdAsync(job.JobId);

                    int creditInvoicesCount = existingJobInvoices != null && existingJobInvoices.Any(job => job.JobType.Contains("CREDIT")) ?
                                           existingJobInvoices.Where(w => w.JobType.Contains("CREDIT")).Count() : 0;

                    string nextCreditNoteNumber = $"{order.Number}/CR{creditInvoicesCount + 1}";

                    jobInvoice.JobId = job.JobId;
                    jobInvoice.CreatedBy = $"{user.Firstname} {user.Lastname}";
                    jobInvoice.JobType = "PURCHASE CREDIT";
                    jobInvoice.InvoiceNumber = nextCreditNoteNumber;
                    jobInvoice.InvoiceDate = model.CreditNoteDate;
                    jobInvoice.CustomerPO = order != null ? order.CustomerRef : string.Empty;
                    jobInvoice.InvoiceDate = model.CreditNoteDate;

                    if (!await jobInvoiceService.JobInvoiceNumberExistsAsync(jobInvoice.InvoiceNumber))
                    {
                        jobInvoice = await jobInvoiceService.PostAsync(jobInvoice);
                    } else
                    {
                        return BadRequest($"Invoice Number already exists in the system");
                    }

                    var jobInvoiceItem = new JobInvoiceItemModel
                    {
                        PurchaseOrderSupplierName = purchaseOrder != null && purchaseOrder.Supplier != null ? purchaseOrder.Supplier.Name : null,
                        JobId = job.JobId,
                        JobInvoiceId = jobInvoice.JobInvoiceId,
                        PurchaseOrderCreditNoteItemId = purchaseOrderCreditNoteItem.PurchaseOrderCreditNoteItemId,
                        InvoiceNumber = jobInvoice.InvoiceNumber,
                        InvoiceType = "PURCHASE CREDIT",
                        Quantity = 1,
                        UnitPrice = purchaseOrderCreditNoteItem.TotalNet,
                        StockCategory = string.Empty,
                        StockCode = string.Empty,
                        Value = purchaseOrderCreditNoteItem.TotalNet,
                        Currency = order.Currency,
                        Description = purchaseOrderItem != null ? purchaseOrderItem.Description : "PO CREDIT ITEM",
                        OrderItemId = purchaseOrderItem != null ? purchaseOrderItem.PurchaseOrderItemId : Guid.NewGuid(),
                        OrderItemStockItemId = null,
                        QuantityToDeliver = 0,
                        ToFollow = 0,
                        Vat = model.VatCode,
                        AccountCode = model.AccountCode,
                        IsStockItem = false,
                        InvoiceDate = jobInvoice.InvoiceDate,
                        SaleOrderNumber = order.Number,
                        IsDeliveryCharge = false,
                        IsInitialDeliveryCharge = false
                    };

                    await jobInvoiceItemService.PostAsync(jobInvoiceItem);
                }

                return Ok(purchaseOrderCreditNoteItem);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot create Purchase Order - {ex}");
                return BadRequest();
            }
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(PurchaseOrderCreditNoteItemModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Put(Guid id, [FromBody] PurchaseOrderCreditNoteItemUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }
            var purchaseOrderCreditNoteItem = await purchaseOrderCreditNoteItemService.GetByIdAsync(id);
            if (purchaseOrderCreditNoteItem is null)
            {
                return BadRequest();
            }
            try
            {
                var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                      c.Type == ClaimTypes.Email);
                if (emailClaim == null)
                {
                    return Unauthorized();
                }

                var user = await userService.GetUserByEmailAsync(emailClaim.Value);

                purchaseOrderCreditNoteItem = await purchaseOrderCreditNoteItemService.PutAsync(id, model);

                return Ok(purchaseOrderCreditNoteItem);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot update Purchase Order Credit Note Item - {ex}");
                return BadRequest();
            }
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Delete(Guid id)
        {
            var jobInvoiceItem = await jobInvoiceItemService.GetByPurchaseOrderCreditNoteItemIdAsync(id);

            if (jobInvoiceItem != null)
            {
                var jobInvoice = await jobInvoiceService.GetAsync(jobInvoiceItem.JobInvoiceId.Value);
                if (jobInvoice != null && jobInvoice.JobInvoiceItems != null && jobInvoice.JobInvoiceItems.Any())
                {
                    if (jobInvoice.JobInvoiceItems.Count > 1)
                    {
                        await jobInvoiceItemService.DeleteAsync(jobInvoiceItem.JobInvoiceItemId.Value);
                    } else
                    {
                        await jobInvoiceItemService.DeleteAsync(jobInvoiceItem.JobInvoiceItemId.Value);
                        await jobInvoiceService.DeleteAsync(jobInvoice.JobInvoiceId);
                    }
                }

            }

            await purchaseOrderCreditNoteItemService.DeleteAsync(id);

            return Ok();
        }

        [HttpPut("pushToXero/{id}")]
        [ProducesResponseType(200, Type = typeof(PurchaseOrderCreditNoteItemModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> PushToXero(Guid id)
        {
            var purchaseOrderCreditNoteItem = await unitOfWork.PurchaseOrderCreditNoteItem.Query().Where(c => c.PurchaseOrderCreditNoteItemId == id).Include(x => x.PurchaseOrderItem).ThenInclude(x => x.PurchaseOrder).ThenInclude(x => x.Supplier).FirstOrDefaultAsync();
            var supplierXeroContactId = purchaseOrderCreditNoteItem.PurchaseOrderItem.PurchaseOrder.Supplier.XeroContactId;
            try
            {
                await purchaseOrderCreditNoteItemService.PushToXero(id, supplierXeroContactId.Value);
            } catch
            {
                return BadRequest();
            }

            return Ok();
        }

        [HttpPost("pushAllToXero")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> PushAllToXero(List<PurchaseOrderCreditNoteItemModel> models)
        {

            var purchaseOrderCreditNoteItem = await unitOfWork.PurchaseOrderCreditNoteItem.Query().Where(c => c.PurchaseOrderCreditNoteItemId == models[0].PurchaseOrderCreditNoteItemId).Include(x => x.PurchaseOrderItem).ThenInclude(x => x.PurchaseOrder).ThenInclude(x => x.Supplier).FirstOrDefaultAsync();
            var supplierXeroContactId = purchaseOrderCreditNoteItem.PurchaseOrderItem.PurchaseOrder.Supplier.XeroContactId;
            try
            {
                await purchaseOrderCreditNoteItemService.PushAllToXero(models, supplierXeroContactId.Value);
                return Ok();
            } catch
            {
                return BadRequest();
            }
        }
    }
}

