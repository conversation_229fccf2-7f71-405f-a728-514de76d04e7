﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Red20.Model.Data.Order;
using Red20.Service.Data.Interface;

namespace Red20.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class OrderItemWorkSiteSheetController : ControllerBase
    {
        private IOrderItemWorkSiteSheetService service;
        private ILogger<AuthController> logger;

        public OrderItemWorkSiteSheetController(
            IOrderItemWorkSiteSheetService service,
            ILogger<AuthController> logger) {
            this.service = service;
            this.logger = logger;
        }

        [HttpGet("byItem/{id}")]
        [ProducesResponseType(200, Type = typeof(OrderItemWorkSiteSheetModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id) {
            var workSiteSheet = await service.GetByOrderItemAsync(id);
            return Ok(workSiteSheet);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(OrderItemWorkSiteSheetModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody]OrderItemWorkSiteSheetUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }

            if (model.Date.HasValue) {
                model.Date = model.Date.Value.AddHours(2);
            }


            try {
                var workSiteSheet = await service.PostAsync(model);
                return Ok(workSiteSheet);
            } catch (Exception ex) {
                logger.LogError($"Cannot create Work Site Sheet - {ex}");
                return BadRequest();
            }
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(OrderItemWorkSiteSheetModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Put(Guid id, [FromBody]OrderItemWorkSiteSheetUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }

            var workSiteSheet = await service.GetAsync(id);
            if (workSiteSheet is null) {
                return BadRequest();
            }

            if (model.Date != workSiteSheet.Date) {
                model.Date = model.Date.HasValue ? model.Date.Value.AddHours(2) : (DateTime?)null;
            }

            try {
                workSiteSheet = await service.PutAsync(id, model);
                return Ok(workSiteSheet);
            } catch (Exception ex) {
                logger.LogError($"Cannot update Work Site Sheet - {ex}");
                return BadRequest();
            }
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Delete(Guid id) {
            if(!await service.OrderItemWorkSiteSheetIdExistsAsync(id)) {
                return BadRequest();
            }
            await service.DeleteAsync(id);
            return Ok();
        }
    }
}