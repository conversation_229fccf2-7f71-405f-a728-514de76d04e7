﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace Red20.Data.Migrations
{
    public partial class TimesheetEntryStatus : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "FridayStatus",
                table: "UserTimesheetEntries",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "MondayStatus",
                table: "UserTimesheetEntries",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SaturdayStatus",
                table: "UserTimesheetEntries",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SundayStatus",
                table: "UserTimesheetEntries",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ThursdayStatus",
                table: "UserTimesheetEntries",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "TuesdayStatus",
                table: "UserTimesheetEntries",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "WednesdayStatus",
                table: "UserTimesheetEntries",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "FridayStatus",
                table: "UserTimesheetEntries");

            migrationBuilder.DropColumn(
                name: "MondayStatus",
                table: "UserTimesheetEntries");

            migrationBuilder.DropColumn(
                name: "SaturdayStatus",
                table: "UserTimesheetEntries");

            migrationBuilder.DropColumn(
                name: "SundayStatus",
                table: "UserTimesheetEntries");

            migrationBuilder.DropColumn(
                name: "ThursdayStatus",
                table: "UserTimesheetEntries");

            migrationBuilder.DropColumn(
                name: "TuesdayStatus",
                table: "UserTimesheetEntries");

            migrationBuilder.DropColumn(
                name: "WednesdayStatus",
                table: "UserTimesheetEntries");
        }
    }
}
