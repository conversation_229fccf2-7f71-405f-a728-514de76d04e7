﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Claims;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Ionic.Zip;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Red20.Model.Data.Job;
using Red20.Model.Data.Order;
using Red20.Service;
using Red20.Service.Data.Interface;
using Syncfusion.DocIO.DLS;
using Syncfusion.Pdf;

namespace Red20.Controllers {
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class ReportController : ControllerBase {
        private IQuoteService quoteService;
        private IOrderService orderService;
        private IPurchaseRequisitionService purchaseRequisitionService;
        private IPurchaseOrderService purchaseOrderService;
        private IEnquiryService enquiryService;
        private IQuoteItemService quoteItemService;
        private ReportService reportService;
        private ILogger<ReportController> logger;
        private IOrderItemService orderItemService;
        private IOrderItemStockItemService stockItemService;
        private IOrderItemJobRecordSheetService jobRecordSheetService;
        private IOrderItemWorkSiteSheetService workSiteSheetService;
        private IOrderAssemblyService assemblyService;
        private IOrderAssemblyHirePreparationSheetService hpsService;
        private IUserService userService;
        private ICustomerComplaintService complaintService;
        private IJobInvoiceService jobInvoiceService;
        private IJobInvoiceItemService jobInvoiceItemService;
        private IJobService jobService;

        public ReportController(
            IQuoteService quoteService,
            IOrderService orderService,
            IEnquiryService enquiryService,
            IQuoteItemService quoteItemService,
            IPurchaseRequisitionService purchaseRequisitionService,
            IPurchaseOrderService purchaseOrderService,
            ReportService reportService,
            ILogger<ReportController> logger,
            IOrderItemService orderItemService,
            IOrderItemStockItemService stockItemService,
            IOrderItemJobRecordSheetService jobRecordSheetService,
            IOrderItemWorkSiteSheetService workSiteSheetService,
            IOrderAssemblyService assemblyService,
            IOrderAssemblyHirePreparationSheetService hpsService,
            IUserService userService,
            ICustomerComplaintService complaintService,
            IJobInvoiceService jobInvoiceService,
            IJobInvoiceItemService jobInvoiceItemService,
            IJobService jobService) {

            this.quoteService = quoteService;
            this.orderService = orderService;
            this.quoteItemService = quoteItemService;
            this.enquiryService = enquiryService;
            this.reportService = reportService;
            this.purchaseRequisitionService = purchaseRequisitionService;
            this.purchaseOrderService = purchaseOrderService;
            this.logger = logger;
            this.orderItemService = orderItemService;
            this.stockItemService = stockItemService;
            this.jobRecordSheetService = jobRecordSheetService;
            this.workSiteSheetService = workSiteSheetService;
            this.assemblyService = assemblyService;
            this.hpsService = hpsService;
            this.userService = userService;
            this.complaintService = complaintService;
            this.jobInvoiceService = jobInvoiceService;
            this.jobInvoiceItemService = jobInvoiceItemService;
            this.jobService = jobService;
        }

        [HttpGet("quotePreview/{id}")]
        [ProducesResponseType(200, Type = typeof(File))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> QuotePreview(Guid id) {
            if (!await quoteService.QuoteIdExistsAsync(id)) {
                return NotFound();
            }

            var firstName = "";
            var lastName = "";

            var quote = await quoteService.GetAsync(id);

            var createdBy = quote.CreatedBy;

            string[] createdBySplit = createdBy.Split(" ");
            if (createdBySplit.Length == 2) {
                firstName = !string.IsNullOrWhiteSpace(quote.CreatedBy) ? quote.CreatedBy.Split(" ")[0] : string.Empty;
                lastName = !string.IsNullOrWhiteSpace(quote.CreatedBy) ? quote.CreatedBy.Split(" ")[1] : string.Empty;
            } else if (createdBySplit.Length == 3) {
                firstName = !string.IsNullOrWhiteSpace(quote.CreatedBy) ? $"{quote.CreatedBy.Split(" ")[0]} {quote.CreatedBy.Split(" ")[1]}" : string.Empty;
                lastName = !string.IsNullOrWhiteSpace(quote.CreatedBy) ? quote.CreatedBy.Split(" ")[2] : string.Empty;
            }

            var user = await userService.GetByNameAsync(firstName, lastName);

            var quoteReportModel = await quoteService.GetQuoteReportModel(id, $"{user.Firstname} {user.Lastname}", user.EmailAddress);

            var stream = reportService.GetReport(quoteReportModel, null, quoteReportModel.IsIssued is false, quoteReportModel.Type == "Hire");

            return File(stream, "application/pdf", $"Quote_Preview{Regex.Replace(quoteReportModel.Number, "[^a - zA - Z0 - 9_] + ", "")}_{DateTime.Now.ToString("ddMMMyyyy")}.pdf");
        }

        [HttpGet("orderPreview/{id}")]
        [ProducesResponseType(200, Type = typeof(File))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> OrderPreview(Guid id) {
            if (!await orderService.OrderIdExistsAsync(id)) {
                return NotFound();
            }
            var firstName = "";
            var lastName = "";
            var order = await orderService.GetAsync(id);
            var createdBy = order.CreatedBy;
            string[] createdBySplit = createdBy.Split(" ");
            if (createdBySplit.Length == 2) {
                firstName = !string.IsNullOrWhiteSpace(order.CreatedBy) ? order.CreatedBy.Split(" ")[0] : string.Empty;
                lastName = !string.IsNullOrWhiteSpace(order.CreatedBy) ? order.CreatedBy.Split(" ")[1] : string.Empty;
            } else if (createdBySplit.Length == 3) {
                firstName = !string.IsNullOrWhiteSpace(order.CreatedBy) ? $"{order.CreatedBy.Split(" ")[0]}" : string.Empty;
                lastName = !string.IsNullOrWhiteSpace(order.CreatedBy) ? order.CreatedBy.Split(" ")[2] : string.Empty;
            }

            var user = await userService.GetByNameAsync(firstName, lastName);
            var orderReportModel = await orderService.GetOrderReportModel(id, user.EmailAddress);

            var stream = reportService.GetOrderReport(orderReportModel, null, false, orderReportModel.Type == "Hire", orderReportModel.IsInternal);

            return File(stream, "application/pdf", $"Quote_Preview{Regex.Replace(orderReportModel.OrderNumber, "[^a - zA - Z0 - 9_] + ", "")}_{DateTime.Now.ToString("ddMMMyyyy")}.pdf");
        }

        [HttpGet("profroma/{id}")]
        [ProducesResponseType(200, Type = typeof(File))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> ProformaOrderPreview(Guid id) {
            if (!await orderService.OrderIdExistsAsync(id)) {
                return NotFound();
            }
            var firstName = "";
            var lastName = "";
            var order = await orderService.GetAsync(id);
            var createdBy = order.CreatedBy;
            string[] createdBySplit = createdBy.Split(" ");
            if (createdBySplit.Length == 2) {
                firstName = !string.IsNullOrWhiteSpace(order.CreatedBy) ? order.CreatedBy.Split(" ")[0] : string.Empty;
                lastName = !string.IsNullOrWhiteSpace(order.CreatedBy) ? order.CreatedBy.Split(" ")[1] : string.Empty;
            } else if (createdBySplit.Length == 3) {
                firstName = !string.IsNullOrWhiteSpace(order.CreatedBy) ? $"{order.CreatedBy.Split(" ")[0]}" : string.Empty;
                lastName = !string.IsNullOrWhiteSpace(order.CreatedBy) ? order.CreatedBy.Split(" ")[2] : string.Empty;
            }

            var user = await userService.GetByNameAsync(firstName, lastName);
            var orderReportModel = await orderService.GetProformaReportModel(id, user.EmailAddress);
            try {
                var stream = reportService.GetProformaOrderReport(orderReportModel);

                return File(stream, "application/pdf", $"Quote_Preview{Regex.Replace(orderReportModel.OrderNumber, "[^a - zA - Z0 - 9_] + ", "")}_{DateTime.Now.ToString("ddMMMyyyy")}.pdf");
            } catch (Exception ex){
                return BadRequest();
            }
        }

        [HttpGet("jobCosting/{id}")]
        [ProducesResponseType(200, Type = typeof(File))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> JobCosting(Guid id) {
            if (!await jobService.JobIdExistsAsync(id)) {
                return NotFound();
            }

            var jobReportModel = await jobService.GetJobCostingReportModel(id);

            var stream = reportService.GetJobCostingReport(jobReportModel);

            return File(stream, "application/pdf", $"JobCosting_{Regex.Replace(jobReportModel.OrderNumber, "[^a - zA - Z0 - 9_] + ", "")}_{DateTime.Now.ToString("ddMMMyyyy")}.pdf");
        }

        [HttpPost("jobCostingNew")]
        [ProducesResponseType(200, Type = typeof(File))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> JobCosting([FromBody] JobModel job)
        {
            var jobReportModel = await jobService.GetJobCostingReportModelNew(job);

            var stream = reportService.GetJobCostingReport(jobReportModel);

            return File(stream, "application/pdf", $"JobCosting_{Regex.Replace(jobReportModel.OrderNumber, "[^a - zA - Z0 - 9_] + ", "")}_{DateTime.Now.ToString("ddMMMyyyy")}.pdf");
        }

        [HttpGet("purchaseRequisitionPreview/{id}")]
        [ProducesResponseType(200, Type = typeof(File))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> PurchaseRequisitionPreview(Guid id) {
            if (!await purchaseRequisitionService.PurchaseRequisitionIdExistsAsync(id)) {
                return NotFound();
            }

            try {
                var purchaseRequisitionReportModel = await purchaseRequisitionService.GetPurchaseRequisitionReportModel(id);
                var stream = reportService.GetReport(purchaseRequisitionReportModel, null, false, false, true);

                return File(stream, "application/pdf", $"Purchase_Requisition_Preview{Regex.Replace(purchaseRequisitionReportModel.Number, "[^a - zA - Z0 - 9_] + ", "")}_{DateTime.Now.ToString("ddMMMyyyy")}.pdf");

            } catch (Exception ex) {
                logger.LogError($"Cannot print pdf - {ex}");
                return BadRequest();
            }
        }

        [HttpGet("purchaseOrderPreview/{id}/{type}")]
        [ProducesResponseType(200, Type = typeof(File))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> PurchaseOrderPreview(Guid id, string type) {
            if (!await purchaseOrderService.PurchaseOrderIdExistsAsync(id)) {
                return NotFound();
            }

            try {
                var purchaseOrderReportModel = await purchaseOrderService.GetPurchaseOrderReportModel(id);
                if(type== "All") {
                    var stream = reportService.GetAllPurchaseOrderReport(purchaseOrderReportModel);
                    return File(stream, "application/pdf", $"Purchase_Order_Preview{Regex.Replace(purchaseOrderReportModel.Number, "[^a - zA - Z0 - 9_] + ", "")}_{DateTime.Now.ToString("ddMMMyyyy")}.pdf");
                }
                else {
                    var stream = reportService.GetPurchaseOrderReport(purchaseOrderReportModel, type);
                    return File(stream, "application/pdf", $"Purchase_Order_Preview{Regex.Replace(purchaseOrderReportModel.Number, "[^a - zA - Z0 - 9_] + ", "")}_{DateTime.Now.ToString("ddMMMyyyy")}.pdf");
                }

            } catch (Exception ex) {
                logger.LogError($"Cannot print pdf - {ex}");
                return BadRequest();
            }
        }

        [HttpGet("complaintPreview/{id}/{type}")]
        [ProducesResponseType(200, Type = typeof(File))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> ComplaintPreview(Guid id, string type) {

            try {
                var complineReportModel = await complaintService.GetCustomerComplaintReportModel(id);
                    var stream = reportService.GetComplaintReport(complineReportModel, type);
                    return File(stream, "application/pdf", $"Complaint_Preview{Regex.Replace(complineReportModel.Number, "[^a - zA - Z0 - 9_] + ", "")}_{DateTime.Now.ToString("ddMMMyyyy")}.pdf");

            } catch (Exception ex) {
                logger.LogError($"Cannot print pdf - {ex}");
                return BadRequest();
            }
        }

        [HttpGet("jobRecordSheet/{id}")]
        [ProducesResponseType(200, Type = typeof(File))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> JobRecordSheetReport(Guid id) {
            //id param is OrderItemId. used to get related stock items and job record sheet details
            if (!await orderItemService.OrderItemIdExistsAsync(id)) {
                return NotFound();
            }

            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);

            var orderItem = await orderItemService.GetAsync(id);
            if (orderItem is null) {
                return BadRequest();
            }

            var jobRecordSheet = await jobRecordSheetService.GetByOrderItemAsync(id);
            var stockItems = await stockItemService.GetByOrderItemAsync(id);

            var jobRecordSheetReportModel = await jobRecordSheetService.GetJobRecordSheetReportModel(id, $"{user.Firstname} {user.Lastname}", jobRecordSheet, stockItems, 0);

            var stream = reportService.GetJobRecordSheetReport(jobRecordSheetReportModel, null, jobRecordSheetReportModel.OrderItemStockItem != null && jobRecordSheetReportModel.OrderItemStockItem.Any());

            return File(stream, "application/pdf", $"JobRecordSheeet_{Regex.Replace(jobRecordSheetReportModel.SalesOrderNumber, "[^a - zA - Z0 - 9_] + ", "")}_{DateTime.Now.ToString("ddMMMyyyy")}.pdf");
        }

        [HttpGet("jobRecordSheetPdfs/{id}")]
        [ProducesResponseType(200, Type = typeof(File))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> JobRecordSheetReports(Guid id) {
            var order = await orderService.GetAsync(id);
            var orderItems = order.OrderItems;

            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);

            var jobRecordSheets = new List<OrderItemJobRecordSheetModel>();
            var jobRecordSheetReportModels = new List<JobRecordSheetReportModel>();

            int count = 1;

            if (orderItems != null && orderItems.Any()) {
                foreach(var orderItem in orderItems.OrderBy(o => o.Created)) {
                    var jobRecordSheet = await jobRecordSheetService.GetByOrderItemAsync(orderItem.OrderItemId);
                    if(jobRecordSheet.OrderItemId != null && jobRecordSheet.OrderItemId != Guid.Empty) {
                        var stockItems = await stockItemService.GetJobRecordSheetStockItemsAsync(orderItem.OrderItemId, "jobRecordSheet");
                        var jobRecordSheetReportModel = await jobRecordSheetService.GetJobRecordSheetReportModel(orderItem.OrderItemId, $"{user.Firstname} {user.Lastname}", jobRecordSheet, stockItems, count);
                        jobRecordSheetReportModels.Add(jobRecordSheetReportModel);
                        count++;
                    }
                }
            }

            List<MemoryStream> streams = new List<MemoryStream>();
            MemoryStream finalStream = new MemoryStream();
            PdfDocument pdfDocument = new PdfDocument();
            pdfDocument.EnableMemoryOptimization = false;
            if (jobRecordSheetReportModels != null && jobRecordSheetReportModels.Any()) {
                foreach(var reportModel in jobRecordSheetReportModels) {
                    reportModel.JobSheetDescription = !string.IsNullOrWhiteSpace(reportModel.JobSheetDescription) ? reportModel.JobSheetDescription.Replace("<li><br></li>", "") : string.Empty;
                    var stream = reportService.GetJobRecordSheetReport(
                        reportModel,
                        null,
                        reportModel.OrderItemStockItem != null && reportModel.OrderItemStockItem.Any());

                    streams.Add(stream);
                }
                PdfDocumentBase.Merge(pdfDocument, streams.ToArray());

                pdfDocument.Save(finalStream);
                pdfDocument.Close(true);
                finalStream.Position = 0;
            }
            return File(finalStream, "application/pdf", $"JobRecordSheeet_{Regex.Replace(order.Number, "[^a - zA - Z0 - 9_] + ", "")}_{DateTime.Now.ToString("ddMMMyyyy")}.pdf");
        }

        [HttpGet("workSiteSheetPdfs/{id}")]
        [ProducesResponseType(200, Type = typeof(File))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> WorkSiteSheetReports(Guid id) {
            var order = await orderService.GetAsync(id);
            var orderItems = order.OrderItems;

            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);

            var workSiteSheets = new List<OrderItemWorkSiteSheetModel>();
            var workSiteSheetReportModels = new List<WorkSiteSheetReportModel>();

            int count = 1;

            if (orderItems != null && orderItems.Any()) {
                foreach (var orderItem in orderItems.OrderBy(o => o.Created)) {
                    var workSiteSheet = await workSiteSheetService.GetByOrderItemAsync(orderItem.OrderItemId);
                    if (workSiteSheet.OrderItemId != null && workSiteSheet.OrderItemId != Guid.Empty) {
                        var stockItems = await stockItemService.GetWorkSiteSheetStockItemsAsync(orderItem.OrderItemId, "workSiteSheet");
                        var workSiteSheetReportModel = await workSiteSheetService.GetWorkSiteSheetReportModel(orderItem.OrderItemId, $"{user.Firstname} {user.Lastname}", workSiteSheet, stockItems, count);
                        workSiteSheetReportModels.Add(workSiteSheetReportModel);
                        count++;
                    }
                }
            }

            List<MemoryStream> streams = new List<MemoryStream>();
            MemoryStream finalStream = new MemoryStream();

            if (workSiteSheetReportModels != null && workSiteSheetReportModels.Any()) {
                foreach (var reportModel in workSiteSheetReportModels) {
                    var stream = reportService.GetWorkSiteSheetReport(reportModel);
                    streams.Add(stream);
                }
            }

            var outputStream = new MemoryStream();

            using (var zip = new ZipFile()) {
                    int index = 1;
                foreach(var stream in streams) {
                    zip.AddEntry($"WorkSiteSheet_{Regex.Replace(order.Number, "[^a - zA - Z0 - 9_] + ", "")}_{DateTime.Now.ToString("ddMMMyyyy")}_{index}.docx", stream);
                    index++;
                }

                zip.Save(outputStream);
            }

            outputStream.Position = 0;
            return File(outputStream, "application/zip", "WorkSiteSheetReports.zip");
        }

        [HttpPost("hirePrepSheetPdfs")]
        [ProducesResponseType(200, Type = typeof(File))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> HirePrepSheetPdfs(OrderAssemblyHirePreparationSheetUpdateModel hpsModel) {
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                      c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);

            var hpsReportModels = new List<OrderAssemblyHPSReportModel>();

            if(hpsModel is null) {
                return BadRequest();
            }

            var orderAssemblies = hpsModel.OrderId.HasValue ?
                await assemblyService.GetByOrderAsync(hpsModel.OrderId.Value) :
                new List<OrderAssemblyModel>();

            if (hpsModel.OrderAssemblyIds != null && hpsModel.OrderAssemblyIds.Any()) {
                int count = 1;
                foreach (var assemblyId in hpsModel.OrderAssemblyIds) {
                    var assembly = await assemblyService.GetAsyncForHirePrepSheet(assemblyId);

                    int countt = orderAssemblies != null && orderAssemblies.Any() ?
                        orderAssemblies.ToList().FindIndex(a => a.OrderAssemblyId == assembly.OrderAssemblyId) + 1 : count;

                    if(assembly != null && !assembly.IsSimpleAssembly) {

                        OrderAssemblyHPSReportModel reportModel = new OrderAssemblyHPSReportModel {
                            Classification = hpsModel.Classification,
                            JobNumber = assembly.Order != null ? $"{assembly.Order.Number} / {countt.ToString()}" : string.Empty,
                            JobNumberAssemblyNumber = assembly.Order != null ? $"{assembly.Order.Number} / {countt.ToString()}" : string.Empty,
                            Customer = assembly.Order != null ? assembly.Order.CustomerName : string.Empty,
                            CustomerRef = assembly.Order != null ? assembly.Order.CustomerRef : string.Empty,
                            HireStartDate = assembly.HireStart.HasValue ? assembly.HireStart.Value.ToString("dd MMMMM yyyy") : string.Empty,
                            DispatchDate = assembly.Order != null && assembly.Order.TargetDispatch.HasValue ? assembly.Order.TargetDispatch.Value.ToString("dd MMMMM yyyy") : string.Empty,
                            InspectedBy = assembly.InspectedBy,
                            PreparedBy = assembly.PreparedBy,
                            Description = !string.IsNullOrWhiteSpace(assembly.Description) ? $"<div style='font-size:9pt; font-family:Arial'>{assembly.Description.Replace("<p><br></p>", "<p></p>").Replace("<p>", "").Replace("</p>", "<br/>")}</div>" : string.Empty,
                            SWL = $"{hpsModel.SWL} {hpsModel.SWLUnit}",
                            Proof = $"{hpsModel.ProofLoad} {hpsModel.ProofLoadUnit}",
                            ControlType = hpsModel.ControlType,
                            AirService = hpsModel.AirService,
                            FittingType = hpsModel.FittingType,
                            ColourCode = hpsModel.ColourCode,
                            ControlLength = $"{hpsModel.ControlLength} metres",
                            ChainCollector = hpsModel.ChainCollector,
                            TrolleyType = hpsModel.TrolleyType,
                            TrolleyFlangeSize = hpsModel.TrolleyFlangeSize,
                            SpecialInstructions = hpsModel.Type == "AirHoist" ? hpsModel.SpecialInstructions : hpsModel.SpecialInstructionsLoadCell,
                            HireEndDate = assembly.HireEnd.HasValue ? assembly.HireEnd.Value.ToString("dd MMMMM yyyy") : string.Empty,
                            ReceivedDate = assembly.ReceivedDate.HasValue ? assembly.ReceivedDate.Value.ToString("dd MMMMM yyyy") : string.Empty,
                            Capacity = hpsModel.Capacity,
                            Model = hpsModel.Model,
                            ThirdPartyRelease = hpsModel.ThirdPartyRelease
                        };
                        count++;
                        hpsReportModels.Add(reportModel);
                    }
                }
            }

            List<MemoryStream> streams = new List<MemoryStream>();
            MemoryStream finalStream = new MemoryStream();
            PdfDocument pdfDocument = new PdfDocument();
            pdfDocument.EnableMemoryOptimization = false;

            if (hpsReportModels != null && hpsReportModels.Any()) {
                foreach (var reportModel in hpsReportModels) {
                    var stream = reportService.GetHirePrepSheetReport(reportModel, hpsModel.Type, null);
                    streams.Add(stream);
                }
                PdfDocumentBase.Merge(pdfDocument, streams.ToArray());

                pdfDocument.Save(finalStream);
                pdfDocument.Close(true);
                finalStream.Position = 0;
            }
            return File(finalStream, "application/pdf", $"HirePreparationSheet_{Regex.Replace("", "[^a - zA - Z0 - 9_] + ", "")}_{DateTime.Now.ToString("ddMMMyyyy")}.pdf");
        }

        [HttpPost("hireDeliveryNote")]
        [ProducesResponseType(200, Type = typeof(File))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> HireDeliveryNote([FromBody] OrderModel model) {
            if (!await orderService.OrderIdExistsAsync(model.OrderId)) {
                return NotFound();
            }

            var deliveryNoteReportModels = new List<HireDeliveryNoteReportModel>();

            var deliveryNoteCustomerReportModel = orderService.GetHireDeliveryNoteReportModel(model, "customer");
            var deliveryNoteOfficeReportModel = orderService.GetHireDeliveryNoteReportModel(model, "office");

            deliveryNoteReportModels.Add(deliveryNoteOfficeReportModel);
            deliveryNoteReportModels.Add(deliveryNoteCustomerReportModel);

            List<MemoryStream> streams = new List<MemoryStream>();
            MemoryStream finalStream = new MemoryStream();
            PdfDocument pdfDocument = new PdfDocument();
            pdfDocument.EnableMemoryOptimization = false;

            if (deliveryNoteReportModels != null && deliveryNoteReportModels.Any()) {
                foreach (var reportModel in deliveryNoteReportModels) {
                    var stream = reportService.GetDeliveryNote(reportModel, reportModel.Type);
                    streams.Add(stream);
                }
                PdfDocumentBase.Merge(pdfDocument, streams.ToArray());

                //if(pdfDocument.PageCount > 2) {
                //    streams.Clear();
                //    pdfDocument = new PdfDocument();
                //    pdfDocument.EnableMemoryOptimization = false;

                //    foreach (var reportModel in deliveryNoteReportModels) {
                //        var stream = reportService.GetDeliveryNoteTwoPages(reportModel, reportModel.Type);
                //        streams.Add(stream);
                //    }

                //    PdfDocumentBase.Merge(pdfDocument, streams.ToArray());
                //}

                pdfDocument.Save(finalStream);
                pdfDocument.Close(true);
                finalStream.Position = 0;
            }

            return File(finalStream, "application/pdf", $"Quote_Preview{Regex.Replace(model.OrderNumber, "[^a - zA - Z0 - 9_] + ", "")}_{DateTime.Now.ToString("ddMMMyyyy")}.pdf");
        }

        [HttpPost("saleDeliveryNote")]
        [ProducesResponseType(200, Type = typeof(File))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> SaleDeliveryNote([FromBody]SaleDeliveryNoteModel model) {
            if (!await jobInvoiceService.JobInvoiceNumberExistsAsync(model.InvoiceNumber)) {
                return NotFound();
            }

            var invoice = await jobInvoiceService.GetByInvoiceNumberAsync(model.InvoiceNumber);
            if(invoice is null) {
                return NotFound();
            }

            var job = await jobService.GetByJobIdForSaleDeliveryNoteAsync(invoice.JobId);
            if(job == null) {
                return BadRequest();
            }

            MemoryStream finalStream = new MemoryStream();
            if (job.OrderType == "Sale") {
                var order = await orderService.GetAsync(job.OrderId);

                var deliveryNoteReportModels = new List<SaleDeliveryNoteReportModel>();

                var deliveryNoteReportModel = orderService.GetSaleDeliveryNoteReportModel(invoice, order);

                deliveryNoteReportModels.Add(deliveryNoteReportModel);

                List<MemoryStream> streams = new List<MemoryStream>();

                PdfDocument pdfDocument = new PdfDocument();
                pdfDocument.EnableMemoryOptimization = false;

                if (deliveryNoteReportModels != null && deliveryNoteReportModels.Any()) {
                    foreach (var reportModel in deliveryNoteReportModels) {
                        var stream = reportService.GetSaleDeliveryNote(reportModel);
                        streams.Add(stream);
                    }
                    PdfDocumentBase.Merge(pdfDocument, streams.ToArray());

                    if (pdfDocument.PageCount > 1) {
                        streams.Clear();
                        pdfDocument = new PdfDocument();
                        pdfDocument.EnableMemoryOptimization = false;

                        foreach (var reportModel in deliveryNoteReportModels) {
                            var stream = reportService.GetSaleDeliveryNoteTwoPages(reportModel);
                            streams.Add(stream);
                        }

                        PdfDocumentBase.Merge(pdfDocument, streams.ToArray());
                    }

                    pdfDocument.Save(finalStream);
                    pdfDocument.Close(true);
                    finalStream.Position = 0;
                }
            }


            return File(finalStream, "application/pdf", $"Quote_Preview{Regex.Replace(model.InvoiceNumber, "[^a - zA - Z0 - 9_] + ", "")}_{DateTime.Now.ToString("ddMMMyyyy")}.pdf");
        }
    }
}