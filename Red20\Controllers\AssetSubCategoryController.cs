﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Red20.Model.Data;
using Red20.Model.Entity;
using Red20.Service.Data.Interface;

namespace Red20.Controllers {
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class AssetSubCategoryController : ControllerBase {

        private IAssetSubCategoryService assetSubCategoryService;
        private ILogger<AuthController> logger;

        public AssetSubCategoryController(
            IAssetSubCategoryService assetSubCategoryService,
            IUnitOfWork unitOfWork,
            ILogger<AuthController> logger) {

            this.assetSubCategoryService = assetSubCategoryService;
            this.logger = logger;
        }

        [HttpGet]
        [ProducesResponseType(200, Type = typeof(IList<AssetSubCategoryModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get() {
            var categories = assetSubCategoryService.GetAllAssetCategories();
            return Ok(categories);
        }

        [HttpGet("hire")]
        [ProducesResponseType(200, Type = typeof(IList<AssetSubCategoryModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetHireCategories() {
            var categories = assetSubCategoryService.GetAllAssetCategories();
            var hireCategories = categories.Where(c => !c.Number.Contains("00")).ToList();
            return Ok(hireCategories);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(AssetSubCategoryModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id) {

            var assetSubCategory = await assetSubCategoryService.GetAsync(id);

            return Ok(assetSubCategory);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(AssetSubCategoryModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody]AssetSubCategoryUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }

            var assetSubCategory = await assetSubCategoryService.PostAsync(model);

            return Ok(assetSubCategory);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(AssetSubCategoryModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Put(Guid id, [FromBody]AssetSubCategoryUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }

            var assetSubCategory = await assetSubCategoryService.GetAsync(id);

            if (assetSubCategory is null) {
                return BadRequest();
            }

            assetSubCategory = await assetSubCategoryService.PutAsync(id, model);

            return Ok(assetSubCategory);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Delete(Guid id) {

            await assetSubCategoryService.DeleteAsync(id);
            return Ok();
        }
    }
}