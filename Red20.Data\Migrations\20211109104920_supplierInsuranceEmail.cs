﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace Red20.Data.Migrations
{
    public partial class supplierInsuranceEmail : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "Is2DayInsuranceEmailSent",
                table: "Suppliers",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "Is7DayInsuranceEmailSent",
                table: "Suppliers",
                nullable: false,
                defaultValue: false);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Is2DayInsuranceEmailSent",
                table: "Suppliers");

            migrationBuilder.DropColumn(
                name: "Is7DayInsuranceEmailSent",
                table: "Suppliers");
        }
    }
}
