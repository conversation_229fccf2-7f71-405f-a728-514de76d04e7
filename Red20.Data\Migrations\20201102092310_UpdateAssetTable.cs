﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Red20.Data.Migrations
{
    public partial class UpdateAssetTable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "BaseValueBasedOn",
                table: "Assets");

            migrationBuilder.DropColumn(
                name: "CostEndofYear",
                table: "Assets");

            migrationBuilder.DropColumn(
                name: "CostStartofYear",
                table: "Assets");

            migrationBuilder.DropColumn(
                name: "CurrentBaseValue",
                table: "Assets");

            migrationBuilder.DropColumn(
                name: "CurrentCostAdditionalCharge",
                table: "Assets");

            migrationBuilder.DropColumn(
                name: "CurrentCostStartofYear",
                table: "Assets");

            migrationBuilder.DropColumn(
                name: "CurrentDepreciationAdditionalCharge",
                table: "Assets");

            migrationBuilder.DropColumn(
                name: "CurrentDepreciationStartofYear",
                table: "Assets");

            migrationBuilder.DropColumn(
                name: "DepreciationCharge",
                table: "Assets");

            migrationBuilder.DropColumn(
                name: "DepreciationEndofYear",
                table: "Assets");

            migrationBuilder.DropColumn(
                name: "DepreciationMethod",
                table: "Assets");

            migrationBuilder.DropColumn(
                name: "DepreciationStartofYear",
                table: "Assets");

            migrationBuilder.DropColumn(
                name: "HistoricalCostAdditionalCharge",
                table: "Assets");

            migrationBuilder.DropColumn(
                name: "HistoricalCostStartofYear",
                table: "Assets");

            migrationBuilder.DropColumn(
                name: "HistoricalDepreciationAdditionalCharge",
                table: "Assets");

            migrationBuilder.DropColumn(
                name: "HistoricalDepreciationStartofYear",
                table: "Assets");

            migrationBuilder.DropColumn(
                name: "InServicePeriod",
                table: "Assets");

            migrationBuilder.DropColumn(
                name: "InServiceYear",
                table: "Assets");

            migrationBuilder.DropColumn(
                name: "NextPosting",
                table: "Assets");

            migrationBuilder.DropColumn(
                name: "NoofPostings",
                table: "Assets");

            migrationBuilder.DropColumn(
                name: "PostingFrequency",
                table: "Assets");

            migrationBuilder.DropColumn(
                name: "PostingMonths",
                table: "Assets");

            migrationBuilder.DropColumn(
                name: "PostingYears",
                table: "Assets");

            migrationBuilder.DropColumn(
                name: "SubCategory",
                table: "Assets");

            migrationBuilder.DropColumn(
                name: "SupplierNumber",
                table: "Assets");

            migrationBuilder.DropColumn(
                name: "Year",
                table: "Assets");

            migrationBuilder.DropColumn(
                name: "YearlyPercentage",
                table: "Assets");

            migrationBuilder.AddColumn<DateTime>(
                name: "DisposedDate",
                table: "Assets",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "DisposedDate",
                table: "Assets");

            migrationBuilder.AddColumn<string>(
                name: "BaseValueBasedOn",
                table: "Assets",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "CostEndofYear",
                table: "Assets",
                type: "float",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "CostStartofYear",
                table: "Assets",
                type: "float",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "CurrentBaseValue",
                table: "Assets",
                type: "float",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "CurrentCostAdditionalCharge",
                table: "Assets",
                type: "float",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "CurrentCostStartofYear",
                table: "Assets",
                type: "float",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "CurrentDepreciationAdditionalCharge",
                table: "Assets",
                type: "float",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "CurrentDepreciationStartofYear",
                table: "Assets",
                type: "float",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "DepreciationCharge",
                table: "Assets",
                type: "float",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "DepreciationEndofYear",
                table: "Assets",
                type: "float",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DepreciationMethod",
                table: "Assets",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "DepreciationStartofYear",
                table: "Assets",
                type: "float",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "HistoricalCostAdditionalCharge",
                table: "Assets",
                type: "float",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "HistoricalCostStartofYear",
                table: "Assets",
                type: "float",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "HistoricalDepreciationAdditionalCharge",
                table: "Assets",
                type: "float",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "HistoricalDepreciationStartofYear",
                table: "Assets",
                type: "float",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "InServicePeriod",
                table: "Assets",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "InServiceYear",
                table: "Assets",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "NextPosting",
                table: "Assets",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "NoofPostings",
                table: "Assets",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PostingFrequency",
                table: "Assets",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "PostingMonths",
                table: "Assets",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "PostingYears",
                table: "Assets",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SubCategory",
                table: "Assets",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SupplierNumber",
                table: "Assets",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "Year",
                table: "Assets",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<double>(
                name: "YearlyPercentage",
                table: "Assets",
                type: "float",
                nullable: true);
        }
    }
}
