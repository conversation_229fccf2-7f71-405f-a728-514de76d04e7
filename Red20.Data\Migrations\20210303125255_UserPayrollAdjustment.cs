﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Red20.Data.Migrations
{
    public partial class UserPayrollAdjustment : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "UserPayrollAdjustments",
                columns: table => new
                {
                    UserPayrollAdjustmentId = table.Column<Guid>(nullable: false),
                    Created = table.Column<DateTime>(nullable: false),
                    CreatedBy = table.Column<string>(nullable: true),
                    CreatedMonth = table.Column<string>(nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserPayrollAdjustments", x => x.UserPayrollAdjustmentId);
                });

            migrationBuilder.CreateTable(
                name: "UserPayrollAdjustmentItems",
                columns: table => new
                {
                    UserPayrollAdjustmentItemId = table.Column<Guid>(nullable: false),
                    UserPayrollAdjustmentId = table.Column<Guid>(nullable: false),
                    UserId = table.Column<Guid>(nullable: false),
                    Paytype = table.Column<string>(nullable: true),
                    Amount = table.Column<double>(nullable: false),
                    Created = table.Column<DateTime>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserPayrollAdjustmentItems", x => x.UserPayrollAdjustmentItemId);
                    table.ForeignKey(
                        name: "FK_UserPayrollAdjustmentItems_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "UserId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_UserPayrollAdjustmentItems_UserPayrollAdjustments_UserPayrollAdjustmentId",
                        column: x => x.UserPayrollAdjustmentId,
                        principalTable: "UserPayrollAdjustments",
                        principalColumn: "UserPayrollAdjustmentId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_UserPayrollAdjustmentItems_UserId",
                table: "UserPayrollAdjustmentItems",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_UserPayrollAdjustmentItems_UserPayrollAdjustmentId",
                table: "UserPayrollAdjustmentItems",
                column: "UserPayrollAdjustmentId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "UserPayrollAdjustmentItems");

            migrationBuilder.DropTable(
                name: "UserPayrollAdjustments");
        }
    }
}
