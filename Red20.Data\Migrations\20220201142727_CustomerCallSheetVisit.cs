﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Red20.Data.Migrations
{
    public partial class CustomerCallSheetVisit : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "CustomerCallSheetVisits",
                columns: table => new
                {
                    CustomerCallSheetVisitId = table.Column<Guid>(nullable: false),
                    CustomerCallSheeetId = table.Column<Guid>(nullable: false),
                    CustomerCallSheetId = table.Column<Guid>(nullable: true),
                    VisitDate = table.Column<DateTime>(nullable: true),
                    Notes = table.Column<string>(nullable: true),
                    Created = table.Column<DateTime>(nullable: false),
                    Updated = table.Column<DateTime>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerCallSheetVisits", x => x.CustomerCallSheetVisitId);
                    table.ForeignKey(
                        name: "FK_CustomerCallSheetVisits_CustomerCallSheets_CustomerCallSheetId",
                        column: x => x.CustomerCallSheetId,
                        principalTable: "CustomerCallSheets",
                        principalColumn: "CustomerCallSheetId",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_CustomerCallSheetVisits_CustomerCallSheetId",
                table: "CustomerCallSheetVisits",
                column: "CustomerCallSheetId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CustomerCallSheetVisits");
        }
    }
}
