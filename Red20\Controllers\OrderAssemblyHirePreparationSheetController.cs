﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Red20.Model.Data.Order;
using Red20.Service.Data.Interface;

namespace Red20.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class OrderAssemblyHirePreparationSheetController : ControllerBase
    {
        private IOrderAssemblyHirePreparationSheetService service;
        private ILogger<AuthController> logger;

        public OrderAssemblyHirePreparationSheetController(
            IOrderAssemblyHirePreparationSheetService service,
            IOrderAssemblyService assemblyService,
            ILogger<AuthController> logger) {
            this.service = service;
            this.logger = logger;
        }

        [HttpGet("byAssembly/{id}")]
        [ProducesResponseType(200, Type = typeof(OrderAssemblyHirePreparationSheetModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id) {
            var hirePreparationSheet = await service.GetByOrderAssemblyAsync(id);
            return Ok(hirePreparationSheet);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(OrderAssemblyHirePreparationSheetModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody]OrderAssemblyHirePreparationSheetUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }

            try {
                var hirePreparationSheet = await service.PostAsync(model);
                return Ok(hirePreparationSheet);
            } catch (Exception ex) {
                logger.LogError($"Cannot create Hire Preparation Sheet - {ex}");
                return BadRequest();
            }
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(OrderAssemblyHirePreparationSheetModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Put(Guid id, [FromBody]OrderAssemblyHirePreparationSheetUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }

            var hirePreparationSheet = await service.GetAsync(id);
            if (hirePreparationSheet is null) {
                return BadRequest();
            }

            try {
                hirePreparationSheet = await service.PutAsync(id, model);
                return Ok(hirePreparationSheet);
            } catch (Exception ex) {
                logger.LogError($"Cannot update Hire Preparation Sheet - {ex}");
                return BadRequest();
            }
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Delete(Guid id) {
            if(!await service.OrderAssemblyHirePreparationSheetIdExistsAsync(id)) {
                return BadRequest();
            }
            await service.DeleteAsync(id);
            return Ok();
        }
    }
}