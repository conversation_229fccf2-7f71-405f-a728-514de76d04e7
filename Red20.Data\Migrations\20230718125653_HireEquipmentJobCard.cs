﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Red20.Data.Migrations
{
    public partial class HireEquipmentJobCard : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "HireEquipmentJobCards",
                columns: table => new
                {
                    HireEquipmentJobCardId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    HireEquipmentId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Type = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    JobNumber = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Location = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Date = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreatedOn = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_HireEquipmentJobCards", x => x.HireEquipmentJobCardId);
                    table.ForeignKey(
                        name: "FK_HireEquipmentJobCards_HireEquipments_HireEquipmentId",
                        column: x => x.HireEquipmentId,
                        principalTable: "HireEquipments",
                        principalColumn: "HireEquipmentId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_HireEquipmentJobCards_HireEquipmentId",
                table: "HireEquipmentJobCards",
                column: "HireEquipmentId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "HireEquipmentJobCards");
        }
    }
}
