﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace Red20.Data.Migrations
{
    public partial class CustomerComplaintHasResponseTime : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "HasReponseTime",
                table: "CustomerComplaints");

            migrationBuilder.AddColumn<bool>(
                name: "HasResponseTime",
                table: "CustomerComplaints",
                nullable: false,
                defaultValue: false);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "HasResponseTime",
                table: "CustomerComplaints");

            migrationBuilder.AddColumn<bool>(
                name: "HasReponseTime",
                table: "CustomerComplaints",
                type: "bit",
                nullable: false,
                defaultValue: false);
        }
    }
}
