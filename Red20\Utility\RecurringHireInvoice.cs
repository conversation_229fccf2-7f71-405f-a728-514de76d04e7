using AutoMapper;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Red20.Data.Context;
using Red20.Model.Data.Job;
using Red20.Model.Data.Order;
using Red20.Model.Entity;
using Red20.Service.Data.Interface;
using Red20.Service.Email.Interface;
using Red20.Service.Xero.Interface;
using Red20.Settings;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Text;
using System.Threading.Tasks;
using System;
using Xero.NetStandard.OAuth2.Model.Accounting;
using Newtonsoft.Json;
using Red20.Model.Data.BankHoliday;
using System.Net;
using Microsoft.EntityFrameworkCore;
using Hangfire;

namespace Red20.Utility
{
    public class RecurringHireInvoice
    {
        private DataContext context;
        private ILogger<TaskUtility> logger;
        private IEmailService emailService;
        private IOrderService orderService;
        private IJobService jobService;
        private IJobInvoiceService jobInvoiceService;
        private IJobInvoiceItemService jobInvoiceItemService;
        private IXeroService xeroService;
        private readonly IConfiguration config;
        private IUnitOfWork unitOfWork;
        private IOrderAssemblyService assemblyService;
        private IMapper mapper;

        public RecurringHireInvoice(
            DataContext context,
            ILogger<TaskUtility> logger,
            IEmailService emailService,
            IOrderService orderService,
            IJobService jobService,
            IJobInvoiceService jobInvoiceService,
            IJobInvoiceItemService jobInvoiceItemService,
            IXeroService xeroService,
            IConfiguration config,
            IUnitOfWork unitOfWork,
            IOrderAssemblyService assemblyService,
            IMapper mapper)
        {
            this.context = context;
            this.logger = logger;
            this.emailService = emailService;
            this.orderService = orderService;
            this.jobService = jobService;
            this.jobInvoiceService = jobInvoiceService;
            this.jobInvoiceItemService = jobInvoiceItemService;
            this.xeroService = xeroService;
            this.config = config;
            this.unitOfWork = unitOfWork;
            this.assemblyService = assemblyService;
            this.mapper = mapper;
        }

        [DisableConcurrentExecution(timeoutInSeconds: 60 * 60)] // Prevent concurrent execution
        [AutomaticRetry(Attempts = 1)]
        public async Task OnHireAssembliesRecurringInvoice()
        {
            // Check if today is the day to run invoices
            if (!ShouldRunInvoiceToday())
                return;

            var orderIdsToMarkAsDeliveryCharged = new List<Guid>();
            var holidays = GetUkGovBankHolidays();
            var holidayDates = GetEventsDates(holidays);
            var lastDayOfMonth = new DateTime(DateTime.UtcNow.Year, DateTime.UtcNow.Month, DateTime.DaysInMonth(DateTime.UtcNow.Year, DateTime.UtcNow.Month));
            var firstDayOfMonth = new DateTime(DateTime.UtcNow.Year, DateTime.UtcNow.Month, 1).Date;
            var hireEndString = lastDayOfMonth.ToString("dd/MM/yyyy");

            int currentInvoiceMonth = DateTime.UtcNow.Month;
            int currentInvoiceYear = DateTime.UtcNow.Year;

            // Get all hire orders with active hire assemblies
            var orders = await orderService.GetAllHireAsync();
            if (orders == null || !orders.Any())
                return;

            orders = orders.OrderBy(o => o.Created).ToList();

            foreach (var order in orders)
            {
                using var transaction = await context.Database.BeginTransactionAsync();
                try
                {
                    // RE-ENABLE THE DUPLICATE CHECK TO PREVENT MULTIPLE INVOICES
                    if (await HasInvoiceForCurrentMonth(order, currentInvoiceYear, currentInvoiceMonth))
                    {
                        logger.LogDebug($"Skipping order {order.Number} - already has invoice for {currentInvoiceMonth}/{currentInvoiceYear}");
                        await transaction.CommitAsync();
                        continue;
                    }

                    // Debug logging to see what orders are being processed
                    logger.LogInformation($"Processing order {order.Number} for {currentInvoiceMonth}/{currentInvoiceYear}");

                    // Get order assemblies that are on hire (have start date but no end date)
                    var orderAssemblies = order.OrderAssemblies
                        .Where(w => !w.IsSimpleAssembly &&
                                   w.HireStart.HasValue &&
                                   !w.HireEnd.HasValue &&
                                   ((w.HireStart.Value.Year == currentInvoiceYear && w.HireStart.Value.Month <= currentInvoiceMonth) ||
                                    w.HireStart.Value.Year < currentInvoiceYear))
                        .OrderBy(o => o.Created)
                        .ToList();

                    if (orderAssemblies == null || !orderAssemblies.Any())
                    {
                        await transaction.CommitAsync();
                        continue;
                    }

                    // Get or create job for this order
                    var job = await GetOrCreateJobForOrder(order);

                    // Create job invoice
                    var jobInvoice = await CreateJobInvoice(job, order, lastDayOfMonth);

                    var jobInvoiceItemModels = new List<JobInvoiceItemModel>();

                    // Handle delivery charge if needed
                    var hasInitialDeliveryCharge = await ProcessInitialDeliveryCharge(
                        order, job, jobInvoice, lastDayOfMonth, jobInvoiceItemModels);

                    if (hasInitialDeliveryCharge)
                        orderIdsToMarkAsDeliveryCharged.Add(order.OrderId);

                    // Track which assemblies need to be updated with invoice date
                    var orderAssemblyIds = new List<Guid>();

                    // Process each assembly
                    foreach (var assembly in orderAssemblies)
                    {
                        await ProcessOrderAssembly(
                            assembly, order, job, jobInvoice, firstDayOfMonth, lastDayOfMonth,
                            hireEndString, jobInvoiceItemModels, orderAssemblyIds);
                    }

                    // Update all assemblies with invoice date
                    await UpdateAssemblyInvoiceDates(orderAssemblyIds, lastDayOfMonth);

                    // No invoice items created - skip to next order
                    if (!jobInvoiceItemModels.Any())
                    {
                        await transaction.CommitAsync();
                        continue;
                    }

                    // Create Xero invoice (keeping existing logic)
                    await CreateXeroInvoice(
                        order, jobInvoice, jobInvoiceItemModels, hireEndString);

                    // Only mark delivery charge as invoiced AFTER successful Xero creation
                    if (hasInitialDeliveryCharge)
                    {
                        await orderService.MarkDeliveryCharge(order.OrderId);
                        // Remove from the list since we're doing it immediately
                        orderIdsToMarkAsDeliveryCharged.Remove(order.OrderId);
                    }

                    // If we get here, everything succeeded
                    await transaction.CommitAsync();
                    logger.LogInformation($"Successfully processed and committed order {order.Number}");

                } catch (Exception ex)
                {
                    await transaction.RollbackAsync();
                    logger.LogError($"Error processing hire order {order.Number}: {ex.Message} - Transaction rolled back");
                    continue; // Continue with next order
                }
            }

            // Update delivery charge status for relevant orders
            foreach (var id in orderIdsToMarkAsDeliveryCharged)
            {
                try
                {
                    await orderService.MarkDeliveryCharge(id);
                } catch (Exception ex)
                {
                    logger.LogError($"Failed to mark delivery charge for order {id}: {ex.Message}");
                }
            }
        }

        // Check for any invoice this month (not just ones with Xero IDs)
        private async Task<bool> HasInvoiceForCurrentMonth(OrderModel order, int currentYear, int currentMonth)
        {
            try
            {
                var job = await jobService.GetByOrderIdAsync(order.OrderId);
                if (job?.JobInvoices != null && job.JobInvoices.Any())
                {
                    var existingInvoices = job.JobInvoices.Where(ji =>
                        ji.InvoiceDate.Year == currentYear &&
                        ji.InvoiceDate.Month == currentMonth &&
                        (ji.JobType == "Hire" || ji.JobType == order.Type)).ToList();

                    // Debug logging
                    if (existingInvoices.Any())
                    {
                        logger.LogInformation($"Order {order.Number} already has {existingInvoices.Count} invoice(s) for {currentMonth}/{currentYear}:");
                        foreach (var inv in existingInvoices)
                        {
                            var xeroIdDisplay = inv.XeroInvoiceId.HasValue ? inv.XeroInvoiceId.Value.ToString() : "None";
                            logger.LogInformation($"  - Invoice: {inv.InvoiceNumber}, Date: {inv.InvoiceDate}, XeroID: {xeroIdDisplay}");
                        }
                        return true;
                    } else
                    {
                        logger.LogDebug($"Order {order.Number} has {job.JobInvoices.Count} total invoices but none for {currentMonth}/{currentYear}");
                    }
                } else
                {
                    logger.LogDebug($"Order {order.Number} has no job or no invoices");
                }
                return false;
            } catch (Exception ex)
            {
                logger.LogWarning($"Error checking existing invoices for order {order.Number}: {ex.Message}");
                return false; // If in doubt, process it
            }
        }

        private bool ShouldRunInvoiceToday()
        {
            var now = DateTime.UtcNow;

            // Special case for December - office closes on 23rd
            if (now.Month == 12 && now.Day == 23)
                return true;

            // Otherwise, run on last business day of the month
            var holidays = GetUkGovBankHolidays();
            var holidayDates = GetEventsDates(holidays);
            var lastBusinessDay = GetLastBusinessDayOfMonth(holidayDates, now.Year, now.Month);

            return now.Year == lastBusinessDay.Year &&
                   now.Month == lastBusinessDay.Month &&
                   now.Day == lastBusinessDay.Day;
        }

        private async Task<JobModel> GetOrCreateJobForOrder(OrderModel order)
        {
            var job = await jobService.GetByOrderIdAsync(order.OrderId);

            if (job == null)
            {
                job = await jobService.PostAsync(new JobModel
                {
                    CreatedBy = "System Recurring",
                    OrderId = order.OrderId,
                    CostCentre = order.Number.Contains("SH") ? "Oldmeldrum" : "West Bromwich",
                    OrderType = order.Type,
                    OrderNumber = order.Number,
                    OrderCustomerName = order.Customer != null ? order.Customer.Name : string.Empty,
                    Description = order.FirstItemDescription,
                    DateOrderRaised = order.Created,
                    Currency = order.Customer.Name == "Rami Yokota B.V" ? "Euro" : order.Currency
                });
            }

            return job;
        }

        private async Task<JobInvoiceModel> CreateJobInvoice(JobModel job, OrderModel order, DateTime lastDayOfMonth)
        {
            int number = 1;

            if (job != null && job.JobInvoices != null && job.JobInvoices.Count > 0)
            {
                number = job.JobInvoices.Any(a => a.JobType == "Sale" || a.JobType == "Hire")
                    ? job.JobInvoices.Where(a => a.JobType == "Sale" || a.JobType == "Hire").Count() + 1
                    : number;
            }

            var jobInvoice = new JobInvoiceModel
            {
                JobId = job.JobId,
                CreatedBy = "Automated Recurring Invoice",
                JobType = order.Type,
                InvoiceNumber = $"{order.Number}/{number}",
                InvoiceDate = lastDayOfMonth.Date,
                CustomerPO = order != null ? order.CustomerRef : string.Empty
            };

            // Ensure unique invoice number (handles old invoice numbers)
            while (await jobInvoiceService.JobInvoiceNumberExistsAsync(jobInvoice.InvoiceNumber))
            {
                number++;
                jobInvoice.InvoiceNumber = $"{order.Number}/{number}";
            }

            return await jobInvoiceService.PostAsync(jobInvoice);
        }

        private async Task<bool> ProcessInitialDeliveryCharge(
            OrderModel order, JobModel job, JobInvoiceModel jobInvoice,
            DateTime lastDayOfMonth, List<JobInvoiceItemModel> jobInvoiceItemModels)
        {

            if (!order.DeliveryCharge.HasValue || order.IsDeliveryChargeInvoiced)
                return false;

            bool orderHasInitialDeliveryCharge = await unitOfWork.OrderAssembly
                .Query(q => q.OrderId == order.OrderId && q.IsInitialDeliveryCharge)
                .AnyAsync();

            if (orderHasInitialDeliveryCharge)
                return false;

            // Create delivery charge assembly
            var orderAssembly = await unitOfWork.OrderAssembly.CreateAsync(new OrderAssembly
            {
                OrderId = order.OrderId,
                AccountCode = "1013",
                Created = DateTime.UtcNow,
                Description = "Delivery Charge",
                IsSimpleAssembly = true,
                IsInitialDeliveryCharge = true,
                DayRate = order.DeliveryCharge.Value,
                ItemDescription = "Delivery Charge",
                InvoiceDate = lastDayOfMonth
            });

            await unitOfWork.SaveChangesAsync();

            // Create invoice item for delivery charge
            var invoiceItem = await jobInvoiceItemService.PostAsync(new JobInvoiceItemModel
            {
                JobId = job.JobId,
                JobInvoiceId = jobInvoice.JobInvoiceId,
                InvoiceNumber = jobInvoice.InvoiceNumber,
                InvoiceType = "INV",
                Value = order.DeliveryCharge.Value,
                Quantity = 1,
                UnitPrice = order.DeliveryCharge.Value,
                Description = "Delivery Charge",
                Currency = order.Customer.Name == "Rami Yokota B.V" ? "Euro" : order.Currency,
                AccountCode = "1013",
                OrderItemId = orderAssembly.OrderAssemblyId,
                QuantityToDeliver = 0.0,
                ToFollow = 0.0,
                Vat = order.TaxCode,
                InvoiceDate = lastDayOfMonth.Date,
                HireStartDateString = "",
                HireEndDateString = "",
                DiscountRate = 0.0,
                Created = DateTime.UtcNow,
                IsInitialDeliveryCharge = true,
                IsDeliveryCharge = true,
                IsDayRate = false
            });

            jobInvoiceItemModels.Add(invoiceItem);
            return true;
        }

        private async Task ProcessOrderAssembly(
            OrderAssemblyModel assembly, OrderModel order, JobModel job,
            JobInvoiceModel jobInvoice, DateTime firstDayOfMonth, DateTime lastDayOfMonth,
            string hireEndString, List<JobInvoiceItemModel> jobInvoiceItemModels, List<Guid> orderAssemblyIds)
        {

            // Check for day rate periods first
            var dayRates = await unitOfWork.OrderAssemblyDayRate
                .Query(q =>
                    q.OrderAssemblyId == assembly.OrderAssemblyId &&
                    q.Active &&
                    ((q.From.Year == DateTime.UtcNow.Year && q.From.Month == DateTime.UtcNow.Month) ||
                     (q.To.Year == DateTime.UtcNow.Year && q.To.Month == DateTime.UtcNow.Month)))
                .OrderBy(o => o.From)
                .ToListAsync();

            if (dayRates != null && dayRates.Any())
            {
                await ProcessAssemblyWithDayRates(
                    assembly, order, job, jobInvoice, dayRates,
                    firstDayOfMonth, lastDayOfMonth, hireEndString, jobInvoiceItemModels);
            } else
            {
                await ProcessAssemblyWithoutDayRates(
                    assembly, order, job, jobInvoice, firstDayOfMonth,
                    lastDayOfMonth, hireEndString, jobInvoiceItemModels);
            }

            // Track this assembly for invoice date update
            orderAssemblyIds.Add(assembly.OrderAssemblyId);
        }

        private async Task ProcessAssemblyWithDayRates(
            OrderAssemblyModel assembly, OrderModel order, JobModel job,
            JobInvoiceModel jobInvoice, List<OrderAssemblyDayRate> dayRates,
            DateTime firstDayOfMonth, DateTime lastDayOfMonth, string hireEndString,
            List<JobInvoiceItemModel> jobInvoiceItemModels)
        {

            double totalQuantity = assembly.InvoiceDate.HasValue ?
                (lastDayOfMonth - assembly.InvoiceDate.Value.Date).TotalDays :
                (lastDayOfMonth - assembly.HireStart.Value.Date).TotalDays + 1;

            double remainingQuantity = totalQuantity;

            // Process each day rate period
            foreach (var dayRate in dayRates)
            {
                var rate = dayRate.DayRate;
                var fromDate = dayRate.From < firstDayOfMonth ? firstDayOfMonth : dayRate.From;
                var toDate = dayRate.To > lastDayOfMonth ? lastDayOfMonth.Date : dayRate.To.Date;
                var dayRateQuantity = (toDate - fromDate).TotalDays + 1;

                // Calculate discount
                var discount = dayRate.Discount.HasValue
                    ? (dayRate.Discount.Value / 100) * dayRateQuantity * rate
                    : assembly.Discount.HasValue
                        ? (assembly.Discount.Value / 100) * dayRateQuantity * rate
                        : (double?)null;

                var dayRateDiscountValue = discount.HasValue ? discount.Value : 0.0;
                var dayRateInvoiceValue = dayRateQuantity * rate - dayRateDiscountValue;

                var jobInvoiceItemModel = new JobInvoiceItemModel
                {
                    JobId = job.JobId,
                    JobInvoiceId = jobInvoice.JobInvoiceId,
                    InvoiceNumber = jobInvoice.InvoiceNumber,
                    InvoiceType = "INV",
                    Value = dayRateInvoiceValue,
                    Quantity = dayRateQuantity,
                    UnitPrice = rate,
                    Description = assembly.OrderAssemblyFirstTwoLinesDescription,
                    Currency = order.Customer.Name == "Rami Yokota B.V" ? "Euro" : order.Currency,
                    AccountCode = assembly.IsSimpleAssembly ? "1013" : order.OrderAccountCode,
                    OrderItemId = assembly.OrderAssemblyId,
                    QuantityToDeliver = 0.0,
                    ToFollow = 0.0,
                    Vat = order.TaxCode,
                    InvoiceDate = lastDayOfMonth.Date,
                    HireStartDateString = fromDate.ToString("dd/MM/yyyy"),
                    HireEndDateString = toDate.ToString("dd/MM/yyyy"),
                    DiscountRate = dayRate.Discount.HasValue
                        ? dayRate.Discount.Value
                        : assembly.Discount.HasValue
                            ? assembly.Discount.Value
                            : 0.0,
                    IsInitialDeliveryCharge = false,
                    IsDeliveryCharge = false,
                    IsDayRate = true,
                    From = fromDate,
                    To = toDate
                };

                await jobInvoiceItemService.PostAsync(jobInvoiceItemModel);
                jobInvoiceItemModels.Add(jobInvoiceItemModel);

                remainingQuantity -= dayRateQuantity;
            }

            // Handle any remaining days not covered by day rate periods
            if (remainingQuantity > 0)
            {
                var description = BuildRemainingDaysDescription(
                    assembly, dayRates, firstDayOfMonth, lastDayOfMonth);

                await CreateInvoiceItemForRemainingDays(
                    assembly, order, job, jobInvoice, remainingQuantity,
                    description, lastDayOfMonth, hireEndString, jobInvoiceItemModels);
            }
        }

        private async Task ProcessAssemblyWithoutDayRates(
            OrderAssemblyModel assembly, OrderModel order, JobModel job,
            JobInvoiceModel jobInvoice, DateTime firstDayOfMonth, DateTime lastDayOfMonth,
            string hireEndString, List<JobInvoiceItemModel> jobInvoiceItemModels)
        {
            // Determine if invoice date exists and falls within the current month
            bool invoiceDateInCurrentMonth = assembly.InvoiceDate.HasValue &&
                assembly.InvoiceDate.Value.Year == firstDayOfMonth.Year &&
                assembly.InvoiceDate.Value.Month == firstDayOfMonth.Month;

            // Determine if hire start date exists and falls within the current month
            bool hireStartInCurrentMonth = assembly.HireStart.HasValue &&
                assembly.HireStart.Value.Year == firstDayOfMonth.Year &&
                assembly.HireStart.Value.Month == firstDayOfMonth.Month;

            // Calculate quantity and hire start date string
            double quantity = 0.0;
            string hireStartDateString = string.Empty;

            if (assembly.InvoiceDate.HasValue)
            {
                if (invoiceDateInCurrentMonth)
                {
                    if (assembly.HireStart.HasValue && hireStartInCurrentMonth)
                    {
                        // Both invoice date and hire start are in current month
                        quantity = (lastDayOfMonth - assembly.HireStart.Value.Date).TotalDays + 1;
                        hireStartDateString = assembly.HireStart.Value.ToString("dd/MM/yyyy");
                    } else
                    {
                        // Invoice date in current month, hire start not in current month
                        quantity = (lastDayOfMonth - firstDayOfMonth).TotalDays + 1;
                        hireStartDateString = firstDayOfMonth.ToString("dd/MM/yyyy");
                    }
                } else
                {
                    if (assembly.HireStart.HasValue && hireStartInCurrentMonth)
                    {
                        // Invoice date not in current month, hire start in current month
                        quantity = (lastDayOfMonth - assembly.HireStart.Value.Date).TotalDays + 1;
                        hireStartDateString = assembly.HireStart.Value.ToString("dd/MM/yyyy");
                    } else
                    {
                        // Invoice date not in current month, hire start not in current month
                        quantity = (lastDayOfMonth - firstDayOfMonth).TotalDays + 1;
                        hireStartDateString = firstDayOfMonth.ToString("dd/MM/yyyy");
                    }
                }
            } else if (assembly.HireStart.HasValue)
            {
                // No invoice date, but hire start exists
                quantity = (lastDayOfMonth - assembly.HireStart.Value.Date).TotalDays + 1;
                hireStartDateString = assembly.HireStart.Value.ToString("dd/MM/yyyy");
            }
            // The fallback (no dates available) is already handled with default initialization

            var discountValue = assembly.Discount.HasValue && quantity > 0
                ? assembly.Discount.Value / 100 * quantity * assembly.DayRate
                : 0.0;

            var invoiceValue = (quantity * assembly.DayRate) - discountValue;

            var jobInvoiceItemModel = new JobInvoiceItemModel
            {
                JobId = job.JobId,
                JobInvoiceId = jobInvoice.JobInvoiceId,
                InvoiceNumber = jobInvoice.InvoiceNumber,
                InvoiceType = "INV",
                Value = invoiceValue,
                Quantity = quantity,
                UnitPrice = assembly.DayRate,
                Description = assembly.OrderAssemblyFirstTwoLinesDescription,
                Currency = order.Customer.Name == "Rami Yokota B.V" ? "Euro" : order.Currency,
                AccountCode = assembly.IsSimpleAssembly ? "1013" : order.OrderAccountCode,
                OrderItemId = assembly.OrderAssemblyId,
                QuantityToDeliver = 0.0,
                ToFollow = 0.0,
                Vat = order.TaxCode,
                InvoiceDate = lastDayOfMonth.Date,
                HireStartDateString = hireStartDateString,
                HireEndDateString = hireEndString,
                DiscountRate = assembly.Discount.HasValue ? assembly.Discount.Value : 0.0,
                IsInitialDeliveryCharge = false,
                IsDeliveryCharge = false,
                IsDayRate = false
            };

            await jobInvoiceItemService.PostAsync(jobInvoiceItemModel);
            jobInvoiceItemModels.Add(jobInvoiceItemModel);
        }

        private string BuildRemainingDaysDescription(
            OrderAssemblyModel assembly, List<OrderAssemblyDayRate> dayRates,
            DateTime firstDayOfMonth, DateTime lastDayOfMonth)
        {

            var description = new StringBuilder();

            if (dayRates.Count == 1)
            {
                // Single day rate case
                if (dayRates[0].From.Date > assembly.HireStart.Value.Date)
                {
                    var dateToUse = assembly.HireStart.Value.Date < firstDayOfMonth
                        ? firstDayOfMonth
                        : assembly.HireStart.Value.Date;
                    description.AppendLine($"Hire Period {dateToUse:dd/MM/yyyy} to {dayRates[0].From.AddDays(-1):dd/MM/yyyy}");
                } else if (dayRates[0].From.Date == assembly.HireStart.Value.Date && dayRates[0].To.Date < lastDayOfMonth.Date)
                {
                    description.AppendLine($"Hire Period {dayRates[0].To.Date.AddDays(1):dd/MM/yyyy} to {lastDayOfMonth.Date:dd/MM/yyyy}");
                }
            } else
            {
                // Multiple day rates case
                for (int index = 0; index < dayRates.Count; index++)
                {
                    if (index == 0)
                    {
                        // First day rate
                        if (dayRates[0].From.Date > assembly.HireStart.Value.Date)
                        {
                            var dateToUse = assembly.HireStart.Value.Date < firstDayOfMonth
                                ? firstDayOfMonth
                                : assembly.HireStart.Value.Date;
                            description.AppendLine($"Hire Period {dateToUse:dd/MM/yyyy} to {dayRates[0].From.AddDays(-1):dd/MM/yyyy}");
                        } else if (dayRates[0].From.Date == assembly.HireStart.Value.Date && index < dayRates.Count - 1)
                        {
                            bool hasDaysSinceLastDayRatePeriod = (dayRates[index].To.Date - dayRates[index + 1].From.Date).TotalDays > 1;
                            if (hasDaysSinceLastDayRatePeriod)
                            {
                                description.AppendLine($"Hire Period {dayRates[index].To.AddDays(1):dd/MM/yyyy} to {dayRates[index + 1].From.AddDays(-1):dd/MM/yyyy}");
                            }
                        }
                    } else
                    {
                        // Middle or last day rate
                        if (index < dayRates.Count - 1)
                        {
                            bool hasDaysSinceLastDayRatePeriod = (dayRates[index].From - dayRates[index - 1].To).TotalDays > 1;
                            if (hasDaysSinceLastDayRatePeriod)
                            {
                                description.AppendLine($"Hire Period {dayRates[index - 1].To.AddDays(1):dd/MM/yyyy} to {dayRates[index].From.AddDays(-1):dd/MM/yyyy}");
                            }
                        } else
                        {
                            // Last day rate
                            bool hasDaysSinceLastDayRatePeriod = (dayRates[index].From - dayRates[index - 1].To).TotalDays > 1;
                            if (hasDaysSinceLastDayRatePeriod)
                            {
                                description.AppendLine($"Hire Period {dayRates[index - 1].To.AddDays(1):dd/MM/yyyy} to {dayRates[index].From.AddDays(-1):dd/MM/yyyy}");
                            }

                            if (dayRates[index].To.Date < lastDayOfMonth.Date)
                            {
                                description.AppendLine($"Hire Period {dayRates[index].To.AddDays(1):dd/MM/yyyy} to {lastDayOfMonth:dd/MM/yyyy}");
                            }
                        }
                    }
                }
            }

            return description.ToString();
        }

        private async Task CreateInvoiceItemForRemainingDays(
            OrderAssemblyModel assembly, OrderModel order, JobModel job,
            JobInvoiceModel jobInvoice, double remainingQuantity, string description,
            DateTime lastDayOfMonth, string hireEndString, List<JobInvoiceItemModel> jobInvoiceItemModels)
        {

            var discountValue = assembly.Discount.HasValue
                ? assembly.Discount.Value / 100 * remainingQuantity * assembly.DayRate
                : 0.0;

            var invoiceValue = (remainingQuantity * assembly.DayRate) - discountValue;

            var hireStartDateString = assembly.InvoiceDate.HasValue
                ? assembly.InvoiceDate.Value.AddDays(1).Date.ToString("dd/MM/yyyy")
                : assembly.HireStart.HasValue
                    ? assembly.HireStart.Value.ToString("dd/MM/yyyy")
                    : string.Empty;

            var jobInvoiceItemModel = new JobInvoiceItemModel
            {
                JobId = job.JobId,
                JobInvoiceId = jobInvoice.JobInvoiceId,
                InvoiceNumber = jobInvoice.InvoiceNumber,
                InvoiceType = "INV",
                Value = invoiceValue,
                Quantity = remainingQuantity,
                UnitPrice = assembly.DayRate,
                Description = description + assembly.OrderAssemblyFirstTwoLinesDescription,
                Currency = order.Customer.Name == "Rami Yokota B.V" ? "Euro" : order.Currency,
                AccountCode = assembly.IsSimpleAssembly ? "1013" : order.OrderAccountCode,
                OrderItemId = assembly.OrderAssemblyId,
                QuantityToDeliver = 0.0,
                ToFollow = 0.0,
                Vat = order.TaxCode,
                InvoiceDate = lastDayOfMonth.Date,
                HireStartDateString = hireStartDateString,
                HireEndDateString = hireEndString,
                DiscountRate = assembly.Discount.HasValue ? assembly.Discount.Value : 0.0,
                IsInitialDeliveryCharge = false,
                IsDeliveryCharge = false,
                IsDayRate = true,
                From = null,
                To = null
            };

            await jobInvoiceItemService.PostAsync(jobInvoiceItemModel);
            jobInvoiceItemModels.Add(jobInvoiceItemModel);
        }

        private async Task UpdateAssemblyInvoiceDates(List<Guid> orderAssemblyIds, DateTime lastDayOfMonth)
        {
            if (!orderAssemblyIds.Any())
                return;

            foreach (var id in orderAssemblyIds)
            {
                var assembly = await unitOfWork.OrderAssembly.GetAsync(id);
                assembly.InvoiceDate = lastDayOfMonth.Date;
                unitOfWork.OrderAssembly.Update(assembly);
            }

            await unitOfWork.SaveChangesAsync();
        }

        // Keep existing Xero invoice creation logic - just remove the "(New)" addition
        private async Task CreateXeroInvoice(
            OrderModel order, JobInvoiceModel jobInvoice,
            List<JobInvoiceItemModel> jobInvoiceItemModels, string hireEndString)
        {
            // Sort invoice items (non-delivery charges first)
            jobInvoiceItemModels = jobInvoiceItemModels.OrderByDescending(o => !o.IsDeliveryCharge).ToList();

            List<LineItem> lineItems = new List<LineItem>();

            foreach (var jobInvoiceItem in jobInvoiceItemModels)
            {
                try
                {
                    // Format description based on item type
                    FormatInvoiceItemDescription(jobInvoiceItem, hireEndString);

                    // Handle Xero API rate limiting
                    await XeroRateLimitManager.WaitForRateLimit(logger);

                    // Get tax rate and account code
                    var taxRate = await xeroService.GetTaxRateAsync(jobInvoiceItem.Vat);

                    await XeroRateLimitManager.WaitForRateLimit(logger);

                    var accountCode = await xeroService.GetAccountAsync(jobInvoiceItem.AccountCode);

                    // Create tracking for location
                    var trackings = new List<LineItemTracking> {
                        new LineItemTracking {
                            Name = "Location",
                            Option = order.Number.Contains("SH") ? "001 - Aberdeen" : "002 - West Brom"
                        }
                    };

                    // Create line item
                    LineItem lineItem = new LineItem
                    {
                        Quantity = Convert.ToDecimal(jobInvoiceItem.Quantity),
                        UnitAmount = Convert.ToDecimal(jobInvoiceItem.UnitPrice),
                        Description = !string.IsNullOrWhiteSpace(jobInvoiceItem.Description)
                            ? Regex.Replace(jobInvoiceItem.Description, "<.*?>", String.Empty)
                            : string.Empty,
                        AccountCode = accountCode.Code,
                        TaxType = taxRate.TaxType,
                        DiscountRate = jobInvoiceItem.DiscountRate.HasValue
                            ? Convert.ToDecimal(jobInvoiceItem.DiscountRate)
                            : (decimal?)null,
                        Tracking = trackings
                    };

                    lineItems.Add(lineItem);
                } catch (Exception ex)
                {
                    await LogXeroApiException(ex, order, jobInvoiceItem);
                    continue; // Continue with next invoice item
                }
            }

            if (!lineItems.Any())
                return;

            try
            {
                // Get or create Xero contact
                await XeroRateLimitManager.WaitForRateLimit(logger);
                var xeroContact = await GetOrCreateXeroContact(order);

                // Determine currency code
                var currencyCode = DetermineCurrencyCode(order);

                // Create Xero invoice
                Invoice invoice = new Invoice
                {
                    Date = jobInvoice.InvoiceDate,
                    InvoiceNumber = jobInvoice.InvoiceNumber,
                    CurrencyCode = currencyCode,
                    Type = Invoice.TypeEnum.ACCREC,
                    Status = Invoice.StatusEnum.DRAFT,
                    LineItems = lineItems,
                    Contact = xeroContact,
                    DueDate = DateTime.Now.AddDays(30),
                    Reference = jobInvoice.CustomerPO
                };

                // Check for existing invoice with same number - this is normal for recurring invoices
                await XeroRateLimitManager.WaitForRateLimit(logger);
                var existingInvoices = await xeroService.GetInvoiceByNumberAsync(invoice.InvoiceNumber);

                if (existingInvoices != null && existingInvoices.Any())
                {
                    logger.LogDebug($"Invoice number {invoice.InvoiceNumber} already exists in Xero - this is expected for recurring hire invoices");
                    // The incrementing number logic in CreateJobInvoice should handle this
                    // Don't add "(New)" suffix - just proceed with the incremented number
                }

                // Create invoice in Xero
                await XeroRateLimitManager.WaitForRateLimit(logger);
                invoice = await xeroService.UpdateOrCreateInvoicesAsync(invoice);

                // Update job invoice with Xero invoice ID
                var jobInvoiceRecord = await unitOfWork.JobInvoice.GetAsync(jobInvoice.JobInvoiceId);
                jobInvoiceRecord.XeroInvoiceId = invoice.InvoiceID;
                unitOfWork.JobInvoice.Update(jobInvoiceRecord);
                await unitOfWork.SaveChangesAsync();

                logger.LogInformation($"Successfully created Xero invoice {invoice.InvoiceNumber} for order {order.Number}");
            } catch (Exception ex)
            {
                logger.LogError($"Error creating Xero Invoice for Hire Order: {order.Number} => {ex}");
                throw; // Re-throw so it gets caught by the order processing try-catch
            }
        }

        private async Task<Contact> GetOrCreateXeroContact(OrderModel order)
        {
            var xeroContact = order.Customer != null && order.Customer.XeroContactId.HasValue
                ? await xeroService.GetContactAsync(order.Customer.XeroContactId.Value)
                : null;

            if (xeroContact != null)
                return xeroContact;

            // Create a new contact
            var addresses = new List<Address>();
            var customerInvoiceAddress = order.Customer != null && order.Customer.CustomerAddresses.Any()
                ? order.Customer.CustomerAddresses.FirstOrDefault(s => s.IsInvoiceDefault)
                : null;

            addresses.Add(new Address
            {
                AddressType = Address.AddressTypeEnum.POBOX,
                AddressLine1 = customerInvoiceAddress?.Street ?? string.Empty,
                AddressLine2 = customerInvoiceAddress?.Street1 ?? string.Empty,
                City = customerInvoiceAddress?.Town ?? string.Empty,
                Country = customerInvoiceAddress?.Country ?? string.Empty,
                PostalCode = customerInvoiceAddress?.PostCode ?? string.Empty
            });

            xeroContact = new Contact
            {
                ContactStatus = Contact.ContactStatusEnum.ACTIVE,
                Name = order.Customer.Name,
                Addresses = addresses,
                DefaultCurrency = order.Customer.Name == "Rami Yokota B.V"
                    ? CurrencyCode.EUR
                    : order.Customer.Currency == "Pounds Sterling"
                        ? CurrencyCode.GBP
                        : order.Customer.Currency == "Euro"
                            ? CurrencyCode.EUR
                            : order.Customer.Currency == "US Dollar"
                                ? CurrencyCode.USD
                                : CurrencyCode.GBP
            };

            try
            {
                await XeroRateLimitManager.WaitForRateLimit(logger);
                xeroContact = await xeroService.UpdateOrCreateContactAsync(xeroContact);
            } catch (Exception ex)
            {
                logger.LogError($"Error creating Xero Contact for Order: {order.Number} and Customer: {(order.Customer?.Name ?? "")} => {ex}");
                throw;
            }

            return xeroContact;
        }

        private void FormatInvoiceItemDescription(JobInvoiceItemModel jobInvoiceItem, string hireEndString)
        {
            if (jobInvoiceItem.IsDayRate && jobInvoiceItem.From.HasValue && jobInvoiceItem.To.HasValue)
            {
                jobInvoiceItem.Description = $"Hire Period {jobInvoiceItem.From.Value:dd/MM/yyyy} to {jobInvoiceItem.To.Value:dd/MM/yyyy}" +
                    Environment.NewLine + jobInvoiceItem.Description;
            } else if (jobInvoiceItem.IsDayRate && !jobInvoiceItem.From.HasValue && !jobInvoiceItem.To.HasValue)
            {
                // Keep description as is
            } else
            {
                jobInvoiceItem.Description = jobInvoiceItem.IsInitialDeliveryCharge || jobInvoiceItem.IsDeliveryCharge
                    ? "Delivery Charge"
                    : $"Hire Period {jobInvoiceItem.HireStartDateString} to {hireEndString} Continues. " +
                      Environment.NewLine + jobInvoiceItem.Description;
            }
        }

        private async Task LogXeroApiException(Exception ex, OrderModel order, JobInvoiceItemModel jobInvoiceItem)
        {
            if (ex != null && !string.IsNullOrEmpty(ex.Message))
            {
                var type = ex.GetType().ToString();
                var message = ex.Message;

                var xeroApiException = new XeroApiException
                {
                    Type = type,
                    Message = message,
                    OrderNumber = order.Number,
                    OrderAssemblyId = jobInvoiceItem.OrderItemId,
                    OrderId = order.OrderId
                };

                await unitOfWork.XeroApiException.CreateAsync(xeroApiException);
                await unitOfWork.SaveChangesAsync();
            }

            logger.LogError($"Xero Api Exception for Hire Order: {order.Number} and Assembly ID: {jobInvoiceItem.OrderItemId} => {ex}");
        }

        private CurrencyCode DetermineCurrencyCode(OrderModel order)
        {
            if (order.Customer.Name.Contains("Rami"))
            {
                return CurrencyCode.EUR;
            }

            return order.Currency == "Pounds Sterling"
                ? CurrencyCode.GBP
                : order.Currency == "Euro"
                    ? CurrencyCode.EUR
                    : order.Currency == "US Dollar"
                        ? CurrencyCode.USD
                        : CurrencyCode.GBP;
        }

        private BankHolidays GetUkGovBankHolidays()
        {
            string apiEndPoint = "https://www.gov.uk/bank-holidays.json";
            BankHolidays apiHolidays = null;

            try
            {
                using (var wb = new WebClient())
                {
                    wb.Encoding = Encoding.UTF8;
                    string response = wb.DownloadString(apiEndPoint);
                    apiHolidays = JsonConvert.DeserializeObject<BankHolidays>(response);
                    apiHolidays.EnglandAndWales.Events = apiHolidays.EnglandAndWales.Events.Where(w => w.Date.Year == DateTime.UtcNow.Year).ToList();
                    apiHolidays.Scotland.Events = apiHolidays.Scotland.Events.Where(w => w.Date.Year == DateTime.UtcNow.Year).ToList();
                    apiHolidays.NorthernIreland.Events = apiHolidays.NorthernIreland.Events.Where(w => w.Date.Year == DateTime.UtcNow.Year).ToList();
                }
            } catch (Exception ex)
            {
                logger.LogError($"Error retrieving UK Bank Holiday Dates from UK Gov API - {ex}");
            }

            return apiHolidays;
        }

        private static IEnumerable<DateTime> GetEventsDates(BankHolidays holidays)
        {
            var result = new List<DateTime>();

            if (holidays != null)
            {
                if (holidays.EnglandAndWales != null && holidays.EnglandAndWales.Events != null && holidays.EnglandAndWales.Events.Any())
                {
                    foreach (var holiday in holidays.EnglandAndWales.Events)
                    {
                        result.Add(holiday.Date);
                    }
                }
                if (holidays.Scotland != null && holidays.Scotland.Events != null && holidays.Scotland.Events.Any())
                {
                    foreach (var holiday in holidays.Scotland.Events)
                    {
                        result.Add(holiday.Date);
                    }
                }
                if (holidays.NorthernIreland != null && holidays.NorthernIreland.Events != null && holidays.NorthernIreland.Events.Any())
                {
                    foreach (var holiday in holidays.NorthernIreland.Events)
                    {
                        result.Add(holiday.Date);
                    }
                }
            }

            return result.Distinct();
        }

        public DateTime GetLastBusinessDayOfMonth(IEnumerable<DateTime> holidays, int year, int month)
        {
            DateTime lastBusinessDay = new DateTime();
            var daysInMonth = DateTime.DaysInMonth(year, month);

            while (daysInMonth > 0)
            {
                var dtCurrent = new DateTime(year, month, daysInMonth);
                if ((holidays == null || !holidays.Any()) && dtCurrent.DayOfWeek < DayOfWeek.Saturday && dtCurrent.DayOfWeek > DayOfWeek.Sunday)
                {
                    lastBusinessDay = dtCurrent;
                    daysInMonth = 0;
                } else if (dtCurrent.DayOfWeek < DayOfWeek.Saturday && dtCurrent.DayOfWeek > DayOfWeek.Sunday && !holidays.Contains(dtCurrent))
                {
                    lastBusinessDay = dtCurrent;
                    daysInMonth = 0;
                } else
                {
                    daysInMonth = daysInMonth - 1;
                }
            }
            return lastBusinessDay;
        }
    }
}