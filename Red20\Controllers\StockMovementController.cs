﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Red20.Model.Data.StockMovement;
using Red20.Service.Data.Interface;

namespace Red20.Controllers {
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class StockMovementController : ControllerBase {
        private IStockMovementService stockMovementService;
        private ILogger<AuthController> logger;
        private IStockMovementItemService stockMovementItemService;
        private IUnitOfWork unitOfWork;
        private IOrderService orderService;
        private IHireEquipmentService hireEquipmentService;
        private IEquipmentServiceHistoryService equipmentServiceHistoryService;
        private IEquipmentServiceStockItemService equipmentServiceStockItemService;
        private IOrderAssemblyHireEquipmentService orderAssemblyHireEquipmentService;
        private IJobInvoiceService jobInvoiceService;
        private IJobInvoiceItemService jobInvoiceItemService;
        private IStockService stockService;

        public StockMovementController(
            IStockMovementService stockMovementService,
            ILogger<AuthController> logger,
            IStockMovementItemService stockMovementItemService,
            IUnitOfWork unitOfWork,
            IOrderService orderService,
            IHireEquipmentService hireEquipmentService,
            IEquipmentServiceHistoryService equipmentServiceHistoryService,
            IEquipmentServiceStockItemService equipmentServiceStockItemService,
            IOrderAssemblyHireEquipmentService orderAssemblyHireEquipmentService,
            IJobInvoiceService jobInvoiceService,
            IJobInvoiceItemService jobInvoiceItemService,
            IStockService stockService)
        {
            this.stockMovementService = stockMovementService;
            this.stockMovementItemService = stockMovementItemService;
            this.hireEquipmentService = hireEquipmentService;
            this.equipmentServiceHistoryService = equipmentServiceHistoryService;
            this.equipmentServiceStockItemService = equipmentServiceStockItemService;
            this.orderAssemblyHireEquipmentService = orderAssemblyHireEquipmentService;
            this.jobInvoiceItemService = jobInvoiceItemService;
            this.jobInvoiceService = jobInvoiceService;
            this.orderService = orderService;
            this.stockService = stockService;
            this.unitOfWork = unitOfWork;
            this.logger = logger;
        }

        [HttpGet]
        [ProducesResponseType(200, Type = typeof(List<StockMovementModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get() {
            var stockMovements = await stockMovementService.GetAsync();
            return Ok(stockMovements);
        }

        [HttpGet("archived")]
        [ProducesResponseType(200, Type = typeof(List<StockMovementModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetArchived()
        {
            var stockMovements = await stockMovementService.GetArchivedAsync();
            return Ok(stockMovements);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(StockMovementModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id) {
            var stockMovement = await stockMovementService.GetAsync(id);
            return Ok(stockMovement);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(StockMovementModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody]StockMovementUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }
            try {
                var stockMovement = await stockMovementService.PostAsync(model);
                return Ok(stockMovement);
            } catch(Exception ex) {
                logger.LogError($"Cannot create Purchase Order - {ex}");
                return BadRequest();
            }

        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(StockMovementModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(404)]
        public async Task<IActionResult> Put(Guid id, [FromBody]StockMovementUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }

            if (await stockMovementService.GetAsync(id) is null) {
                return NotFound();
            }

            var stockMovement = await stockMovementService.PutAsync(id, model);
            return Ok(stockMovement);
        }
        [HttpDelete("{id}")]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Delete(Guid id) {
            if (await stockMovementService.GetAsync(id) is null) {
                return NotFound();
            }

            var stockMovementItems = await stockMovementItemService.GetByStockMovementAsync(id);

            if(stockMovementItems != null && stockMovementItems.Any())
            {
                var stockMovementItemsIds = stockMovementItems.Select(s => s.StockMovementItemId).ToArray();

                for(int i = 0;i < stockMovementItemsIds.Length; i++)
                {
                    var stockMovementItem = await unitOfWork.StockMovementItem.GetAsync(stockMovementItemsIds[i]);
                    if (stockMovementItem != null)
                    {
                        if (stockMovementItem.OrderId.HasValue)
                        {
                            var order = await orderService.GetAsync(stockMovementItem.OrderId.Value);

                            if (!string.IsNullOrWhiteSpace(stockMovementItem.HireEquipmentSerialNumber))
                            {
                                var hireEquipment = await hireEquipmentService.GetBySerialNumber(stockMovementItem.HireEquipmentSerialNumber);
                                var orderAssemblyHireEquipment = await orderAssemblyHireEquipmentService.GetBySerialNumberAsync(hireEquipment.SerialNumber);

                                if (hireEquipment != null)
                                {
                                    var serviceHistory = await equipmentServiceHistoryService.GetByOrderIdAssemblyIdAndHireEquipmentId(order.OrderId, orderAssemblyHireEquipment.OrderAssemblyId, hireEquipment.HireEquipmentId);

                                    if (serviceHistory != null)
                                    {
                                        var existingServiceHistoryStockItem = await equipmentServiceStockItemService.GetByOrderAndStockAsync(order.OrderId, stockMovementItem.StockId, serviceHistory.EquipmentServiceHistoryId);
                                        if (existingServiceHistoryStockItem != null)
                                        {
                                            await equipmentServiceStockItemService.DeleteAsync(existingServiceHistoryStockItem.EquipmentServiceStockItemId);
                                        }
                                    }
                                }
                            }
                        }

                        bool deleteInvoice = false;

                        if (stockMovementItem.JobInvoiceItemId.HasValue && stockMovementItem.JobInvoiceItemId.ToString() != Guid.Empty.ToString())
                        {
                            var jobInvoiceItemModel = await jobInvoiceItemService.GetAsync(stockMovementItem.JobInvoiceItemId.Value);
                            if (jobInvoiceItemModel != null)
                            {
                                var jobInvoice = jobInvoiceItemModel.JobInvoiceId.HasValue ? await jobInvoiceService.GetAsync(jobInvoiceItemModel.JobInvoiceId.Value) : null;
                                if (jobInvoice != null)
                                {
                                    if (!jobInvoice.JobInvoiceItems.Where(w => w.JobInvoiceItemId != jobInvoiceItemModel.JobInvoiceItemId).Any())
                                    {
                                        deleteInvoice = true;
                                    }
                                }
                                if (jobInvoiceItemModel.JobInvoiceItemId.HasValue)
                                {
                                    await jobInvoiceItemService.DeleteAsync(jobInvoiceItemModel.JobInvoiceItemId.Value);
                                }

                                if (deleteInvoice)
                                {
                                    await jobInvoiceService.DeleteAsync(jobInvoice.JobInvoiceId);
                                }
                            }
                        }

                        var stock = await stockService.GetAsync(stockMovementItem.StockId);

                        var stockTransaction = stock.StockTransactions.Where(c => c.StockMovementItemId == stockMovementItem.StockMovementItemId).OrderByDescending(c => c.Created).FirstOrDefault();

                        if (stockTransaction != null)
                        {
                            await unitOfWork.StockTransaction.DeleteAsync(stockTransaction.StockTransactionId);
                            stock.PhysicalStock = stock.PhysicalStock + stockTransaction.Quantity;
                        }

                        await unitOfWork.SaveChangesAsync();
                        await stockMovementItemService.DeleteAsync(stockMovementItemsIds[i]);
                    }
                }
            }

            await stockMovementService.DeleteAsync(id);
            return Ok();
        }
    }
}
