﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Red20.Excel.Export;
using Red20.Model.Data.Document;
using Red20.Model.Data.Enquiry;
using Red20.Model.Data.Quote;
using Red20.Service.Data.Interface;
using Red20.Service.Storage.Interface;
using Red20.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Red20.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class EnquiryController : ControllerBase
    {

        private IEnquiryService enquiryService;
        private IDocumentService documentService;
        private IUserService userService;
        private ILogger<AuthController> logger;
        private IStorageService blobStorage;
        private IUnitOfWork unitOfWork;

        public EnquiryController(
            IEnquiryService enquiryService,
            IDocumentService documentService,
            IStorageService blobStorage,
            IUserService userService,
            IUnitOfWork unitOfWork,
            ILogger<AuthController> logger)
        {

            this.enquiryService = enquiryService;
            this.documentService = documentService;
            this.blobStorage = blobStorage;
            this.userService = userService;
            this.unitOfWork = unitOfWork;
            this.logger = logger;
        }

        [HttpGet]
        [ProducesResponseType(200, Type = typeof(PaginatedResult<EnquiryModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get([FromQuery] EnquiryFilterParams filterParams)
        {
            var paginatedQuotes = await enquiryService.GetPaginatedEnquiries(filterParams);
            return Ok(paginatedQuotes);
        }

        [HttpGet("getAll")]
        [ProducesResponseType(200, Type = typeof(IList<EnquiryModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAllEnquiries()
        {
            var enquiries = enquiryService.GetAllEnquiries();
            return Ok(enquiries.OrderByDescending(c => c.Number));
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(EnquiryModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id)
        {

            var enquiry = await enquiryService.GetAsync(id);

            return Ok(enquiry);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(EnquiryModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody] EnquiryUpdateModel model)
        {
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);

            if (model is null)
            {
                return BadRequest();
            }

            model.Number = await NumberSequenceUtility.GetNextEnquiryNumber(unitOfWork);

            var enquiry = await enquiryService.PostAsync(model, user);

            return Ok(enquiry);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(EnquiryModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Put(Guid id, [FromBody] EnquiryUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }

            var enquiry = await enquiryService.GetAsync(id);

            if (enquiry is null)
            {
                return BadRequest();
            }

            enquiry = await enquiryService.PutAsync(id, model);

            return Ok(enquiry);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Delete(Guid id)
        {

            await enquiryService.DeleteAsync(id);
            return Ok();
        }

        [HttpPost("export")]
        [ProducesResponseType(200)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> GenerateReport(List<EnquiryModel> models)
        {
            try
            {
                var document = new DocumentModel();
                var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                      c.Type == ClaimTypes.Email);
                if (emailClaim == null)
                {
                    return Unauthorized();
                }

                var user = await userService.GetUserByEmailAsync(emailClaim.Value);
                var currentUser = $"{user.Firstname} {user.Lastname}";
                var itemData = ExportUtility.ExportEnquiryReport(models, currentUser);
                document = await documentService.PostAsync("Enquiry_Order_Report.xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"{user.Firstname} {user.Lastname}", "Enquiries", null, null, null, false, false, false);
                var file = File(itemData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                await blobStorage.UploadAsync(document.DocumentId, itemData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                return Ok();
            } catch (Exception)
            {
                return NotFound();
            }
        }

        [HttpGet("downloadReport")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> DownloadReport()
        {
            try
            {
                var lastDocument = await unitOfWork.Document.Query().Where(t => t.Type == "Enquiries").OrderByDescending(c => c.Created).FirstOrDefaultAsync();
                var download = await blobStorage.DownloadAsync(lastDocument.DocumentId);
                var file = File(download.Item1, download.Item2);
                return file;
            } catch (Exception)
            {
                return NotFound();
            }
        }
    }
}
