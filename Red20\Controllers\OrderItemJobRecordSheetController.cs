﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Red20.Model.Data.Order;
using Red20.Service.Data.Interface;

namespace Red20.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class OrderItemJobRecordSheetController : ControllerBase
    {
        private IOrderItemJobRecordSheetService service;
        private ILogger<AuthController> logger;
        private IUserService userService;

        public OrderItemJobRecordSheetController(
            IOrderItemJobRecordSheetService service,
            ILogger<AuthController> logger,
            IUserService userService) {
            this.service = service;
            this.logger = logger;
            this.userService = userService;
        }

        [HttpGet("byItem/{id}")]
        [ProducesResponseType(200, Type = typeof(OrderItemJobRecordSheetModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id) {
            var jobRecordSheet = await service.GetByOrderItemAsync(id);
            return Ok(jobRecordSheet);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(OrderItemJobRecordSheetModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody]OrderItemJobRecordSheetUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);

            try {
                var jobRecordSheet = await service.PostAsync(model, $"{user.Firstname} {user.Lastname}");
                return Ok(jobRecordSheet);
            } catch (Exception ex) {
                logger.LogError($"Cannot create Job Record Sheet - {ex}");
                return BadRequest();
            }
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(OrderItemJobRecordSheetModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Put(Guid id, [FromBody]OrderItemJobRecordSheetUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }

            var jobRecordSheet = await service.GetAsync(id);
            if (jobRecordSheet is null) {
                return BadRequest();
            }

            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);

            try {
                jobRecordSheet = await service.PutAsync(id, model, $"{user.Firstname} {user.Lastname}");
                return Ok(jobRecordSheet);
            } catch (Exception ex) {
                logger.LogError($"Cannot update Job Record Sheet - {ex}");
                return BadRequest();
            }
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Delete(Guid id) {
            if(!await service.OrderItemJobRecordSheetIdExistsAsync(id)) {
                return BadRequest();
            }
            await service.DeleteAsync(id);
            return Ok();
        }
    }
}