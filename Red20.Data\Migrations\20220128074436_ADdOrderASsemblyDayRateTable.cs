﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Red20.Data.Migrations
{
    public partial class ADdOrderASsemblyDayRateTable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "OrderAssemblyDayRates",
                columns: table => new
                {
                    Id = table.Column<Guid>(nullable: false),
                    OrderAssemblyId = table.Column<Guid>(nullable: false),
                    Created = table.Column<DateTime>(nullable: false),
                    From = table.Column<DateTime>(nullable: false),
                    To = table.Column<DateTime>(nullable: false),
                    DayRate = table.Column<double>(nullable: false),
                    Active = table.Column<bool>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OrderAssemblyDayRates", x => x.Id);
                    table.ForeignKey(
                        name: "FK_OrderAssemblyDayRates_OrderAssemblies_OrderAssemblyId",
                        column: x => x.OrderAssemblyId,
                        principalTable: "OrderAssemblies",
                        principalColumn: "OrderAssemblyId",
                        onDelete: ReferentialAction.NoAction);
                });

            migrationBuilder.CreateIndex(
                name: "IX_OrderAssemblyDayRates_OrderAssemblyId",
                table: "OrderAssemblyDayRates",
                column: "OrderAssemblyId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "OrderAssemblyDayRates");
        }
    }
}
