﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace Red20.Data.Migrations
{
    public partial class StockTransactionQuantityTotalNetString : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "QuantityString",
                table: "StockTransactions",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "TotalNetString",
                table: "StockTransactions",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "QuantityString",
                table: "StockTransactions");

            migrationBuilder.DropColumn(
                name: "TotalNetString",
                table: "StockTransactions");
        }
    }
}
