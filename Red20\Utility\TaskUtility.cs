using AutoMapper;
using Hangfire;
using Microsoft.CodeAnalysis;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Red20.Data.Context;
using Red20.Model.Data.BankHoliday;
using Red20.Model.Data.Job;
using Red20.Model.Entity;
using Red20.Service.Data.Interface;
using Red20.Service.Email.Interface;
using Red20.Service.Xero.Interface;
using Red20.Settings;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Xero.NetStandard.OAuth2.Model.Accounting;

namespace Red20.Utility {
    [AutomaticRetry(Attempts = 0)]
    public class TaskUtility {
        private DataContext context;
        private ILogger<TaskUtility> logger;
        private IEmailService emailService;
        private ClientSettings settings;
        private IOrderService orderService;
        private IJobService jobService;
        private IJobInvoiceService jobInvoiceService;
        private IJobInvoiceItemService jobInvoiceItemService;
        private IXeroService xeroService;
        private readonly IConfiguration config;
        private IUnitOfWork unitOfWork;
        private IOrderAssemblyService assemblyService;
        private IMapper mapper;

        public TaskUtility(
            DataContext context,
            ILogger<TaskUtility> logger,
            IEmailService emailService,
            IOptions<ClientSettings> settings,
            IConfiguration config,
            IOrderService orderService,
            IJobService jobService,
            IJobInvoiceService jobInvoiceService,
            IJobInvoiceItemService jobInvoiceItemService,
            IXeroService xeroService,
            IUnitOfWork unitOfWork,
            IOrderAssemblyService assemblyService,
            IMapper mapper) {

            this.context = context;
            this.logger = logger;
            this.emailService = emailService;
            this.config = config;
            this.settings = settings.Value;
            this.orderService = orderService;
            this.jobService = jobService;
            this.jobInvoiceService = jobInvoiceService;
            this.jobInvoiceItemService = jobInvoiceItemService;
            this.xeroService = xeroService;
            this.unitOfWork = unitOfWork;
            this.assemblyService = assemblyService;
            this.mapper = mapper;
        }

        [AutomaticRetry(Attempts = 1)]
        public async Task SendCertificateExpiryEmail() {
            var suppliers = await context.Suppliers.Where(w => w.CertificateExpiryDate.HasValue)
                .Include(i => i.SupplierAddresses).ThenInclude(i => i.SupplierContacts)
                .ToListAsync();

            if (suppliers != null && suppliers.Any()) {
                foreach (var supplier in suppliers) {
                    string email = config.GetValue<string>("Email:CertificateExpiryEmail");
                    string name = "";
                    var url = settings.Host;
                    var link = Flurl.Url.Combine(url, "suppliers");
                    name = $"{supplier.Name}";

                    double days = (supplier.CertificateExpiryDate.Value.Date - DateTime.UtcNow.Date).TotalDays;
                    if (days < 3 && days > 0 && supplier.Is2DayEmailSent is false) {/*this days greater than 0 is there in case the red 20 admins forget to go and update the expiry date*/
                        try {
                            await emailService.SendCertificateExpiryEmail(email, name, 2.0, link);
                            supplier.Is2DayEmailSent = true;
                            await context.SaveChangesAsync();
                        } catch (Exception ex) {
                            logger.LogError($"{ex}");
                        }
                    } else if (supplier.CertificateExpiryDate.Value.Date.AddDays(7) == DateTime.UtcNow.Date && supplier.Is14DayEmailSent is false) {
                        try {
                            await emailService.SendCertificateExpiredEmail(email, name, supplier.CertificateExpiryDate.Value.Date, link);
                            supplier.Is14DayEmailSent = true;
                            await context.SaveChangesAsync();
                        } catch (Exception ex) {
                            logger.LogError($"{ex}");
                        }
                    }
                }
            }
        }

        [AutomaticRetry(Attempts = 1)]
        public async Task SendInsuranceCertificateExpiryEmail() {
            var suppliers = await context.Suppliers.Where(w => w.InsuranceExpiryDate.HasValue && w.InsuranceExpiryDate != null)
                .Include(i => i.SupplierAddresses).ThenInclude(i => i.SupplierContacts)
                .ToListAsync();

            if (suppliers != null && suppliers.Any()) {
                foreach (var supplier in suppliers) {
                    string email = config.GetValue<string>("Email:CertificateExpiryEmail");
                    string name = "";
                    var url = settings.Host;
                    var link = Flurl.Url.Combine(url, "suppliers");
                    name = $"{supplier.Name}";

                    double days = (supplier.InsuranceExpiryDate.Value.Date - DateTime.UtcNow.Date).TotalDays;
                    if (days < 3 && days > 0 && supplier.Is2DayInsuranceEmailSent is false) {/*this days greater than 0 is there in case the red 20 admins forget to go and update the expiry date*/
                        try {
                            await emailService.SendCertificateExpiryEmail(email, name, 2.0, link);
                            supplier.Is2DayInsuranceEmailSent = true;
                            await context.SaveChangesAsync();
                        } catch (Exception ex) {
                            logger.LogError($"{ex}");
                        }
                    } else if (supplier.InsuranceExpiryDate.Value.Date.AddDays(7) == DateTime.UtcNow.Date && supplier.Is7DayInsuranceEmailSent is false) {
                        try {
                            await emailService.SendInsuranceCertificateExpiredEmail(email, name, supplier.InsuranceExpiryDate.Value.Date, link);
                            supplier.Is7DayInsuranceEmailSent = true;
                            await context.SaveChangesAsync();
                        } catch (Exception ex) {
                            logger.LogError($"{ex}");
                        }
                    }
                }
            }
        }

        [AutomaticRetry(Attempts = 1)]
        public async Task SendCurrencyRemiderEmail() {
            var url = settings.Host;
            var link = Flurl.Url.Combine(url, "currencyRates");
            var now = DateTime.UtcNow.Date;
            var lastDayOfTheMonth = new DateTime(now.Year, now.Month, DateTime.DaysInMonth(now.Year, now.Month));

            string email = "<EMAIL>";
            string name = "Lena Fraser";
            if (now == lastDayOfTheMonth) {
                await emailService.SendCurrencyReminderEmail(email, name, link);
            }
        }

        [AutomaticRetry(Attempts = 1)]
        public async Task SendQuoteReminderEmail() {
            var quotes = await context.Quotes.Where(w => w.ReminderDate.HasValue).ToListAsync();

            if (quotes != null && quotes.Any()) {
                foreach (var quote in quotes) {
                    if (quote.ReminderDate.Value.Date == DateTime.UtcNow.Date) {
                        if (!string.IsNullOrWhiteSpace(quote.CreatedBy)) {
                            var firstName = quote.CreatedBy.Split(" ")[0];
                            var lastName = quote.CreatedBy.Split(" ")[1];

                            if (!string.IsNullOrWhiteSpace(firstName) && !string.IsNullOrWhiteSpace(lastName)) {
                                var user = await context.Users.Where(w => w.Firstname == firstName && w.Lastname == lastName).FirstOrDefaultAsync();
                                if (user != null) {
                                    string email = user.EmailAddress;
                                    string quoteNumber = quote.Number;
                                    string customerFeedback = quote.CustomerFeedback;

                                    try {
                                        await emailService.SendQuoteReminderEmail(email, $"{user.Firstname} {user.Lastname}", quoteNumber, customerFeedback);
                                    } catch (Exception ex) {
                                        logger.LogError($"Cannot send Quote Reminder Email - {ex}");
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        public async Task OnHireAssembliesRecurringInvoicePerOrder(Guid orderId) {
            var lastDayOfMonth = new DateTime(DateTime.UtcNow.Year, DateTime.UtcNow.Month, DateTime.DaysInMonth(DateTime.UtcNow.Year, DateTime.UtcNow.Month));
            var hireEndString = lastDayOfMonth.ToString("dd/MM/yyyy");

            var order = await orderService.GetAsync(orderId);
            bool hasInitialDeliveryChargeToInvoice = false;

            var orderAssemblies = order.OrderAssemblies.Where(w => w.HireStart.HasValue && !w.HireEnd.HasValue).ToList();
            if (orderAssemblies != null && orderAssemblies.Any()) {
                orderAssemblies = orderAssemblies.OrderBy(o => o.Created).ToList();

                var job = await jobService.GetByOrderIdAsync(order.OrderId);
                var jobInvoice = new JobInvoiceModel();
                if (job == null) {
                    job = await jobService.PostAsync(new JobModel {
                        CreatedBy = "System Recurring",
                        OrderId = order.OrderId,
                        CostCentre = order.Number.Contains("SH") ? "Oldmeldrum" : "West Bromwich",
                        OrderType = order.Type,
                        OrderNumber = order.Number,
                        OrderCustomerName = order.Customer != null ? order.Customer.Name : string.Empty,
                        Description = order.FirstItemDescription,
                        DateOrderRaised = order.Created,
                        Currency = order.Currency
                    });
                }

                int number = 1;
                if (job != null) {
                    var jobInvoices = await jobInvoiceService.GetByJobIdAsync(job.JobId);
                    number = jobInvoices.Any(a => a.JobType == "Sale" || a.JobType == "Hire") ? jobInvoices.Where(a => a.JobType == "Sale" || a.JobType == "Hire").Count() + 1 : number;
                }

                jobInvoice.JobId = job.JobId;
                jobInvoice.CreatedBy = $"Automated Recurring Invoice";
                jobInvoice.JobType = order.Type;
                jobInvoice.InvoiceNumber = $"{order.Number}/{number}";
                jobInvoice.InvoiceDate = lastDayOfMonth.Date;
                jobInvoice.CustomerPO = order != null ? order.CustomerRef : string.Empty;

                if (!await jobInvoiceService.JobInvoiceNumberExistsAsync(jobInvoice.InvoiceNumber)) {
                    jobInvoice = await jobInvoiceService.PostAsync(jobInvoice);
                } else {
                    bool invoiceNumberExists = true;

                    while (invoiceNumberExists) {
                        number++;
                        jobInvoice.InvoiceNumber = $"{order.Number}/{number}";

                        if (!await jobInvoiceService.JobInvoiceNumberExistsAsync(jobInvoice.InvoiceNumber)) {
                            jobInvoice = await jobInvoiceService.PostAsync(jobInvoice);
                            invoiceNumberExists = false;
                        }
                    }
                }

                var jobInvoiceItemModels = new List<JobInvoiceItemModel>();

                //check if initial delivery charge has to be added to this invoice
                if (order.DeliveryCharge.HasValue && !order.IsDeliveryChargeInvoiced) {

                    bool orderHasInitialDeliveryCharge = await unitOfWork.OrderAssembly.Query(q => q.OrderId == order.OrderId && q.IsInitialDeliveryCharge).AnyAsync();

                    if (!orderHasInitialDeliveryCharge) {
                        hasInitialDeliveryChargeToInvoice = true;

                        var orderAssembly = await unitOfWork.OrderAssembly.CreateAsync(new OrderAssembly {
                            OrderId = order.OrderId,
                            AccountCode = "1013",
                            Created = DateTime.UtcNow,
                            Description = "Delivery Charge",
                            IsSimpleAssembly = true,
                            IsInitialDeliveryCharge = true,
                            DayRate = order.DeliveryCharge.Value,
                            ItemDescription = "Delivery Charge",
                            InvoiceDate = lastDayOfMonth
                        });

                        await unitOfWork.SaveChangesAsync();

                        var invoiceItem = await jobInvoiceItemService.PostAsync(new JobInvoiceItemModel {
                            JobId = job.JobId,
                            JobInvoiceId = jobInvoice.JobInvoiceId,
                            InvoiceNumber = jobInvoice.InvoiceNumber,
                            InvoiceType = "INV",
                            Value = order.DeliveryCharge.Value,
                            Quantity = 1,
                            UnitPrice = order.DeliveryCharge.Value,
                            Description = "Delivery Charge",
                            Currency = order.Currency,
                            AccountCode = "1013",
                            OrderItemId = orderAssembly.OrderAssemblyId,
                            QuantityToDeliver = 0.0,
                            ToFollow = 0.0,
                            Vat = order.TaxCode,
                            InvoiceDate = lastDayOfMonth.Date,
                            HireStartDateString = "",
                            HireEndDateString = "",
                            DiscountRate = 0.0,
                            Created = DateTime.UtcNow,
                            IsInitialDeliveryCharge = true,
                            IsDeliveryCharge = true
                        });

                        jobInvoiceItemModels.Add(invoiceItem);
                    }
                }

                var orderAssemblyIds = new List<Guid>();

                var dayRates = new List<OrderAssemblyDayRate>();
                var firstDayOfMonth = new DateTime(DateTime.UtcNow.Year, DateTime.UtcNow.Month, 1).Date;

                for (var x = 0; x < orderAssemblies.Count; x++) {

                        dayRates = await unitOfWork.OrderAssemblyDayRate
                                    .Query(q =>
                                        q.OrderAssemblyId == orderAssemblies[x].OrderAssemblyId &&
                                        ((q.From.Year == DateTime.UtcNow.Year && q.From.Month == DateTime.UtcNow.Month) ||
                                         (q.To.Year == DateTime.UtcNow.Year && q.To.Month == DateTime.UtcNow.Month)))
                                    .OrderBy(o => o.From)
                                    .ToListAsync();

                    if (dayRates != null && dayRates.Any())
                    {
                        double totalQuantity = (lastDayOfMonth - firstDayOfMonth).TotalDays + 1;

                        double remainingQuantity = totalQuantity;

                        foreach (var dayRate in dayRates)
                        {
                            var rate = dayRate.DayRate;

                            var fromDate = dayRate.From < firstDayOfMonth ? firstDayOfMonth : dayRate.From;

                            var toDate = dayRate.To > lastDayOfMonth ? lastDayOfMonth.Date : dayRate.To.Date;
                            var dayRateQuantity = (toDate - fromDate).TotalDays + 1;

                            var discount = dayRate.Discount.HasValue ? (dayRate.Discount.Value / 100) * dayRateQuantity * rate : 
                                           orderAssemblies[x].Discount.HasValue ? (orderAssemblies[x].Discount.Value / 100) * dayRateQuantity * rate :
                                           (double?)null;

                            var dayRateDiscountValue = discount.HasValue ? discount.Value : 0.0;
                            var dayRateInvoiceValue = dayRateQuantity * rate - dayRateDiscountValue;

                            var hireStartDateString = fromDate.ToString("dd/MM/yyyy");

                            var jobInvoiceItemModel = new JobInvoiceItemModel
                            {
                                JobId = job.JobId,
                                JobInvoiceId = jobInvoice.JobInvoiceId,
                                InvoiceNumber = jobInvoice.InvoiceNumber,
                                InvoiceType = "INV",
                                Value = dayRateInvoiceValue,
                                Quantity = dayRateQuantity,
                                UnitPrice = rate,
                                Description = orderAssemblies[x].OrderAssemblyFirstTwoLinesDescription,
                                Currency = order.Currency,
                                AccountCode = orderAssemblies[x].IsSimpleAssembly ? "1013" : order.OrderAccountCode,
                                OrderItemId = orderAssemblies[x].OrderAssemblyId,
                                QuantityToDeliver = 0.0,
                                ToFollow = 0.0,
                                Vat = order.TaxCode,
                                InvoiceDate = lastDayOfMonth.Date,
                                HireStartDateString = hireStartDateString,
                                HireEndDateString = toDate.ToString("dd/MM/yyyy"),
                                DiscountRate = dayRate.Discount.HasValue ? dayRate.Discount.Value : orderAssemblies[x].Discount.HasValue ? orderAssemblies[x].Discount.Value : 0.0,
                                IsInitialDeliveryCharge = false,
                                IsDeliveryCharge = false,
                                IsDayRate = true,
                                From = fromDate,
                                To = toDate
                            };

                            await jobInvoiceItemService.PostAsync(jobInvoiceItemModel);
                            jobInvoiceItemModels.Add(jobInvoiceItemModel);

                            remainingQuantity = remainingQuantity - dayRateQuantity;
                        }

                        if(remainingQuantity > 0)
                        {
                            var discountValue = orderAssemblies[x].Discount.HasValue ? 
                                                    orderAssemblies[x].Discount.Value / 100 * remainingQuantity * orderAssemblies[x].DayRate : 0.0;

                            var invoiceValue = (remainingQuantity * orderAssemblies[x].DayRate) - discountValue;

                            var description = "";

                            for(int index = 0; index < dayRates.Count; index++)
                            {
                                if(dayRates.Count == 1)
                                {
                                    if (dayRates[0].From.Date > orderAssemblies[x].HireStart.Value.Date)
                                    {
                                        var dateToUse = orderAssemblies[x].HireStart.Value.Date < firstDayOfMonth ? firstDayOfMonth : orderAssemblies[x].HireStart.Value.Date;
                                        description +=
                                            $"Hire Period {dateToUse.ToString("dd/MM/yyyy")} to {dayRates.First().From.AddDays(-1).ToString("dd/MM/yyyy")}" + Environment.NewLine;
                                    }
                                    else if(dayRates[0].From.Date == orderAssemblies[x].HireStart.Value.Date)
                                    {
                                        if(dayRates.First().To.Date < lastDayOfMonth.Date)
                                        {
                                            description += $"Hire Period {dayRates.First().To.Date.AddDays(1).ToString("dd/MM/yyyy")} to {lastDayOfMonth.Date.ToString("dd/MM/yyyy")}" + Environment.NewLine;
                                        }
                                    }
                                }
                                else
                                {
                                    if (index == 0)
                                    {
                                        if (dayRates[0].From.Date > orderAssemblies[x].HireStart.Value.Date)
                                        {
                                            var dateToUse = orderAssemblies[x].HireStart.Value.Date < firstDayOfMonth ? firstDayOfMonth : orderAssemblies[x].HireStart.Value.Date;
                                            description += $"Hire Period {dateToUse.ToString("dd/MM/yyyy")} to {dayRates.First().From.AddDays(-1).ToString("dd/MM/yyyy")}" + Environment.NewLine;
                                        }
                                        else if(dayRates[0].From.Date == orderAssemblies[x].HireStart.Value.Date)
                                        {
                                            bool hasDaysSinceLastDayRatePeriod = (dayRates[index].To.Date - dayRates[index + 1].From.Date).TotalDays > 1;

                                            if (hasDaysSinceLastDayRatePeriod)
                                            {
                                                description += $"Hire Period {dayRates[index].To.AddDays(1).ToString("dd/MM/yyyy")} to {dayRates[index + 1].From.AddDays(-1).ToString("dd/MM/yyyy")}" + Environment.NewLine;
                                            }
                                        }
                                    }
                                    else
                                    {
                                        if (index < dayRates.Count - 1) // if we have not reached the last day rate.
                                        {
                                            bool hasDaysSinceLastDayRatePeriod = (dayRates[index].From - dayRates[index - 1].To).TotalDays > 1;

                                            if (hasDaysSinceLastDayRatePeriod)
                                            {
                                                description += $"Hire Period {dayRates[index - 1].To.AddDays(1).ToString("dd/MM/yyyy")} to {dayRates[index].From.AddDays(-1).ToString("dd/MM/yyyy")}" + Environment.NewLine;
                                            }
                                        }
                                        else
                                        {
                                            bool hasDaysSinceLastDayRatePeriod = (dayRates[index].From - dayRates[index - 1].To).TotalDays > 1;

                                            if (hasDaysSinceLastDayRatePeriod)
                                            {
                                                description += $"Hire Period {dayRates[index - 1].To.AddDays(1).ToString("dd/MM/yyyy")} to {dayRates[index].From.AddDays(-1).ToString("dd/MM/yyyy")}" + Environment.NewLine;
                                            }

                                            if (dayRates[index].To.Date < lastDayOfMonth.Date)
                                            {
                                                description += $"Hire Period {dayRates[index].To.AddDays(1).ToString("dd/MM/yyyy")} to {lastDayOfMonth.ToString("dd/MM/yyyy")}" + Environment.NewLine;
                                            }
                                        }
                                    }
                                }
                            }

                            var hireStartDateString = orderAssemblies[x].InvoiceDate.HasValue ? orderAssemblies[x].InvoiceDate.Value.AddDays(1).Date.ToString("dd/MM/yyyy") :
                                              orderAssemblies[x].HireStart.HasValue ? orderAssemblies[x].HireStart.Value.ToString("dd/MM/yyyy") : string.Empty;


                            var jobInvoiceItemModel = new JobInvoiceItemModel
                            {
                                JobId = job.JobId,
                                JobInvoiceId = jobInvoice.JobInvoiceId,
                                InvoiceNumber = jobInvoice.InvoiceNumber,
                                InvoiceType = "INV",
                                Value = invoiceValue,
                                Quantity = remainingQuantity,
                                UnitPrice = orderAssemblies[x].DayRate,
                                Description = description + orderAssemblies[x].OrderAssemblyFirstTwoLinesDescription,
                                Currency = order.Currency,
                                AccountCode = orderAssemblies[x].IsSimpleAssembly ? "1013" : order.OrderAccountCode,
                                OrderItemId = orderAssemblies[x].OrderAssemblyId,
                                QuantityToDeliver = 0.0,
                                ToFollow = 0.0,
                                Vat = order.TaxCode,
                                InvoiceDate = lastDayOfMonth.Date,
                                HireStartDateString = hireStartDateString,
                                HireEndDateString = hireEndString,
                                DiscountRate = orderAssemblies[x].Discount.HasValue ? orderAssemblies[x].Discount.Value : 0.0,
                                IsInitialDeliveryCharge = false,
                                IsDeliveryCharge = false,
                                IsDayRate = true,
                                From = null,
                                To = null
                            };

                            await jobInvoiceItemService.PostAsync(jobInvoiceItemModel);

                            jobInvoiceItemModels.Add(jobInvoiceItemModel);
                        }
                    }
                    else
                    {
                        // Determine if invoice date exists and falls within the current month
                        bool invoiceDateInCurrentMonth = orderAssemblies[x].InvoiceDate.HasValue && 
                            orderAssemblies[x].InvoiceDate.Value.Year == firstDayOfMonth.Year && 
                            orderAssemblies[x].InvoiceDate.Value.Month == firstDayOfMonth.Month;

                        // Determine if hire start date exists and falls within the current month
                        bool hireStartInCurrentMonth = orderAssemblies[x].HireStart.HasValue && 
                            orderAssemblies[x].HireStart.Value.Year == firstDayOfMonth.Year && 
                            orderAssemblies[x].HireStart.Value.Month == firstDayOfMonth.Month;

                        // Calculate quantity and hire start date string
                        var quantity = 0.0;
                        var hireStartDateString = string.Empty;

                        if (orderAssemblies[x].InvoiceDate.HasValue)
                        {
                            if (invoiceDateInCurrentMonth)
                            {
                                if (orderAssemblies[x].HireStart.HasValue && hireStartInCurrentMonth)
                                {
                                    // Both invoice date and hire start are in current month
                                    quantity = (lastDayOfMonth - orderAssemblies[x].HireStart.Value.Date).TotalDays + 1;
                                    hireStartDateString = orderAssemblies[x].HireStart.Value.ToString("dd/MM/yyyy");
                                }
                                else
                                {
                                    // Invoice date in current month, hire start not in current month
                                    quantity = (lastDayOfMonth - firstDayOfMonth).TotalDays + 1;
                                    hireStartDateString = firstDayOfMonth.Date.ToString("dd/MM/yyyy");
                                }
                            }
                            else // Invoice date not in current month
                            {
                                if (orderAssemblies[x].HireStart.HasValue && hireStartInCurrentMonth)
                                {
                                    // Invoice date not in current month, hire start in current month
                                    quantity = (lastDayOfMonth - orderAssemblies[x].HireStart.Value.Date).TotalDays + 1;
                                    hireStartDateString = orderAssemblies[x].HireStart.Value.ToString("dd/MM/yyyy");
                                }
                                else
                                {
                                    // Invoice date not in current month, hire start not in current month
                                    quantity = (lastDayOfMonth - firstDayOfMonth).TotalDays + 1;
                                    hireStartDateString = firstDayOfMonth.Date.ToString("dd/MM/yyyy");
                                }
                            }
                        }
                        else if (orderAssemblies[x].HireStart.HasValue)
                        {
                            // No invoice date, but hire start exists
                            quantity = (lastDayOfMonth - orderAssemblies[x].HireStart.Value.Date).TotalDays + 1;
                            hireStartDateString = orderAssemblies[x].HireStart.Value.ToString("dd/MM/yyyy");
                        }
                        // The fallback (no dates available) is already handled with default initialization

                        var discountValue = orderAssemblies[x].Discount.HasValue && quantity > 0 ?
                                            orderAssemblies[x].Discount.Value / 100 * quantity * orderAssemblies[x].DayRate :
                                            0.0;

                        var invoiceValue = (quantity * orderAssemblies[x].DayRate) - discountValue;

                        var jobInvoiceItemModel = new JobInvoiceItemModel
                        {
                            JobId = job.JobId,
                            JobInvoiceId = jobInvoice.JobInvoiceId,
                            InvoiceNumber = jobInvoice.InvoiceNumber,
                            InvoiceType = "INV",
                            Value = invoiceValue,
                            Quantity = quantity,
                            UnitPrice = orderAssemblies[x].DayRate,
                            Description = orderAssemblies[x].OrderAssemblyFirstTwoLinesDescription,
                            Currency = order.Currency,
                            AccountCode = orderAssemblies[x].IsSimpleAssembly ? "1013" : order.OrderAccountCode,
                            OrderItemId = orderAssemblies[x].OrderAssemblyId,
                            QuantityToDeliver = 0.0,
                            ToFollow = 0.0,
                            Vat = order.TaxCode,
                            InvoiceDate = lastDayOfMonth.Date,
                            HireStartDateString = hireStartDateString,
                            HireEndDateString = hireEndString,
                            DiscountRate = orderAssemblies[x].Discount.HasValue ? orderAssemblies[x].Discount.Value : 0.0,
                            IsInitialDeliveryCharge = false,
                            IsDeliveryCharge = false,
                            IsDayRate = false,
                            From = null,
                            To = null
                        };

                        await jobInvoiceItemService.PostAsync(jobInvoiceItemModel);

                        jobInvoiceItemModels.Add(jobInvoiceItemModel);
                    }
                    
                    orderAssemblyIds.Add(orderAssemblies[x].OrderAssemblyId);
                }

                if (orderAssemblyIds.Any()) {
                    foreach(var id in orderAssemblyIds) {
                        var assembly = await unitOfWork.OrderAssembly.GetAsync(id);
                        assembly.InvoiceDate = lastDayOfMonth.Date;
                        unitOfWork.OrderAssembly.Update(assembly);
                    }
                    await unitOfWork.SaveChangesAsync();
                }

                List<LineItem> lineItems = new List<LineItem>();
                if (jobInvoiceItemModels != null && jobInvoiceItemModels.Any()) {
                    jobInvoiceItemModels = jobInvoiceItemModels.OrderByDescending(o => !o.IsDeliveryCharge).ToList();

                    foreach (var jobInvoiceItem in jobInvoiceItemModels) {
                        try {

                            if(jobInvoiceItem.IsDayRate && jobInvoiceItem.From.HasValue && jobInvoiceItem.To.HasValue)
                            {
                                jobInvoiceItem.Description = $"Hire Period {jobInvoiceItem.From.Value.ToString("dd/MM/yyyy")} to {jobInvoiceItem.To.Value.ToString("dd/MM/yyyy")}" + Environment.NewLine + jobInvoiceItem.Description;
                            } 
                            else if (jobInvoiceItem.IsDayRate && !jobInvoiceItem.From.HasValue && !jobInvoiceItem.To.HasValue)
                            {
                                jobInvoiceItem.Description = jobInvoiceItem.Description;
                            }
                            else
                            {
                                jobInvoiceItem.Description = jobInvoiceItem.IsInitialDeliveryCharge || jobInvoiceItem.IsDeliveryCharge ? "Delivery Charge" :
                                $"Hire Period {jobInvoiceItem.HireStartDateString} to {hireEndString} Continues. " + Environment.NewLine + jobInvoiceItem.Description;
                            }

                            var taxRate = await xeroService.GetTaxRateAsync(jobInvoiceItem.AccountCode);
                            var accountCode = await xeroService.GetAccountAsync(jobInvoiceItem.AccountCode);
                            List<LineItemTracking> trackings = new List<LineItemTracking>();

                            trackings.Add(new LineItemTracking {
                                Name = "Location",
                                Option = order.Number.Contains("SH") ? "001 - Aberdeen" : "002 - West Brom",
                            });

                            LineItem lineItem = new LineItem {
                                Quantity = Convert.ToDecimal(jobInvoiceItem.Quantity),
                                UnitAmount = Convert.ToDecimal(jobInvoiceItem.UnitPrice),
                                Description = !string.IsNullOrWhiteSpace(jobInvoiceItem.Description) ? Regex.Replace(jobInvoiceItem.Description, "<.*?>", String.Empty) : string.Empty,
                                AccountCode = accountCode.Code,
                                TaxType = taxRate != null ? taxRate.TaxType : String.Empty,
                                DiscountRate = jobInvoiceItem.DiscountRate.HasValue ? Convert.ToDecimal(jobInvoiceItem.DiscountRate) : (decimal?)null,
                                Tracking = trackings
                            };

                            lineItems.Add(lineItem);
                        } catch (Exception ex) {
                            if(ex != null && ex.Message != null)
                            {
                                var type = ex.GetType().ToString();
                                var message = ex.Message;

                                var xeroApiException = new XeroApiException();
                                xeroApiException.Type = type;
                                xeroApiException.Message = message;
                                xeroApiException.OrderNumber = order.Number;
                                xeroApiException.OrderAssemblyId = jobInvoiceItem.OrderItemId;
                                xeroApiException.OrderId = order.OrderId;
                                await unitOfWork.XeroApiException.CreateAsync(xeroApiException);
                                await unitOfWork.SaveChangesAsync();
                            }

                            logger.LogError($"Xero Api Exception for Hire Order Recurring Invoicing Order: {order.Number} and Hire Assembly ID: {jobInvoiceItem.OrderItemId} => {ex}");
                            continue;
                        }
                    }
                }

                var xeroContact = order.Customer != null && order.Customer.XeroContactId.HasValue ?
                                  await xeroService.GetContactAsync(order.Customer.XeroContactId.Value) :
                                  null;

                if (xeroContact is null) {
                    var addresses = new List<Address>();
                    Address address = new Address();

                    var customerInvoiceAddress = order.Customer != null && order.Customer.CustomerAddresses.Any() ?
                                                 order.Customer.CustomerAddresses.Where(s => s.IsInvoiceDefault).FirstOrDefault() :
                                                 null;

                    address.AddressType = Address.AddressTypeEnum.POBOX;
                    address.AddressLine1 = customerInvoiceAddress != null ? customerInvoiceAddress.Street : string.Empty;
                    address.AddressLine2 = customerInvoiceAddress != null ? customerInvoiceAddress.Street1 : string.Empty;
                    address.City = customerInvoiceAddress != null ? customerInvoiceAddress.Town : string.Empty;
                    address.Country = customerInvoiceAddress != null ? customerInvoiceAddress.Country : string.Empty;
                    address.PostalCode = customerInvoiceAddress != null ? customerInvoiceAddress.PostCode : string.Empty;

                    addresses.Add(address);

                    xeroContact = new Contact {
                        ContactStatus = Contact.ContactStatusEnum.ACTIVE,
                        Name = order.Customer.Name,
                        Addresses = addresses,
                        DefaultCurrency = order.Customer.Currency == "Pounds Sterling" ? CurrencyCode.GBP :
                                          order.Customer.Currency == "Euro" ? CurrencyCode.EUR :
                                          order.Customer.Currency == "US Dollar" ? CurrencyCode.USD :
                                          CurrencyCode.GBP
                    };

                    try {
                        xeroContact = await xeroService.UpdateOrCreateContactAsync(xeroContact);
                    } catch (Exception ex) {
                        logger.LogError($"Error creating Xero Contact for Hire Order: {order.Number} and Customer: {(order.Customer != null ? order.Customer.Name : "")} => {ex}");
                    }
                }

                Invoice invoice = new Invoice(); //xero invoice object
                invoice.Date = jobInvoice.InvoiceDate;
                invoice.InvoiceNumber = jobInvoice.InvoiceNumber;
                invoice.CurrencyCode = order.Currency == "Pounds Sterling" ? CurrencyCode.GBP :
                                       order.Currency == "Euro" ? CurrencyCode.EUR :
                                       order.Currency == "US Dollar" ? CurrencyCode.USD :
                                       CurrencyCode.GBP;
                invoice.Type = Invoice.TypeEnum.ACCREC;
                invoice.Status = Invoice.StatusEnum.DRAFT;
                invoice.LineItems = lineItems;
                invoice.Contact = xeroContact;
                invoice.DueDate = DateTime.Now.AddDays(30);
                invoice.Reference = jobInvoice.CustomerPO;

                try {
                    var existingInvoices = await xeroService.GetInvoiceByNumberAsync(invoice.InvoiceNumber);
                    if (existingInvoices != null && existingInvoices.Any())
                    {
                        invoice.InvoiceNumber = invoice.InvoiceNumber + " (New)";
                    }

                    invoice = await xeroService.UpdateOrCreateInvoicesAsync(invoice);
                    var jobInvoiceRecord = await unitOfWork.JobInvoice.GetAsync(jobInvoice.JobInvoiceId);
                    jobInvoiceRecord.XeroInvoiceId = invoice.InvoiceID;
                    unitOfWork.JobInvoice.Update(jobInvoiceRecord);
                } catch (Exception ex) {
                    logger.LogError($"Error creating Xero Invoice for Hire Order: {order.Number} => {ex}");
                }

                if (hasInitialDeliveryChargeToInvoice is true) {
                    order = await orderService.MarkDeliveryCharge(order.OrderId);
                }
            }
        }

        [AutomaticRetry(Attempts = 1)]
        public async Task ArchivePurchaseOrders() {
            var purchaseOrders = await context.PurchaseOrders.Where(c => c.DateCompleted.HasValue && !c.ArchivedDate.HasValue).ToListAsync();

            if (purchaseOrders != null && purchaseOrders.Any()) {
                foreach (var purchaseOrder in purchaseOrders) {
                    var completedDate = purchaseOrder.DateCompleted.Value.Date.AddMonths(3);
                    var now = DateTime.UtcNow.Date;
                    if (completedDate <= now) {
                        purchaseOrder.ArchivedDate = DateTime.UtcNow;

                        context.PurchaseOrders.Update(purchaseOrder);

                        await context.SaveChangesAsync();
                    }
                }
            }
        }

        [AutomaticRetry(Attempts = 1)]
        public async Task CreateCheckInUsers() {
           var users = await context.Users.Where(c => c.Enabled && c.Firstname != "Ravi" && c.Firstname != "Arrash" && c.Firstname != "Check-In" && !c.Leaver).ToListAsync();

            foreach (var user in users) {
                var userCheckIn = new UserCheckIn {
                    UserCheckInId = Guid.NewGuid(),
                    UserId = user.UserId,
                    Username = $"{user.Firstname} {user.Lastname}",
                    Modified = DateTime.UtcNow,
                    ModifiedBy = "System",
                    Created = DateTime.UtcNow
                };
                  context.UserCheckIns.Add(userCheckIn);

                await context.SaveChangesAsync();
            }
        }

        [AutomaticRetry(Attempts = 1)]
        public async Task CreateCheckOutUsers() {
            var userCheckInUsers = await context.UserCheckIns.Where(c => c.LastCheckedIn.HasValue && !c.LastCheckedOut.HasValue).ToListAsync();

            foreach (var userCheckIn in userCheckInUsers) {
                var checkInCheckOut = await context.UserCheckInCheckOuts.Where(c => c.UserCheckInId == userCheckIn.UserCheckInId).OrderByDescending(c => c.Created).FirstOrDefaultAsync();

                checkInCheckOut.CheckOut = DateTime.UtcNow;

                context.UserCheckInCheckOuts.Update(checkInCheckOut);

                userCheckIn.LastCheckedOut = DateTime.UtcNow;
               
                context.UserCheckIns.Update(userCheckIn);

                await context.SaveChangesAsync();
            }
        }

        [AutomaticRetry(Attempts = 1)]
        public async Task CreateCheckFollowupCallSheets()
        {
            var customerCallSheetThreeDays = await context.CustomerCallSheets.Where(c => c.FollowUpDate.Value.Date.AddDays(-3) == DateTime.Now.Date).Include(c => c.Customer).ToListAsync();
            var url = settings.Host;
            var link = Flurl.Url.Combine(url, "customerCallSheets");

            if (customerCallSheetThreeDays.Count > 0)
            {
                foreach (var callSheet in customerCallSheetThreeDays)
                {
                    await emailService.SendCallSheetReminderEmail(callSheet.CreatedByEmail, callSheet.CreatedBy, callSheet.CustomerId.HasValue ? callSheet.Customer.Name : callSheet.NewCustomerName, callSheet.Description, callSheet.FollowUpDate.Value.ToString("dd/MM/yyyy"), link);
                }
            }
        }
    }
}
