﻿using Red20.Data.Context;
using Red20.Model.Entity;
using Red20.Service.Data.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Red20.Utility
{
    public static class NumberSequenceUtility
    {
        private const string ENQUIRY_NUMBER_PREFIX = "ES";
        private const string PURCHASEREQUISITION_NUMBER_PREFIX = "PR";
        private const string PURCHASEORDER_NUMBER_PREFIX = "PO";
        private const string ASSET_NUMBER_PREFIX = "AT";
        private const string CUSTOMERCOMPLAINT_NUMBER_PREFIX = "CC";
        private const string SUPPLIERCOMPLAINT_NUMBER_PREFIX = "SC";

        private const int ENQUIRY_NUMBER_PADDING = 6;
        private const int PURCHASEREQUISITION_NUMBER_PADDING = 6;
        private const int PURCHASEORDER_NUMBER_PADDING = 6;
        private const int ASSET_NUMBER_PADDING = 6;
        private const int CUSTOMERCOMPLAINT_NUMBER_PADDING = 6;
        private const int SUPPLIERCOMPLAINT_NUMBER_PADDING = 6;

        private const int ORDER_NUMBER_PADDING = 6;
        private const string OLDMELDRUM_SALE_ORDER_NUMBER_PREFIX = "SS";
        private const string WBA_SALE_ORDER_NUMBER_PREFIX = "WS";
        private const string OLDMELDRUM_HIRE_ORDER_NUMBER_PREFIX = "SH";
        private const string WBA_HIRE_ORDER_NUMBER_PREFIX = "WH";
        private const string OLDMELDRUM_INTERNAL_SALE_ORDER_NUMBER_PREFIX = "SI";
        private const string WBA_INTERNAL_SALE_ORDER_NUMBER_PREFIX = "WI";

        public static async Task<string> GetNextOrderNumber(IUnitOfWork unitOfWork, string location, string type, bool isInternal = false)
        {
            string prefix = "";
            string result = "";

            if (type == "Sale" && isInternal)
            {
                if (location == "West Bromwich")
                {
                    prefix = WBA_INTERNAL_SALE_ORDER_NUMBER_PREFIX;
                }
                else
                {
                    prefix = OLDMELDRUM_INTERNAL_SALE_ORDER_NUMBER_PREFIX;
                }
                result = await GetNextOrderNumberSequence(unitOfWork, prefix, "{0}{1}", ORDER_NUMBER_PADDING, "Order", "Internal");
            }
            else if (type == "Sale")
            {
                if (location == "West Bromwich")
                {
                    prefix = WBA_SALE_ORDER_NUMBER_PREFIX;
                }
                else
                {
                    prefix = OLDMELDRUM_SALE_ORDER_NUMBER_PREFIX;
                }
                result = await GetNextOrderNumberSequence(unitOfWork, prefix, "{0}{1}", ORDER_NUMBER_PADDING, "Order", "Sale");
            }
            else if (type == "Hire")
            {
                if (location == "West Bromwich")
                {
                    prefix = WBA_HIRE_ORDER_NUMBER_PREFIX;
                }
                else
                {
                    prefix = OLDMELDRUM_HIRE_ORDER_NUMBER_PREFIX;
                }
                result = await GetNextOrderNumberSequence(unitOfWork, prefix, "{0}{1}", ORDER_NUMBER_PADDING, "Order", "Hire");
            }
            return result;
        }

        public static async Task<string> GetNextEnquiryNumber(IUnitOfWork unitOfWork)
        {
            return await GetNextNumberSequence(unitOfWork, ENQUIRY_NUMBER_PREFIX, "{0}{1}", ENQUIRY_NUMBER_PADDING, "Enquiry");
        }

        public static async Task<string> GetNextPurchaseRequisitionNumber(IUnitOfWork unitOfWork)
        {
            return await GetNextNumberSequence(unitOfWork, PURCHASEREQUISITION_NUMBER_PREFIX, "{0}{1}", PURCHASEREQUISITION_NUMBER_PADDING, "Purchase Req");
        }

        public static async Task<string> GetNextPurchaseOrderNumber(IUnitOfWork unitOfWork)
        {
            return await GetNextNumberSequence(unitOfWork, PURCHASEORDER_NUMBER_PREFIX, "{0}{1}", PURCHASEORDER_NUMBER_PADDING, "Purchase Order");
        }

        public static async Task<string> GetNextQuoteNumber(IUnitOfWork unitOfWork, string enquiryNumber)
        {
            return await GetNextNumberSequence(unitOfWork, enquiryNumber, "{0}-{1}", null, "Quote");
        }

        public static async Task<string> GetNextAssetNumber(IUnitOfWork unitOfWork)
        {
            return await GetNextNumberSequence(unitOfWork, ASSET_NUMBER_PREFIX, "{0}{1}", ASSET_NUMBER_PADDING, "Asset");
        }

        public static async Task<string> GetNextCustomerComplaintNumber(IUnitOfWork unitOfWork)
        {
            return await GetNextNumberSequence(unitOfWork, CUSTOMERCOMPLAINT_NUMBER_PREFIX, "{0}{1}", CUSTOMERCOMPLAINT_NUMBER_PADDING, "Customer Complaint");
        }

        public static async Task<string> GetNextSupplierComplaintNumber(IUnitOfWork unitOfWork)
        {
            return await GetNextNumberSequence(unitOfWork, SUPPLIERCOMPLAINT_NUMBER_PREFIX, "{0}{1}", SUPPLIERCOMPLAINT_NUMBER_PADDING, "Supplier Complaint");
        }

        public static async Task<string> GetNextRevisedQuoteNumber(IUnitOfWork unitOfWork, string enquiryNumber, string quoteNumber, Guid originalQuoteId)
        {
            return await GetNextRevisedNumberSequence(unitOfWork, enquiryNumber, quoteNumber, "{0}-{1}{2}", originalQuoteId);
        }

        private static async Task<string> GetNextNumberSequence(IUnitOfWork unitOfWork, string name, string format, int? padding = null, string type = null)
        {
            var numberSequence = unitOfWork.NumberSequence
                .Query(n => n.Name == name && string.IsNullOrWhiteSpace(n.Letter) && n.Type == type).OrderByDescending(o => o.Number).FirstOrDefault();

            if (numberSequence == null)
            {
                numberSequence = new NumberSequence
                {
                    Name = name,
                    Number = 1,
                    Type = type
                };

                await unitOfWork.NumberSequence.CreateAsync(numberSequence);

            }
            else
            {
                numberSequence.Number = numberSequence.Number + 1;
            }

            return padding != null ? string.Format(format, name, numberSequence.Number.ToString().PadLeft(padding.Value, '0')) :
               string.Format(format, name, numberSequence.Number.ToString());
        }

        private static async Task<string> GetNextOrderNumberSequence(IUnitOfWork unitOfWork, string name, string format, int? padding = null, string type = null, string orderType = null)
        {
            var numberSequence = unitOfWork.NumberSequence
                                    .Query(n => n.Type == "Order" && string.IsNullOrWhiteSpace(n.Letter) && n.Type == type && n.OrderType == orderType)
                                    .OrderByDescending(o => o.Number)
                                    .FirstOrDefault();

            if (numberSequence == null)
            {
                numberSequence = new NumberSequence
                {
                    Name = name,
                    Number = 1,
                    Type = type,
                    OrderType = orderType
                };

                await unitOfWork.NumberSequence.CreateAsync(numberSequence);

            }
            else
            {
                numberSequence.Number = numberSequence.Number + 1;
            }

            return padding != null ? string.Format(format, name, numberSequence.Number.ToString().PadLeft(padding.Value, '0')) :
               string.Format(format, name, numberSequence.Number.ToString());
        }

        private static async Task<string> GetNextRevisedNumberSequence(
            IUnitOfWork unitOfWork,
            string name,
            string quoteNumber,
            string format,
            Guid originalQuoteId)
        {

            string letter = "";
            string[] splitQuoteNumber = quoteNumber.Contains("-") ? quoteNumber.Split("-") : new string[] { };

            bool parentIsRevision = splitQuoteNumber[1].Any(x => char.IsLetter(x));

            var count = splitQuoteNumber[1].Length;
            //var newRevisionLetter = splitQuoteNumber[1][count-1].ToString();

            var newSplitString = "";

            int number = 0;
            if (splitQuoteNumber != null && splitQuoteNumber.Any())
            {
                if (parentIsRevision)
                {
                    newSplitString = splitQuoteNumber[1].Remove(count - 1);
                }
                number = parentIsRevision ? Int32.Parse(newSplitString) : Int32.Parse(splitQuoteNumber[1]);
            }

            string originalQuoteNumber = "";

            if (parentIsRevision)
            {
                var splitNumber = splitQuoteNumber[0] + "-" + splitQuoteNumber[1].Remove(count - 1);
                originalQuoteNumber = splitNumber;
            }
            else
            {
                originalQuoteNumber = quoteNumber;
            }

            int revisionsCount = unitOfWork.Quote.Query(q => q.Number.Contains(originalQuoteNumber)).Count() - 1;
            string currentMaxLetter = revisionsCount > 0 ? Number2String(revisionsCount, true) : string.Empty;

            NumberSequence numberSequence;
            if (!string.IsNullOrEmpty(currentMaxLetter))
            {
                numberSequence = await unitOfWork.NumberSequence
                    .SingleOrDefaultAsync(n => n.Name == name &&
                                               n.Number == number &&
                                               !string.IsNullOrWhiteSpace(n.Letter) &&
                                               n.Letter == currentMaxLetter &&
                                               n.Type == "QuoteRevision");
            }
            else
            {
                numberSequence = await unitOfWork.NumberSequence
                    .SingleOrDefaultAsync(n => n.Name == name && (n.Number > number || n.Number == number) && string.IsNullOrWhiteSpace(n.Letter) &&
                                               n.Type == "QuoteRevision");
            }

            if (numberSequence != null)
            {
                bool hasLetter = !string.IsNullOrWhiteSpace(numberSequence.Letter);

                var newNumberSequence = new NumberSequence();

                if (hasLetter)
                {
                    newNumberSequence.Name = name;
                    newNumberSequence.Number = number;
                    newNumberSequence.Type = "QuoteRevision";
                    char[] character = numberSequence.Letter.ToCharArray();
                    if (character != null && character.Any())
                    {
                        character[0]++;
                        newNumberSequence.Letter = character[0].ToString();
                    }
                    letter = newNumberSequence.Letter;
                    await unitOfWork.NumberSequence.CreateAsync(newNumberSequence);
                }
                else
                {
                    await unitOfWork.NumberSequence.CreateAsync(new NumberSequence
                    {
                        Name = name,
                        Number = number,
                        Letter = "A",
                        Type = "QuoteRevision"
                    });
                    letter = "A";
                }
            }
            else
            {
                await unitOfWork.NumberSequence.CreateAsync(new NumberSequence
                {
                    Name = name,
                    Number = number,
                    Letter = "A",
                    Type = "QuoteRevision"
                });
                letter = "A";
            }

            return string.Format(format, name, number, letter);
        }

        private static String Number2String(int number, bool isCaps)
        {
            Char c = (Char)((isCaps ? 65 : 97) + (number - 1));
            return c.ToString();
        }
    }
}
