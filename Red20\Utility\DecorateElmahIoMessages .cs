﻿using Elmah.Io.Extensions.Logging;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Red20.Utility {
    public class DecorateElmahIoMessages : IConfigureOptions<ElmahIoProviderOptions> {
        private readonly IHttpContextAccessor httpContextAccessor;

        public DecorateElmahIoMessages(IHttpContextAccessor httpContextAccessor) {
            this.httpContextAccessor = httpContextAccessor;
        }

        public void Configure(ElmahIoProviderOptions options) {
            options.OnMessage = msg =>
            {
                var context = httpContextAccessor.HttpContext;
                if (context == null) return;
                msg.User = context.User?.Identity?.Name;
            };
        }
    }
}
