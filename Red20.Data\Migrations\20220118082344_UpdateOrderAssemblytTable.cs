﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Red20.Data.Migrations
{
    public partial class UpdateOrderAssemblytTable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "OrderAssemblyId",
                table: "OrderCreditNoteItems",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "FullCredit",
                table: "OrderAssemblies",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "HasCreditNote",
                table: "OrderAssemblies",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "PartCredit",
                table: "OrderAssemblies",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<int>(
                name: "TotalCredited",
                table: "OrderAssemblies",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateIndex(
                name: "IX_OrderCreditNoteItems_OrderAssemblyId",
                table: "OrderCreditNoteItems",
                column: "OrderAssemblyId");

            migrationBuilder.AddForeignKey(
                name: "FK_OrderCreditNoteItems_OrderAssemblies_OrderAssemblyId",
                table: "OrderCreditNoteItems",
                column: "OrderAssemblyId",
                principalTable: "OrderAssemblies",
                principalColumn: "OrderAssemblyId",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_OrderCreditNoteItems_OrderAssemblies_OrderAssemblyId",
                table: "OrderCreditNoteItems");

            migrationBuilder.DropIndex(
                name: "IX_OrderCreditNoteItems_OrderAssemblyId",
                table: "OrderCreditNoteItems");

            migrationBuilder.DropColumn(
                name: "OrderAssemblyId",
                table: "OrderCreditNoteItems");

            migrationBuilder.DropColumn(
                name: "FullCredit",
                table: "OrderAssemblies");

            migrationBuilder.DropColumn(
                name: "HasCreditNote",
                table: "OrderAssemblies");

            migrationBuilder.DropColumn(
                name: "PartCredit",
                table: "OrderAssemblies");

            migrationBuilder.DropColumn(
                name: "TotalCredited",
                table: "OrderAssemblies");
        }
    }
}
