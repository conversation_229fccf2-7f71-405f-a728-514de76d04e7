﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Red20.Model.Data;
using Red20.Model.Entity;
using Red20.Service.Data.Interface;

namespace Red20.Controllers {
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class PredefinedItemController : ControllerBase {

        private IPredefinedItemService predefinedItemService;
        private ILogger<AuthController> logger;

        public PredefinedItemController(
            IPredefinedItemService predefinedItemService,
            IUnitOfWork unitOfWork,
            ILogger<AuthController> logger) {

            this.predefinedItemService = predefinedItemService;
            this.logger = logger;
        }

        [HttpGet]
        [ProducesResponseType(200, Type = typeof(IList<PredefinedItemModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get() {
            var predefinedItems = predefinedItemService.GetAllPredefinedItems();
            return Ok(predefinedItems);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(PredefinedItemModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id) {

            var predefinedItem = await predefinedItemService.GetAsync(id);

            return Ok(predefinedItem);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(PredefinedItemModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody]PredefinedItemUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }

            var predefinedItem = await predefinedItemService.PostAsync(model);

            return Ok(predefinedItem);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(PredefinedItemModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Put(Guid id, [FromBody]PredefinedItemUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }

            var predefinedItem = await predefinedItemService.GetAsync(id);

            if (predefinedItem is null) {
                return BadRequest();
            }

            predefinedItem = await predefinedItemService.PutAsync(id, model);

            return Ok(predefinedItem);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Delete(Guid id) {

            await predefinedItemService.DeleteAsync(id);
            return Ok();
        }
    }
}