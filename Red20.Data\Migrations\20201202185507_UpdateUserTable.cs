﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Red20.Data.Migrations
{
    public partial class UpdateUserTable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "AccountName",
                table: "Users",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "AccountNumber",
                table: "Users",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "Address1",
                table: "Users",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Address2",
                table: "Users",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "BankName",
                table: "Users",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "Basic",
                table: "Users",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "BranchName",
                table: "Users",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "City",
                table: "Users",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Country",
                table: "Users",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DateofBirth",
                table: "Users",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "Department",
                table: "Users",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "EmployeeCode",
                table: "Users",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "EndDate",
                table: "Users",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Gender",
                table: "Users",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "Leaver",
                table: "Users",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "MartialStatus",
                table: "Users",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "NINumber",
                table: "Users",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "OffshoreRateWeekday",
                table: "Users",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "OffshoreRateWeekend",
                table: "Users",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "Overtime1",
                table: "Users",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "Overtime2",
                table: "Users",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PayMethod",
                table: "Users",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Postcode",
                table: "Users",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ResourceCode",
                table: "Users",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Salaried",
                table: "Users",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "SortCode",
                table: "Users",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<DateTime>(
                name: "StartDate",
                table: "Users",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "Town",
                table: "Users",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "UserPayrollRates",
                columns: table => new
                {
                    UserPayrollRateId = table.Column<Guid>(nullable: false),
                    UserId = table.Column<Guid>(nullable: false),
                    PayType = table.Column<string>(nullable: true),
                    Hours = table.Column<double>(nullable: true),
                    OriginalRate = table.Column<double>(nullable: false),
                    AmendedRate = table.Column<double>(nullable: true),
                    Comments = table.Column<string>(nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserPayrollRates", x => x.UserPayrollRateId);
                    table.ForeignKey(
                        name: "FK_UserPayrollRates_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "UserId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_UserPayrollRates_UserId",
                table: "UserPayrollRates",
                column: "UserId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "UserPayrollRates");

            migrationBuilder.DropColumn(
                name: "AccountName",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "AccountNumber",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "Address1",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "Address2",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "BankName",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "Basic",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "BranchName",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "City",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "Country",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "DateofBirth",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "Department",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "EmployeeCode",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "EndDate",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "Gender",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "Leaver",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "MartialStatus",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "NINumber",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "OffshoreRateWeekday",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "OffshoreRateWeekend",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "Overtime1",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "Overtime2",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "PayMethod",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "Postcode",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "ResourceCode",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "Salaried",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "SortCode",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "StartDate",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "Town",
                table: "Users");
        }
    }
}
