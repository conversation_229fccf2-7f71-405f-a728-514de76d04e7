﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Red20.Data.Migrations
{
    public partial class UserCheckInLastRecordedDates : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "LastCheckedIn",
                table: "UserCheckIns",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "LastCheckedOut",
                table: "UserCheckIns",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "LastTotalHours",
                table: "UserCheckIns",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "LastCheckedIn",
                table: "UserCheckIns");

            migrationBuilder.DropColumn(
                name: "LastCheckedOut",
                table: "UserCheckIns");

            migrationBuilder.DropColumn(
                name: "LastTotalHours",
                table: "UserCheckIns");
        }
    }
}
