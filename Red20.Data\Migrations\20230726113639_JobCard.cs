﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Red20.Data.Migrations
{
    public partial class JobCard : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "JobDate",
                table: "HireEquipments",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "JobNumber",
                table: "HireEquipments",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Location",
                table: "HireEquipments",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Type",
                table: "HireEquipments",
                type: "nvarchar(max)",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "<PERSON>Date",
                table: "HireEquipments");

            migrationBuilder.DropColumn(
                name: "JobNumber",
                table: "HireEquipments");

            migrationBuilder.DropColumn(
                name: "Location",
                table: "HireEquipments");

            migrationBuilder.DropColumn(
                name: "Type",
                table: "HireEquipments");
        }
    }
}
