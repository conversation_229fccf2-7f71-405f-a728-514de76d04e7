﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Red20.Model.Data.Order;
using Red20.Service.Data.Interface;

namespace Red20.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class EquipmentServiceHistoryController : ControllerBase
    {
        private IEquipmentServiceHistoryService service;
        private IEquipmentServiceStockItemService itemsService;
        private IStockService stockService;
        private IHireEquipmentService equipmentService;
        private IOrderService orderService;
        private IOrderAssemblyService orderAssemblyService;
        private IOrderAssemblyHireEquipmentService orderHireEquipmentService;
        private ILogger<AuthController> logger;

        public EquipmentServiceHistoryController(
            IEquipmentServiceHistoryService service,
            IEquipmentServiceStockItemService itemsService,
            IStockService stockService,
            IHireEquipmentService equipmentService,
            IOrderService orderService,
            IOrderAssemblyService orderAssemblyService,
            IOrderAssemblyHireEquipmentService orderHireEquipmentService,
            ILogger<AuthController> logger) {
            this.service = service;
            this.itemsService = itemsService;
            this.stockService = stockService;
            this.equipmentService = equipmentService;
            this.orderService = orderService;
            this.orderAssemblyService = orderAssemblyService;
            this.orderHireEquipmentService = orderHireEquipmentService;
            this.logger = logger;
        }

        [HttpGet("byOrderAndAssembly/{id}/{orderId}")]
        [ProducesResponseType(200, Type = typeof(IList<EquipmentServiceHistoryModel>))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id, Guid orderId) {
            return Ok(await service.GetByOrderAssemblyAndOrderAsync(id, orderId));
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(EquipmentServiceHistoryModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody]EquipmentServiceHistoryUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }

            if (model.InspectedDate.HasValue) {
                model.InspectedDate = model.InspectedDate.Value.AddHours(2);
            }

            if (model.SetHireEquipmentAvailable) {
                var update = await this.SetHireEquipmentAvailable(model.HireEquipmentId, model.OrderAssemblyHireEquipmentId);
                if (!update) {
                    return BadRequest();
                }
            }

            try {
                var equipmentServiceHistory = await service.PostAsync(model);
                return Ok(equipmentServiceHistory);
            } catch (Exception ex) {
                logger.LogError($"Cannot create Equipment Service History Record - {ex}");
                return BadRequest();
            }
        }

        private async Task<bool> SetHireEquipmentAvailable(Guid hireEquipmentId, Guid orderHireEquipmentId) {
            var hireEquipment = await equipmentService.GetAsync(hireEquipmentId);
            if(hireEquipment == null) {
                return false;
            }

            var orderHireEquipment = await orderHireEquipmentService.GetAsync(orderHireEquipmentId);
            if(orderHireEquipment == null) {
                return false;
            }

            try {
                orderHireEquipment.Status = "Unlinked";
                await orderHireEquipmentService.PutAsync(orderHireEquipment);

                hireEquipment.Status = "Available";
                hireEquipment.OrderNumber = null;
                await equipmentService.PutAsync(hireEquipment.HireEquipmentId, hireEquipment);
                return true;
            } catch (Exception ex) {
                logger.LogError($"Cannot update status for Hire Equipment - {ex}");
                return false;
            }
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(EquipmentServiceHistoryModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Put(Guid id, [FromBody]EquipmentServiceHistoryUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }

            var serviceHistory = await service.GetAsync(id);
            if(serviceHistory == null) {
                return BadRequest();
            }

            if (model.SetHireEquipmentAvailable && serviceHistory.SetHireEquipmentAvailable is false) {
                var update = await this.SetHireEquipmentAvailable(model.HireEquipmentId, model.OrderAssemblyHireEquipmentId);
                if (!update) {
                    return BadRequest();
                }
            }

            try {
                var equipmentServiceHistory = await service.PutAsync(id, model);
                return Ok(equipmentServiceHistory);
            } catch (Exception ex) {
                logger.LogError($"Cannot update Equipment Service History Record - {ex}");
                return BadRequest();
            }
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Delete(Guid id) {
            if(!await service.EquipmentServiceHistoryIdExistsAsync(id)) {
                return BadRequest();
            }
            await service.DeleteAsync(id);
            return Ok();
        }
    }
}