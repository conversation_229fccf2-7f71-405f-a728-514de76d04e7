﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace Red20.Data.Migrations
{
    public partial class AddHireStartAndHireEndDatesToInvoiceItemTable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "HireEndDateString",
                table: "JobInvoiceItem",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "HireStartDateString",
                table: "JobInvoiceItem",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "HireEndDateString",
                table: "JobInvoiceItem");

            migrationBuilder.DropColumn(
                name: "HireStartDateString",
                table: "JobInvoiceItem");
        }
    }
}
