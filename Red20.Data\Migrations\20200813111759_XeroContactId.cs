﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Red20.Data.Migrations
{
    public partial class XeroContactId : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "XeroSupplierId",
                table: "Suppliers");

            migrationBuilder.DropColumn(
                name: "XeroCustomerId",
                table: "Customers");

            migrationBuilder.AddColumn<Guid>(
                name: "XeroContactId",
                table: "Suppliers",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "XeroContactId",
                table: "Customers",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "XeroContactId",
                table: "Suppliers");

            migrationBuilder.DropColumn(
                name: "XeroContactId",
                table: "Customers");

            migrationBuilder.AddColumn<Guid>(
                name: "XeroSupplierId",
                table: "Suppliers",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "XeroCustomerId",
                table: "Customers",
                type: "uniqueidentifier",
                nullable: true);
        }
    }
}
