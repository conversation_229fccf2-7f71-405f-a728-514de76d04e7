﻿using Microsoft.EntityFrameworkCore;
using Red20.Model.Entity;

namespace Red20.Data.Context
{
    public class DataContext : DbContext
    {

        public DataContext(DbContextOptions<DataContext> options) : base(options)
        {
        }

        public DataContext() { }

        public DbSet<Customer> Customers { get; set; }
        public DbSet<CustomerCallSheet> CustomerCallSheets { get; set; }
        public DbSet<CustomerCallSheetVisit> CustomerCallSheetVisits { get; set; }
        public DbSet<User> Users { get; set; }
        public DbSet<UserCheckIn> UserCheckIns { get; set; }
        public DbSet<UserCheckInCheckOut> UserCheckInCheckOuts { get; set; }
        public DbSet<UserPayrollRate> UserPayrollRates { get; set; }
        public DbSet<UserRed20Rate> UserRed20Rates { get; set; }
        public DbSet<UserToken> UserTokens { get; set; }
        public DbSet<UserTimesheet> UserTimesheets { get; set; }
        public DbSet<UserTimesheetEntry> UserTimesheetEntries { get; set; }
        public DbSet<UserPayrollAdjustment> UserPayrollAdjustments { get; set; }
        public DbSet<UserPayrollAdjustmentItem> UserPayrollAdjustmentItems { get; set; }
        public DbSet<Supplier> Suppliers { get; set; }

        public DbSet<SupplierActionLog> SupplierActionLogs { get; set; }
        public DbSet<AnalysisCode> AnalysisCodes { get; set; }
        public DbSet<Category> Categories { get; set; }
        public DbSet<PredefinedItem> PredefinedItems { get; set; }
        public DbSet<PredefinedItemStock> PredefinedItemStocks { get; set; }
        public DbSet<Stock> Stocks { get; set; }
        public DbSet<StockFifo> StockFifos { get; set; }
        public DbSet<StockSerialTracking> StockSerialTrackings { get; set; }
        public DbSet<StockSerialTrackingLog> StockSerialTrackingLogs { get; set; }
        public DbSet<StockAssembly> StockAssemblies { get; set; }
        public DbSet<StockTransaction> StockTransactions { get; set; }
        public DbSet<StockAllocation> StockAllocations { get; set; }
        public DbSet<StockMovement> StockMovements { get; set; }
        public DbSet<StockMovementItem> StockMovementItems { get; set; }
        public DbSet<StockAssemblyMovement> StockAssemblyMovements { get; set; }
        public DbSet<StockAssemblyMovementItem> StockAssemblyMovementItems { get; set; }
        public DbSet<StockTake> StockTakes { get; set; }
        public DbSet<Document> Documents { get; set; }
        public DbSet<Enquiry> Enquiries { get; set; }
        public DbSet<Quote> Quotes { get; set; }
        public DbSet<PurchaseRequisition> PurchaseRequisitions { get; set; }
        public DbSet<PurchaseRequisitionItem> PurchaseRequisitionItems { get; set; }
        public DbSet<HireEquipment> HireEquipments { get; set; }
        public DbSet<HireEquipmentJobCard> HireEquipmentJobCards { get; set; }
        public DbSet<Order> Orders { get; set; }
        public DbSet<OrderItem> OrderItems { get; set; }
        public DbSet<OrderCreditNoteItem> OrderCreditNoteItems { get; set; }
        public DbSet<OrderItemJobRecordSheet> OrderItemJobRecordSheets { get; set; }
        public DbSet<OrderItemWorkSiteSheet> OrderItemWorkSiteSheets { get; set; }
        public DbSet<OrderItemStockItem> OrderItemStockItems { get; set; }
        public DbSet<OrderAssembly> OrderAssemblies { get; set; }
        public DbSet<OrderAssemblyHirePreparationSheet> OrderAssemblyHirePreparationSheets { get; set; }

        public DbSet<PurchaseOrder> PurchaseOrders { get; set; }
        public DbSet<PurchaseOrderItem> PurchaseOrderItems { get; set; }
        public DbSet<PurchaseOrderCreditNoteItem> PurchaseOrderCreditNoteItems { get; set; }
        public DbSet<PurchaseOrderItemLog> PurchaseOrderItemLogs { get; set; }
        public DbSet<PurchaseOrderItemSerialNumber> PurchaseOrderItemSerialNumbers { get; set; }
        public DbSet<OrderAssemblyHireEquipment> OrderAssemblyHireEquipments { get; set; }

        public DbSet<NumberSequence> NumberSequences { get; set; }

        public DbSet<EquipmentServiceHistory> EquipmentServiceHistories { get; set; }
        public DbSet<EquipmentServiceStockItem> EquipmentServiceStockItems { get; set; }
        public DbSet<Job> Jobs { get; set; }
        public DbSet<JobInvoice> JobInvoices { get; set; }
        public DbSet<Asset> Assets { get; set; }
        public DbSet<AssetCategory> AssetCategories { get; set; }
        public DbSet<AssetSubCategory> AssetSubCategories { get; set; }
        public DbSet<OrderStockAllocation> OrderStockAllocations { get; set; }
        public DbSet<CustomerComplaint> CustomerComplaints { get; set; }
        public DbSet<CurrencyRate> CurrencyRates { get; set; }
        public DbSet<AccountCode> AccountCodes { get; set; }
        public DbSet<AssetType> AssetTypes { get; set; }
        public DbSet<OrderAssemblyDayRate> OrderAssemblyDayRates { get; set; }
        public DbSet<XeroApiException> XeroApiExceptions { get; set; }


        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Stock>().Property<string>("_types").HasField("_types");
            modelBuilder.Entity<StockMovementItem>().Property<string>("_serialNumbers").HasField("_serialNumbers");
            modelBuilder.Entity<StockAssemblyMovementItem>().Property<string>("_serialNumbers").HasField("_serialNumbers");
            modelBuilder.Entity<User>().Property<string>("_roles").HasField("_roles");
            modelBuilder.Entity<Customer>().HasIndex(p => p.Name);
            modelBuilder.Entity<Supplier>().HasIndex(p => p.Name);
            modelBuilder.Entity<Document>().HasIndex(p => p.CompanyId);

            modelBuilder.Entity<Enquiry>().HasIndex(p => p.Number);
            modelBuilder.Entity<Enquiry>().HasIndex(p => p.EnquiryId);
            modelBuilder.Entity<Enquiry>().HasIndex(p => p.CustomerAddressId);
            modelBuilder.Entity<Enquiry>().HasIndex(p => p.CustomerContactId);

            modelBuilder.Entity<Quote>().HasIndex(p => p.Number);
            modelBuilder.Entity<Quote>().HasIndex(p => p.EnquiryId);
            modelBuilder.Entity<Quote>().HasIndex(p => p.CustomerAddressId);
            modelBuilder.Entity<Quote>().HasIndex(p => p.CustomerContactId);

            modelBuilder.Entity<QuoteItem>().HasIndex(qi => qi.Description);

            modelBuilder.Entity<Order>().HasIndex(p => p.Number);
            modelBuilder.Entity<Order>().HasIndex(p => p.EnquiryId);
            modelBuilder.Entity<Order>().HasIndex(p => p.CustomerShippingAddressId);
            modelBuilder.Entity<Order>().HasIndex(p => p.CustomerInvoiceAddressId);
            modelBuilder.Entity<Order>().HasIndex(p => p.CustomerContactId);

            modelBuilder.Entity<PurchaseRequisition>().HasIndex(p => p.Number);
            modelBuilder.Entity<PurchaseRequisition>().HasIndex(p => p.SupplierId);

            modelBuilder.Entity<PurchaseOrderItemLog>()
                .HasIndex(p => p.PurchaseOrderItemId);

            modelBuilder.Entity<User>().HasIndex(p => p.EmailAddress);
            modelBuilder.Entity<User>().HasIndex(p => p.Firstname);
            modelBuilder.Entity<User>().HasIndex(p => p.Lastname);

            modelBuilder.Entity<UserToken>().HasIndex(p => p.UserId);
            modelBuilder.Entity<UserToken>().HasIndex(p => p.Token);
            modelBuilder.Entity<UserToken>().HasIndex(p => p.Expires);

            modelBuilder.Entity<Job>().HasIndex(p => p.JobId);
            modelBuilder.Entity<JobInvoiceItem>().HasIndex(p => p.OrderItemId);

            modelBuilder.Entity<HireEquipment>().HasIndex(p => p.HireEquipmentId);
            //modelBuilder.Entity<HireEquipment>().HasQueryFilter(c => c.Status != "Sold / Scrapped");

            modelBuilder.Entity<Asset>().HasIndex(p => p.Number);
        }
    }
}
