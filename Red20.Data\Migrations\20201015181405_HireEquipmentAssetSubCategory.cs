﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Red20.Data.Migrations
{
    public partial class HireEquipmentAssetSubCategory : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "AssetSubCategoryId",
                table: "HireEquipments",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_HireEquipments_AssetSubCategoryId",
                table: "HireEquipments",
                column: "AssetSubCategoryId");

            migrationBuilder.AddForeignKey(
                name: "FK_HireEquipments_AssetSubCategories_AssetSubCategoryId",
                table: "HireEquipments",
                column: "AssetSubCategoryId",
                principalTable: "AssetSubCategories",
                principalColumn: "AssetSubCategoryId",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_HireEquipments_AssetSubCategories_AssetSubCategoryId",
                table: "HireEquipments");

            migrationBuilder.DropIndex(
                name: "IX_HireEquipments_AssetSubCategoryId",
                table: "HireEquipments");

            migrationBuilder.DropColumn(
                name: "AssetSubCategoryId",
                table: "HireEquipments");
        }
    }
}
