﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Office.Interop.Excel;
using Red20.Excel.Export;
using Red20.Model.Data.Document;
using Red20.Model.Data.Job;
using Red20.Model.Data.Order;
using Red20.Model.Data.Stock;
using Red20.Model.Data.StockMovement;
using Red20.Model.Data.StockSerialTracking;
using Red20.Model.Entity;
using Red20.Service.Data.Interface;
using Red20.Service.Storage.Interface;
using Red20.Service.Xero.Interface;

namespace Red20.Controllers {
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class StockMovementItemController : ControllerBase {
        private static readonly List<string> saleOrderAccountCodes = new List<string> { "1000", "1010", "1030", "1001", "1011", "1032", "1083", "1082", "1008", "1002", "1050", "1003", "1004", "1014", "1005", "1009", "1015" };

        private IStockMovementItemService stockMovementItemService;
        private IStockMovementService stockMovementService;
        private IHireEquipmentService hireEquipmentService;
        private IOrderAssemblyService orderAssemblyService;
        private IOrderService orderService;
        private IEquipmentServiceHistoryService equipmentServiceHistoryService;
        private IEquipmentServiceStockItemService equipmentServiceStockItemService;
        private IOrderAssemblyHireEquipmentService orderAssemblyHireEquipmentService;
        private IStockService stockService;
        private ILogger<AuthController> logger;

        private IJobInvoiceService jobInvoiceService;
        private IJobInvoiceItemService jobInvoiceItemService;
        private IUserService userService;
        private IUnitOfWork unitOfWork;
        private IMapper mapper;
        private IXeroService xeroService;
        private IDocumentService documentService;
        private IStorageService blobStorage;

        public StockMovementItemController(
            IStockMovementItemService stockMovementItemService,
            IStockMovementService stockMovementService,
            ILogger<AuthController> logger,
            IUnitOfWork unitOfWork,
            IMapper mapper,
            IHireEquipmentService hireEquipmentService,
            IOrderAssemblyService orderAssemblyService,
            IOrderService orderService,
            IEquipmentServiceHistoryService equipmentServiceHistoryService,
            IEquipmentServiceStockItemService equipmentServiceStockItemService,
            IOrderAssemblyHireEquipmentService orderAssemblyHireEquipmentService,
            IStockService stockService,
            IUserService userService,
            IJobInvoiceService jobInvoiceService,
            IXeroService xeroService,
            IDocumentService documentService,
            IStorageService blobStorage,
            IJobInvoiceItemService jobInvoiceItemService) {

            this.stockMovementItemService = stockMovementItemService;
            this.stockMovementService = stockMovementService;
            this.hireEquipmentService = hireEquipmentService;
            this.orderAssemblyService = orderAssemblyService;
            this.orderService = orderService;
            this.equipmentServiceHistoryService = equipmentServiceHistoryService;
            this.equipmentServiceStockItemService = equipmentServiceStockItemService;
            this.orderAssemblyHireEquipmentService = orderAssemblyHireEquipmentService;
            this.stockService = stockService;
            this.logger = logger;
            this.unitOfWork = unitOfWork;
            this.userService = userService;
            this.jobInvoiceService = jobInvoiceService;
            this.jobInvoiceItemService = jobInvoiceItemService;
            this.mapper = mapper;
            this.xeroService = xeroService;
            this.documentService = documentService;
            this.blobStorage = blobStorage;
        }

        [HttpGet]
        [ProducesResponseType(200, Type = typeof(List<StockMovementItemModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get() {
            var stockMovementItems = await stockMovementItemService.GetAsync();
            return Ok(stockMovementItems);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(StockMovementItemModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id) {
            var stockMovementItem = await stockMovementItemService.GetAsync(id);
            return Ok(stockMovementItem);
        }

        [HttpGet("getAveragePrice/{id}/{quantity}")]
        [ProducesResponseType(200, Type = typeof(double))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetStockAveragePrice(Guid id, double quantity) {
            var stock = await stockService.GetAsync(id);
            var costPrice = await stockMovementItemService.GetStockPrice(stock.CostPrice.Value, quantity, id);
            return Ok(costPrice);
        }

        [HttpGet("byStockItems")]
        [ProducesResponseType(200, Type = typeof(StockMovementItemModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> StockMovementItems() {
            var stockItems = new List<HireStockMonthlyReportModel>();
            var stockMovementItems = await unitOfWork.StockMovementItem.Query().Include(c => c.Stock).Include(c => c.Order).ThenInclude(c => c.Customer).Where(c => c.Order.Number.Contains("SH") || c.Order.Number.Contains("WH")).ToListAsync();
            var accountCodes = await xeroService.GetAccountsAsync();
            var requiredAccountCodes = accountCodes.Where(w => saleOrderAccountCodes.Contains(w.Code)).ToList();

            foreach (var stockMovementItem in stockMovementItems) {
                var accountCode = accountCodes.Where(c => c.Code == stockMovementItem.Order.AccountCode).FirstOrDefault();
                var accountCodeNumber = accountCode != null ? int.Parse(accountCode.Code) : 0;
                var newAccountCode = accountCode != null ? accountCode.Code.StartsWith("1") ? (accountCodeNumber + 1000).ToString() : accountCode.Code : string.Empty;
                var stockModel = new HireStockMonthlyReportModel {
                    OrderNumber = stockMovementItem.Order.Number,
                    JobAccountNumber = accountCode != null ? $"{newAccountCode}-{accountCode.Name}" : stockMovementItem.Order.AccountCode.StartsWith("1") ? (int.Parse(stockMovementItem.Order.AccountCode) + 1000).ToString() : stockMovementItem.Order.AccountCode,
                    StockCode = stockMovementItem.Stock.Code,
                    Quantity = stockMovementItem.Quantity,
                    CostPrice = stockMovementItem.CostPrice,
                    CostPriceString = stockMovementItem.CostPrice.ToString("n2"),
                    NetValue = stockMovementItem.NetValue,
                    NetValueString = stockMovementItem.NetValue.Value.ToString("n2"),
                    Created = stockMovementItem.Created,
                    CreatedString = stockMovementItem.Created.ToString("dd/MM/yyyy"),

                };
                stockItems.Add(stockModel);
            }
            return Ok(stockItems);
        }

        [HttpGet("byStockMovement/{id}")]
        [ProducesResponseType(200, Type = typeof(List<StockMovementItemModel>))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetByStockMovementAsync(Guid id) {
            var stockMovementItems = await stockMovementItemService.GetByStockMovementAsync(id);
            return Ok(stockMovementItems);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(StockMovementItemModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody] StockMovementItemUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);
            var currentUser = $"{user.Firstname} {user.Lastname}";

            try {
                model.NetValue = model.Quantity * model.CostPrice;
                model.CurrentUser = currentUser;
                var stockMovementItem = await stockMovementItemService.PostAsync_New(model);

                if (model.OrderId.HasValue && model.OrderId.Value != Guid.Empty && !string.IsNullOrWhiteSpace(model.StockCode) && model.Quantity > 0) {
                    try {
                        var stockMovementItemModel = stockMovementItem;
                        stockMovementItemModel.OrderAssemblyId = model.OrderAssemblyId;
                        await UpdateJobAndEquipmentServiceHistory(stockMovementItemModel);

                        Guid stockItemInvoiceItemId = await stockMovementItemService.CreateStockItemInvoice(stockMovementItemModel, model.OrderId.Value, user.UserId);
                        stockMovementItem.JobInvoiceItemId = stockItemInvoiceItemId;
                        await stockMovementItemService.PutAsync(stockMovementItemModel.StockMovementItemId, stockMovementItemModel, true);
                    } catch (Exception ex) {
                        logger.LogError($"Error updating Job or Equipment Service History - {ex}");
                        return BadRequest();
                    }
                }

                return Ok(stockMovementItem);
            } catch (Exception ex) {
                logger.LogError($"Cannot create Purchase Order - {ex}");
                return BadRequest();
            }
        }

        private async Task UpdateJobAndEquipmentServiceHistory(StockMovementItemModel stockMovementItem, Guid? oldStockId = null) {
            var order = await orderService.GetAsync(stockMovementItem.OrderId.Value);
            var stockId = oldStockId != null ? oldStockId : stockMovementItem.StockId;

            if (!string.IsNullOrWhiteSpace(stockMovementItem.HireEquipmentSerialNumber)) {
                var hireEquipment = await hireEquipmentService.GetBySerialNumber(stockMovementItem.HireEquipmentSerialNumber);
                var orderAssemblyId = stockMovementItem.OrderAssemblyId.HasValue ? stockMovementItem.OrderAssemblyId : null;
                var orderAssemblyHireEquipment = await orderAssemblyHireEquipmentService.GetBySerialNumberAsync(hireEquipment.SerialNumber, orderAssemblyId);

                if (hireEquipment != null && orderAssemblyHireEquipment != null) {
                    var serviceHistory = await equipmentServiceHistoryService.GetByOrderIdAssemblyIdAndHireEquipmentId(order.OrderId, orderAssemblyHireEquipment.OrderAssemblyId, hireEquipment.HireEquipmentId);

                    if (serviceHistory == null) {
                        if (await orderAssemblyHireEquipmentService.AssemblyEquipmentExists(hireEquipment.SerialNumber)) {

                            var serviceHistoryModel = new EquipmentServiceHistoryUpdateModel();
                            serviceHistoryModel.OrderId = stockMovementItem.OrderId.Value;

                            serviceHistoryModel.OrderAssemblyId = orderAssemblyHireEquipment.OrderAssemblyId;

                            serviceHistoryModel.OrderNumber = order.Number;
                            serviceHistoryModel.HireEquipmentId = hireEquipment.HireEquipmentId;
                            serviceHistoryModel.InspectedBy = null;
                            serviceHistoryModel.InspectedDate = null;
                            serviceHistoryModel.Notes = null;
                            serviceHistoryModel.SetHireEquipmentAvailable = false;
                            serviceHistoryModel.OrderAssemblyHireEquipmentId = orderAssemblyHireEquipment.OrderAssemblyHireEquipmentId;

                            serviceHistory = await equipmentServiceHistoryService.PostAsync(serviceHistoryModel);

                            var serviceHistoryStockItem = new EquipmentServiceStockItemUpdateModel();
                            serviceHistoryStockItem.EquipmentServiceHistoryId = serviceHistory.EquipmentServiceHistoryId;
                            serviceHistoryStockItem.StockId = stockMovementItem.StockId;
                            serviceHistoryStockItem.StockCode = stockMovementItem.StockCode;
                            serviceHistoryStockItem.Quantity = stockMovementItem.Quantity;
                            serviceHistoryStockItem.HireEquipmentId = hireEquipment.HireEquipmentId;
                            serviceHistoryStockItem.OrderAssemblyId = orderAssemblyHireEquipment.OrderAssemblyId;
                            serviceHistoryStockItem.OrderId = order.OrderId;
                            serviceHistoryStockItem.OrderNumber = order.Number;

                            serviceHistoryStockItem = await equipmentServiceStockItemService.PostAsync(serviceHistoryStockItem);
                        }
                    } else {

                        var existingServiceHistoryStockItem = await unitOfWork.EquipmentServiceStockItem
                            .Query(q => q.OrderId == stockMovementItem.OrderId && q.StockId == stockId && q.EquipmentServiceHistoryId == serviceHistory.EquipmentServiceHistoryId)
                            .FirstOrDefaultAsync();

                        if (existingServiceHistoryStockItem == null) {
                            var serviceHistoryStockItem = new EquipmentServiceStockItem();
                            serviceHistoryStockItem.EquipmentServiceHistoryId = serviceHistory.EquipmentServiceHistoryId;
                            serviceHistoryStockItem.StockId = stockMovementItem.StockId;
                            serviceHistoryStockItem.StockCode = stockMovementItem.StockCode;
                            serviceHistoryStockItem.Quantity = (int)stockMovementItem.Quantity;
                            serviceHistoryStockItem.HireEquipmentId = hireEquipment.HireEquipmentId;
                            serviceHistoryStockItem.OrderAssemblyId = orderAssemblyHireEquipment.OrderAssemblyId;
                            serviceHistoryStockItem.OrderId = order.OrderId;
                            serviceHistoryStockItem.OrderNumber = order.Number;

                            serviceHistoryStockItem = await unitOfWork.EquipmentServiceStockItem.CreateAsync(serviceHistoryStockItem);
                            await unitOfWork.SaveChangesAsync();
                        } else {
                            existingServiceHistoryStockItem.Quantity = (int)stockMovementItem.Quantity;
                            existingServiceHistoryStockItem.StockCode = stockMovementItem.StockCode;
                            existingServiceHistoryStockItem.StockId = stockMovementItem.StockId;

                            try {
                                unitOfWork.EquipmentServiceStockItem.Update(existingServiceHistoryStockItem);
                                await unitOfWork.SaveChangesAsync();
                            } catch (Exception ex) {
                                var x = ex;
                            }
                        }
                    }
                }
            }
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(StockMovementItemModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(404)]
        public async Task<IActionResult> Put(Guid id, [FromBody] StockMovementItemUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }

            var existingStockMovementItem = await stockMovementItemService.GetAsync(id);

            var oldStockId = existingStockMovementItem.StockId;
            var stockChanged = existingStockMovementItem.StockId != model.StockId;

            if (existingStockMovementItem == null) {
                return NotFound();
            }

            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);

            var userId = user.UserId;

            bool orderChanged = existingStockMovementItem.OrderId.HasValue && model.OrderId.HasValue && existingStockMovementItem.OrderId.Value != model.OrderId.Value;
            bool orderAdded = !existingStockMovementItem.OrderId.HasValue && model.OrderId.HasValue;

            var stockMovementItem = await stockMovementItemService.PutAsync(id, model, orderIdAdded: orderAdded);

            if (orderChanged) {
                // Delete existing job if different to the one in job costing

                bool deleteInvoice = false;

                if (orderChanged) {
                    if (stockMovementItem.JobInvoiceItemId.HasValue && stockMovementItem.JobInvoiceItemId.ToString() != Guid.Empty.ToString()) {
                        var jobInvoiceItemModel = await jobInvoiceItemService.GetAsync(stockMovementItem.JobInvoiceItemId.Value);
                        if (jobInvoiceItemModel != null) {
                            var jobInvoice = jobInvoiceItemModel.JobInvoiceId.HasValue ? await jobInvoiceService.GetAsync(jobInvoiceItemModel.JobInvoiceId.Value) : null;
                            if (jobInvoice != null) {
                                if (!jobInvoice.JobInvoiceItems.Where(w => w.JobInvoiceItemId != jobInvoiceItemModel.JobInvoiceItemId).Any()) {
                                    deleteInvoice = true;
                                }
                            }
                            if (jobInvoiceItemModel.JobInvoiceItemId.HasValue) {
                                await jobInvoiceItemService.DeleteAsync(jobInvoiceItemModel.JobInvoiceItemId.Value);
                            }

                            if (deleteInvoice) {
                                await jobInvoiceService.DeleteAsync(jobInvoice.JobInvoiceId);
                            }
                        }
                    }

                    if (model.OrderId.HasValue && model.OrderId.Value != Guid.Empty && !string.IsNullOrWhiteSpace(model.StockCode) && model.Quantity > 0) {
                        try {
                            await UpdateJobAndEquipmentServiceHistory(stockMovementItem);
                            Guid stockItemInvoiceItemId = await stockMovementItemService.CreateStockItemInvoice(stockMovementItem, model.OrderId.Value, userId);
                            stockMovementItem.JobInvoiceItemId = stockItemInvoiceItemId;

                            stockMovementItem = await stockMovementItemService.PutAsync(stockMovementItem.StockMovementItemId, stockMovementItem, hasJobInvoiceItemId: true);
                        } catch (Exception ex) {
                            logger.LogError($"Error updating Job or Equipment Service History - {ex}");
                            return BadRequest();
                        }
                    }
                }
            } else if (orderAdded) {
                await UpdateJobAndEquipmentServiceHistory(stockMovementItem);
                Guid stockItemInvoiceItemId = await stockMovementItemService.CreateStockItemInvoice(stockMovementItem, model.OrderId.Value, userId);
                stockMovementItem.JobInvoiceItemId = stockItemInvoiceItemId;
                await stockMovementItemService.PutAsync(stockMovementItem.StockMovementItemId, stockMovementItem, hasJobInvoiceItemId: true);
            } else {
                await UpdateJobAndEquipmentServiceHistory(stockMovementItem, stockChanged ? oldStockId : (Guid?)null);
            }

            return Ok(stockMovementItem);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Delete(Guid id) {
            if (await stockMovementItemService.GetAsync(id) is null) {
                return NotFound();
            }

            bool deleteInvoice = false;
            var stockMovementItemModel = await stockMovementItemService.GetAsync(id);

            var relatedStockFifoRecord = await unitOfWork.StockFifo.Query(q =>
                q.StockId == stockMovementItemModel.StockId &&
                q.StockMovementItemId == stockMovementItemModel.StockMovementItemId)
                .FirstOrDefaultAsync();

            if(relatedStockFifoRecord != null) {
                await unitOfWork.StockFifo.DeleteAsync(relatedStockFifoRecord.StockFifoId);
            }

            if (stockMovementItemModel.OrderId.HasValue) {
                var order = await orderService.GetAsync(stockMovementItemModel.OrderId.Value);

                if (!string.IsNullOrWhiteSpace(stockMovementItemModel.HireEquipmentSerialNumber)) {
                    var hireEquipment = await hireEquipmentService.GetBySerialNumber(stockMovementItemModel.HireEquipmentSerialNumber);
                    var orderAssemblyHireEquipment = await orderAssemblyHireEquipmentService.GetBySerialNumberAsync(hireEquipment.SerialNumber);

                    if (hireEquipment != null) {
                        var serviceHistory = await equipmentServiceHistoryService.GetByOrderIdAssemblyIdAndHireEquipmentId(order.OrderId, orderAssemblyHireEquipment.OrderAssemblyId, hireEquipment.HireEquipmentId);

                        if (serviceHistory != null) {
                            var existingServiceHistoryStockItem = await equipmentServiceStockItemService.GetByOrderAndStockAsync(order.OrderId, stockMovementItemModel.StockId, serviceHistory.EquipmentServiceHistoryId);
                            if(existingServiceHistoryStockItem != null) {
                                await equipmentServiceStockItemService.DeleteAsync(existingServiceHistoryStockItem.EquipmentServiceStockItemId);
                            }
                        }
                    }
                }
            }

            if (stockMovementItemModel.JobInvoiceItemId.HasValue && stockMovementItemModel.JobInvoiceItemId.ToString() != Guid.Empty.ToString()) {
                var jobInvoiceItemModel = await jobInvoiceItemService.GetAsync(stockMovementItemModel.JobInvoiceItemId.Value);
                if (jobInvoiceItemModel != null) {
                    var jobInvoice = jobInvoiceItemModel.JobInvoiceId.HasValue ? await jobInvoiceService.GetAsync(jobInvoiceItemModel.JobInvoiceId.Value) : null;
                    if (jobInvoice != null) {
                        if (!jobInvoice.JobInvoiceItems.Where(w => w.JobInvoiceItemId != jobInvoiceItemModel.JobInvoiceItemId).Any()) {
                            deleteInvoice = true;
                        }
                    }
                    if (jobInvoiceItemModel.JobInvoiceItemId.HasValue) {
                        await jobInvoiceItemService.DeleteAsync(jobInvoiceItemModel.JobInvoiceItemId.Value);
                    }

                    if (deleteInvoice) {
                        await jobInvoiceService.DeleteAsync(jobInvoice.JobInvoiceId);
                    }
                }
            }
            var stock = await stockService.GetAsync(stockMovementItemModel.StockId);

            var stockTransaction = stock.StockTransactions.Where(c => c.StockMovementItemId == stockMovementItemModel.StockMovementItemId).OrderByDescending(c => c.Created).FirstOrDefault();

            if (stockTransaction != null) {
                stock.PhysicalStock = stock.PhysicalStock + (stockTransaction.Status == "IN" ? -stockTransaction.Quantity : stockTransaction.Quantity);

                var stockTransactions = unitOfWork.StockTransaction.Query()
                .Where(c => c.StockId == stockMovementItemModel.StockId && c.Created >= stockMovementItemModel.Created)
                .OrderBy(c => c.Created)
                .ToList();

                foreach(var transaction in stockTransactions)
                {
                    transaction.PreviousStockQuantity = transaction.PreviousStockQuantity +
                                                        (transaction.Status == "IN" ?
                                                            -transaction.Quantity :
                                                            transaction.Quantity);

                    unitOfWork.StockTransaction.Update(transaction);
                }

                await unitOfWork.StockTransaction.DeleteAsync(stockTransaction.StockTransactionId);
            }

            var stockTake = unitOfWork.StockTake
                .Query(q => q.StockMovementItemId.HasValue && q.StockMovementItemId.Value == stockMovementItemModel.StockMovementItemId)
                .FirstOrDefault();

            var stockTakes = unitOfWork.StockTake.Query()
                .Where(w => w.StockId == stockMovementItemModel.StockId && w.Created >= stockMovementItemModel.Created)
                .ToList();

            if (stockTake != null)
            {
                await unitOfWork.StockTake.DeleteAsync(stockTake.StockTakeId);
            }

            foreach (var take in stockTakes)
            {
                take.PhysicalStock =
                    take.PhysicalStock +
                    (stockMovementItemModel.Type == "IN" ? -stockMovementItemModel.Quantity : stockMovementItemModel.Quantity);

                take.Quantity = stockMovementItemModel.Type == "IN" ?
                    take.PhysicalStock + stockMovementItemModel.Quantity :
                    take.PhysicalStock - stockMovementItemModel.Quantity;

                unitOfWork.StockTake.Update(take);
            }

            await unitOfWork.SaveChangesAsync();

            await stockMovementItemService.DeleteAsync(id);
            return Ok();
        }

        [HttpPost("generateHireMonthEndReport")]
        [ProducesResponseType(200)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> GenerateMonthEndReport(List<HireStockMonthlyReportModel> models) {
            try {
                var document = new DocumentModel();
                var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
                if (emailClaim == null)
                {
                    return Unauthorized();
                }

                var user = await userService.GetUserByEmailAsync(emailClaim.Value);
                var currentUser = $"{user.Firstname} {user.Lastname}";
                var itemData = ExportUtility.ExportHireStockMonthEndReport(models, currentUser);
                document = await documentService.PostAsync("Stock_Month_End_Report.xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"{user.Firstname} {user.Lastname}", "Stock Items", null, null, null, false, false, false);
                var file = File(itemData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                await blobStorage.UploadAsync(document.DocumentId, itemData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                return Ok();
            } catch (Exception ex) {
                return NotFound();
            }
        }
        [HttpGet("downloadReport")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> DownloadReport() {
            try {
                var lastDocument = await unitOfWork.Document.Query().Where(t => t.Type == "Stock Items").OrderByDescending(c => c.Created).FirstOrDefaultAsync();
                var download = await blobStorage.DownloadAsync(lastDocument.DocumentId);
                var file = File(download.Item1, download.Item2);
                return file;
            } catch (Exception ex) {
                return NotFound();
            }
        }
    }
}
