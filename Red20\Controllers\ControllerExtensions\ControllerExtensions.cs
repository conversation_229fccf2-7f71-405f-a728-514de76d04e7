﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace Red20.Controllers.ControllerExtensions {
    public static class ControllerExtensions {
        public static byte[] FileToByteArray<T>(this T controller, IFormFile file) where T : ControllerBase {
            MemoryStream stream = new MemoryStream();
            file.CopyTo(stream);
            return stream.ToArray();
        }
    }
}
