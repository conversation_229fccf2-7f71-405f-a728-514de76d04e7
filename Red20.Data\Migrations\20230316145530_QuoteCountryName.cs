﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Red20.Data.Migrations
{
    public partial class QuoteCountryName : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "CompanyCountryName",
                table: "Quotes",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CompanyName",
                table: "Quotes",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ItemDescription",
                table: "Quotes",
                type: "nvarchar(max)",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CompanyCountryName",
                table: "Quotes");

            migrationBuilder.DropColumn(
                name: "CompanyName",
                table: "Quotes");

            migrationBuilder.DropColumn(
                name: "ItemDescription",
                table: "Quotes");
        }
    }
}
