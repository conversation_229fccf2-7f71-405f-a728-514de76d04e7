﻿using Hangfire.Dashboard;
using Red20.Model.Constant;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Red20.Utility {
    public class HangfireAuthorizationFilter : IDashboardAuthorizationFilter {
        public bool Authorize(DashboardContext context) {
            var httpContext = context.GetHttpContext();

            // Allow all user roles to see the Dashboard.
            return true;
        }
    }
}
