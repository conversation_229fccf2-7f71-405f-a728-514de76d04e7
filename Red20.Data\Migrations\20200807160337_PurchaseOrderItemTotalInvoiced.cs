﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace Red20.Data.Migrations
{
    public partial class PurchaseOrderItemTotalInvoiced : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsPartInvoiced",
                table: "PurchaseOrderItems",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<int>(
                name: "TotalInvoiced",
                table: "PurchaseOrderItems",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsPartInvoiced",
                table: "PurchaseOrderItems");

            migrationBuilder.DropColumn(
                name: "TotalInvoiced",
                table: "PurchaseOrderItems");
        }
    }
}
