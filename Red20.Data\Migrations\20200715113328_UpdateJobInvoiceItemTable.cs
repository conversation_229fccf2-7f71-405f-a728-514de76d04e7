﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace Red20.Data.Migrations
{
    public partial class UpdateJobInvoiceItemTable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ItemValue",
                table: "JobInvoiceItem");

            migrationBuilder.AddColumn<string>(
                name: "StockCategory",
                table: "JobInvoiceItem",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "StockCode",
                table: "JobInvoiceItem",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "Value",
                table: "JobInvoiceItem",
                nullable: false,
                defaultValue: 0.0);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "StockCategory",
                table: "JobInvoiceItem");

            migrationBuilder.DropColumn(
                name: "StockCode",
                table: "JobInvoiceItem");

            migrationBuilder.DropColumn(
                name: "Value",
                table: "JobInvoiceItem");

            migrationBuilder.AddColumn<double>(
                name: "ItemValue",
                table: "JobInvoiceItem",
                type: "float",
                nullable: false,
                defaultValue: 0.0);
        }
    }
}
