﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Red20.Excel.Export;
using Red20.Model.Data.Document;
using Red20.Model.Data.PurchaseOrder;
using Red20.Service.Data.Interface;
using Red20.Service.Storage.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Red20.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class PurchaseOrderItemController : ControllerBase
    {

        private IPurchaseOrderService purchaseOrderService;
        private IPurchaseOrderItemService purchaseOrderItemService;
        private IPurchaseOrderItemLogService purchaseOrderItemLogService;
        private IUserService userService;
        private IDocumentService documentService;
        private IStorageService blobStorage;
        private ILogger<AuthController> logger;
        IUnitOfWork unitOfWork;


        public PurchaseOrderItemController(
            IPurchaseOrderService purchaseOrderService,
            IPurchaseOrderItemService purchaseOrderItemService,
            IPurchaseOrderItemLogService purchaseOrderItemLogService,
            IUserService userService,
            IUnitOfWork unitOfWork,
            IDocumentService documentService,
            IStorageService blobStorage,
            ILogger<AuthController> logger)
        {

            this.purchaseOrderService = purchaseOrderService;
            this.purchaseOrderItemService = purchaseOrderItemService;
            this.purchaseOrderItemLogService = purchaseOrderItemLogService;
            this.userService = userService;
            this.logger = logger;
            this.unitOfWork = unitOfWork;
            this.documentService = documentService;
            this.blobStorage = blobStorage;
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(PurchaseOrderItemModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id)
        {
            var purchaseOrderItem = await purchaseOrderItemService.GetByIdAsync(id);
            return Ok(purchaseOrderItem);
        }

        [HttpGet("items")]
        [ProducesResponseType(200, Type = typeof(IList<PurchaseOrderItemModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetPrItems()
        {
            try
            {
                var orders = unitOfWork.PurchaseOrderItem.Query().Include(c => c.PurchaseOrder).Where(c => c.Quantity == c.TotalReceived && c.PurchaseOrder.Status == "Received").GroupBy(c => c.PurchaseOrder.Number).Select(x => x.Key).ToList();
                return Ok(orders);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot create Purchase Order - {ex}");
                return BadRequest();
            }
        }

        [HttpGet("byPurchaseOrder/{id}")]
        [ProducesResponseType(200, Type = typeof(PurchaseOrderItemModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetByPurchaseOrder(Guid id)
        {
            try
            {
                var purchaseOrderItems = await purchaseOrderItemService.GetByPurchaseOrderIdAsync(id);
                return Ok(purchaseOrderItems);
            } catch (Exception ex)
            {
                var x = ex;
                return BadRequest();
            }
        }

        [HttpGet("byNotInvoiced/{id}")]
        [ProducesResponseType(200, Type = typeof(PurchaseOrderItemModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetNotInvoicedItems(Guid id)
        {
            try
            {
                var purchaseOrderItems = await purchaseOrderItemService.GetNotInvoicedItems(id);
                return Ok(purchaseOrderItems);
            } catch (Exception ex)
            {
                var x = ex;
                return BadRequest();
            }
        }

        [HttpGet("purchaseOrder/{id}")]
        [ProducesResponseType(200, Type = typeof(PurchaseOrderItemNewInvoiceModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetItemsByPurchaseOrder(Guid id)
        {
            var purchaseOrderItems = await purchaseOrderItemService.GetItemsByPurchaseOrderIdAsync(id);
            return Ok(purchaseOrderItems);
        }

        [HttpGet("po/{id}")]
        [ProducesResponseType(200, Type = typeof(PurchaseOrderItemReceiveModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetPurchaseOrderItemsByPurchaseOrder(Guid id)
        {
            var purchaseOrderItems = await purchaseOrderItemService.GetPurchaseOrderItemsByPurchaseOrderIdAsync(id);
            return Ok(purchaseOrderItems);
        }

        [HttpGet("received")]
        [ProducesResponseType(200, Type = typeof(PurchaseOrderItemModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAllReceivedItemsAsync()
        {
            var purchaseOrderItems = await purchaseOrderItemService.GetAllReceivedItems();

            return Ok(purchaseOrderItems);
        }

        [HttpGet("export")]
        [ProducesResponseType(200, Type = typeof(PurchaseOrderItemExportModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAllReceivedExportItemsAsync()
        {
            var purchaseOrderItems = await purchaseOrderItemService.GetAllReceivedExportItems();
            return Ok(purchaseOrderItems);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(PurchaseOrderItemModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody] PurchaseOrderItemUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }
            var purchaseOrder = await purchaseOrderService.GetAsync(model.PurchaseOrderId);
            try
            {
                model.DateReceived = null;
                var purchaseOrderItem = await purchaseOrderItemService.PostAsync(model);
                return Ok(purchaseOrderItem);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot create Purchase Order - {ex}");
                return BadRequest();
            }
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(PurchaseOrderItemModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Put(Guid id, [FromBody] PurchaseOrderItemUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }
            var purchaseOrderItem = await purchaseOrderItemService.GetByIdAsync(id);
            if (purchaseOrderItem is null)
            {
                return BadRequest();
            }
            try
            {
                var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
                if (emailClaim == null)
                {
                    return Unauthorized();
                }

                var user = await userService.GetUserByEmailAsync(emailClaim.Value);

                try
                {
                    if (model.TotalReceived != purchaseOrderItem.Quantity && model.IsPartComplete && !model.EditItem)
                    {
                        await purchaseOrderItemLogService.PostAsync(id, user.Name, model.TotalReceived.Value, model.PartReceivedDate.Value);
                    }
                } catch (Exception ex)
                {
                    logger.LogError($"Cannot update Purchase Order Item - {ex}");
                    return BadRequest();
                }

                purchaseOrderItem = await purchaseOrderItemService.PutAsync(id, model);

                return Ok(purchaseOrderItem);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot update Purchase Order Item - {ex}");
                return BadRequest();
            }
        }

        [HttpPut("updateReceivedDate")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> UpdateReceivedDate([FromBody] List<PurchaseOrderItemModel> models)
        {

            try
            {
                var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                      c.Type == ClaimTypes.Email);
                if (emailClaim == null)
                {
                    return Unauthorized();
                }

                var user = await userService.GetUserByEmailAsync(emailClaim.Value);

                foreach (var model in models)
                {
                    var purchaseOrderItem = await purchaseOrderItemService.GetByIdAsync(model.PurchaseOrderItemId);
                    purchaseOrderItem = await purchaseOrderItemService.UpdateReceivedDate(model.PurchaseOrderItemId, model);
                }

            } catch (Exception ex)
            {
                logger.LogError($"Cannot update Purchase Order Item - {ex}");
                return BadRequest();
            }
            return Ok();
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Delete(Guid id)
        {

            await purchaseOrderItemService.DeleteAsync(id);
            return Ok();
        }

        [HttpPost("receiveAll")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> ReceiveAllItems(List<PurchaseOrderItemReceiveModel> models)
        {
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                      c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);

            var currentUser = $"{user.Firstname} {user.Lastname}";
            try
            {
                await purchaseOrderItemService.ReceiveAll(models, currentUser);
                return Ok();
            } catch (Exception ex)
            {
                logger.LogError($"Cannot update Purchase Order Item - {ex}");
                return BadRequest();
            }
        }

        [HttpPost("generateReceivedItemsReport")]
        [ProducesResponseType(200)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> GenerateReceivedItemsReport(List<PurchaseOrderItemModel> models)
        {
            try
            {
                var document = new DocumentModel();
                var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
                if (emailClaim == null)
                {
                    return Unauthorized();
                }

                var user = await userService.GetUserByEmailAsync(emailClaim.Value);
                var currentUser = $"{user.Firstname} {user.Lastname}";
                var itemData = ExportUtility.ExportReceivedItemsReport(models, currentUser);
                document = await documentService.PostAsync("Purchase_Order_Received_Items_Report.xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"{user.Firstname} {user.Lastname}", "GoodsReceived", null, null, null, false, false, false);
                var file = File(itemData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                await blobStorage.UploadAsync(document.DocumentId, itemData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                return Ok();
            } catch (Exception ex)
            {
                return NotFound();
            }
        }
        [HttpGet("receivedItemsReport")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> PrintReceivedItemsReport()
        {
            try
            {
                var lastDocument = await unitOfWork.Document.Query().Where(t => t.Type == "GoodsReceived").OrderByDescending(c => c.Created).FirstOrDefaultAsync();
                var download = await blobStorage.DownloadAsync(lastDocument.DocumentId);
                var file = File(download.Item1, download.Item2);
                return file;
            } catch (Exception ex)
            {
                return NotFound();
            }
        }

        [HttpGet("getStockPrice/{stockId}")]
        [ProducesResponseType(200, Type = typeof(PurchaseOrderItemModel))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetPurchaseStockLastPrice(Guid stockId)
        {
            var purchaseOrderItem = await purchaseOrderItemService.GetLastStockPrice(stockId);
            return Ok(purchaseOrderItem);
        }
    }
}

