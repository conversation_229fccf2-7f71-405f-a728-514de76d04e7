﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Red20.Excel.Export;
using Red20.Model.Data.Document;
using Red20.Model.Data.Job;
using Red20.Service.Data.Interface;
using Red20.Service.Storage.Interface;
using Red20.Service.Xero.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Red20.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class JobInvoiceItemController : ControllerBase
    {
        private static readonly List<string> saleOrderAccountCodes = new List<string> { "1000", "1010", "1030", "1001", "1011", "1032", "1083", "1082", "1008", "1002", "1050", "1003", "1004", "1014", "1005", "1009", "1015" };

        private IUserService userService;
        private IJobInvoiceService jobInvoiceService;
        private IJobInvoiceItemService jobInvoiceItemService;
        private IPurchaseOrderItemService itemService;
        private IJobService jobService;
        private IXeroService xeroService;
        private IDocumentService documentService;
        private IStorageService blobStorage;

        private ILogger<AuthController> logger;
        IUnitOfWork unitOfWork;


        public JobInvoiceItemController(
            IUserService userService,
            IUnitOfWork unitOfWork,
            IJobInvoiceService jobInvoiceService,
            IJobInvoiceItemService jobInvoiceItemService,
            IPurchaseOrderItemService itemService,
            IJobService jobService,
            IXeroService xeroService,
            IDocumentService documentService,
            IStorageService blobStorage,
        ILogger<AuthController> logger)
        {
            this.userService = userService;
            this.jobInvoiceService = jobInvoiceService;
            this.jobInvoiceItemService = jobInvoiceItemService;
            this.logger = logger;
            this.itemService = itemService;
            this.unitOfWork = unitOfWork;
            this.jobService = jobService;
            this.xeroService = xeroService;
            this.documentService = documentService;
            this.blobStorage = blobStorage;
        }

        [HttpGet("stockItems")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetJobInvoicesItemsByStock()
        {
            var stockItems = new List<JobInvoiceItemMonthReportModel>();
            var stockInvoiceItems = await unitOfWork.JobInvoiceItem.Query(c => c.InvoiceType == "STOCK").ToListAsync();
            var accountCodes = await xeroService.GetAccountsAsync();
            var requiredAccountCodes = accountCodes.Where(w => saleOrderAccountCodes.Contains(w.Code)).ToList();

            foreach (var stockInvoiceItem in stockInvoiceItems)
            {
                var accountCode = requiredAccountCodes.Where(c => c.Code == stockInvoiceItem.AccountCode).FirstOrDefault();
                var accountCodeNumber = accountCode != null ? int.Parse(accountCode.Code) : 0;
                var newAccountCode = accountCode != null ? accountCode.Code.StartsWith("1") ? (accountCodeNumber + 1000).ToString() : accountCode.Code : string.Empty;
                var discountValue = stockInvoiceItem.DiscountRate.HasValue ? stockInvoiceItem.Quantity * stockInvoiceItem.UnitPrice * (stockInvoiceItem.DiscountRate.Value / 100) : 0;
                var currencyString = stockInvoiceItem.InvoiceType == "STOCK" ? "£" : !string.IsNullOrWhiteSpace(stockInvoiceItem.Currency) ? stockInvoiceItem.Currency == "GBP" || stockInvoiceItem.Currency == "Pounds Sterling" ? "£" : stockInvoiceItem.Currency == "Euro" ? "€" : "$" : "£";
                var stockModel = new JobInvoiceItemMonthReportModel
                {
                    InvoiceNumber = stockInvoiceItem.InvoiceNumber,
                    JobAccountCode = accountCode != null ? $"{newAccountCode}-{accountCode.Name}" : stockInvoiceItem.AccountCode.StartsWith("1") ? (int.Parse(stockInvoiceItem.AccountCode) + 1000).ToString() : stockInvoiceItem.AccountCode,
                    AccountCode = accountCode != null ? newAccountCode : stockInvoiceItem.AccountCode.StartsWith("1") ? (int.Parse(stockInvoiceItem.AccountCode) + 1000).ToString() : stockInvoiceItem.AccountCode,
                    StockCode = stockInvoiceItem.StockCode,
                    Quantity = stockInvoiceItem.Quantity,
                    UnitPrice = stockInvoiceItem.UnitPrice,
                    UnitPriceString = stockInvoiceItem.UnitPrice.ToString("n2"),
                    TotalNett = (stockInvoiceItem.Quantity * stockInvoiceItem.UnitPrice) - discountValue,
                    TotalNettString = $"{currencyString}{((stockInvoiceItem.Quantity * stockInvoiceItem.UnitPrice) - discountValue).ToString("n2")}",
                    InvoiceDate = stockInvoiceItem.InvoiceDate,
                    InvoiceDateString = stockInvoiceItem.InvoiceDate.ToString("dd/MM/yyyy"),

                };
                stockItems.Add(stockModel);
            }
            return Ok(stockItems);
        }

        [HttpPost("invoiceItems")]
        [ProducesResponseType(200, Type = typeof(JobInvoiceModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody] List<JobInvoiceItemModel> models)
        {
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);

            var orderItem = await itemService.GetByIdAsync(models[0].OrderItemId);

            try
            {
                if (models != null && models.Any())
                {
                    var job = await jobService.GetByOrderIdAsync(orderItem.PurchaseOrderId);

                    if (job == null)
                    {
                        job = await jobService.PostAsync(new JobModel
                        {
                            CreatedBy = user != null ? $"{user.Firstname} {user.Lastname}" : null,
                            OrderId = orderItem.PurchaseOrderId,
                            OrderType = orderItem.Order != null ? orderItem.Order.Type : null,
                            OrderNumber = orderItem.Order != null ? orderItem.Order.Number : null,
                            OrderCustomerName = orderItem.Order != null && orderItem.Order.Customer != null ? orderItem.Order.Customer.Name : null,
                            Description = orderItem.Order != null ? orderItem.Order.FirstItemDescription : null,
                            DateOrderRaised = orderItem.Order != null ? orderItem.Order.Created : (DateTime?)null,
                            Currency = orderItem.Order != null ? orderItem.Order.Currency : null
                        });
                    }

                    var jobInvoice = new JobInvoiceModel();
                    jobInvoice.JobId = job.JobId;
                    jobInvoice.InvoiceNumber = models[0].InvoiceNumber;
                    jobInvoice.CreatedBy = user.Name;
                    jobInvoice.Created = DateTime.UtcNow;
                    jobInvoice.JobType = job.OrderType == "PO" ? "PO" : job.OrderType == "Sale" ? "S" : "H";
                    //jobInvoice.TotalValue = 0.0;
                    jobInvoice.InvoiceDate = models[0].InvoiceDate;

                    jobInvoice = await jobInvoiceService.PostAsync(jobInvoice);

                    await jobInvoiceItemService.PostInvoiceItemsAsync(models, user.Name, jobInvoice, job);
                }

                return Ok();
            } catch (Exception ex)
            {
                logger.LogError($"Cannot create Purchase Order Item Invoice - {ex}");
                return BadRequest();
            }
        }
        [HttpPost("generateMonthEndReport")]
        [ProducesResponseType(200)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> GenerateMonthEndReport(List<JobInvoiceItemMonthReportModel> models)
        {
            try
            {
                var document = new DocumentModel();
                var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
                if (emailClaim == null)
                {
                    return Unauthorized();
                }

                var user = await userService.GetUserByEmailAsync(emailClaim.Value);
                var currentUser = $"{user.Firstname} {user.Lastname}";
                var itemData = ExportUtility.ExportStockMonthEndReport(models, currentUser);
                document = await documentService.PostAsync("Stock_Month_End_Report.xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"{user.Firstname} {user.Lastname}", "Stock Items", null, null, null, false, false, false);
                var file = File(itemData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                await blobStorage.UploadAsync(document.DocumentId, itemData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                return Ok();
            } catch (Exception)
            {
                return NotFound();
            }
        }
        [HttpGet("downloadReport")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> DownloadReport()
        {
            try
            {
                var lastDocument = await unitOfWork.Document.Query().Where(t => t.Type == "Stock Items").OrderByDescending(c => c.Created).FirstOrDefaultAsync();
                var download = await blobStorage.DownloadAsync(lastDocument.DocumentId);
                var file = File(download.Item1, download.Item2);
                return file;
            } catch (Exception)
            {
                return NotFound();
            }
        }
    }
}

