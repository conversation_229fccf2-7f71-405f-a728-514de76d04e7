﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Red20.Data.Migrations
{
    public partial class PurchaseNoteCreditNoteItem : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "PurchaseOrderCreditNoteItems",
                columns: table => new
                {
                    PurchaseOrderCreditNoteItemId = table.Column<Guid>(nullable: false),
                    PurchaseOrderItemId = table.Column<Guid>(nullable: false),
                    Quantity = table.Column<int>(nullable: true),
                    Price = table.Column<double>(nullable: true),
                    Vat = table.Column<double>(nullable: true),
                    VatCode = table.Column<string>(nullable: true),
                    TotalNet = table.Column<double>(nullable: false),
                    TotalGross = table.Column<double>(nullable: false),
                    Description = table.Column<string>(nullable: true),
                    AccountCode = table.Column<string>(nullable: true),
                    Discount = table.Column<double>(nullable: true),
                    Created = table.Column<DateTime>(nullable: false),
                    Modified = table.Column<DateTime>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PurchaseOrderCreditNoteItems", x => x.PurchaseOrderCreditNoteItemId);
                    table.ForeignKey(
                        name: "FK_PurchaseOrderCreditNoteItems_PurchaseOrderItems_PurchaseOrderItemId",
                        column: x => x.PurchaseOrderItemId,
                        principalTable: "PurchaseOrderItems",
                        principalColumn: "PurchaseOrderItemId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_PurchaseOrderCreditNoteItems_PurchaseOrderItemId",
                table: "PurchaseOrderCreditNoteItems",
                column: "PurchaseOrderItemId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "PurchaseOrderCreditNoteItems");
        }
    }
}
