﻿using System.Diagnostics;
using System.Net.Http;
using System.Net;
using System.Security.Claims;
using System.Text.Json;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Red20.Data.Context;
using Red20.Model.Entity;
using Microsoft.EntityFrameworkCore;
using Red20.Service.Security.Interface;

namespace AuthMiddleware
{
    public class AuthenticationMiddleware
    {
        private readonly ILogger<AuthenticationMiddleware> logger;
        private readonly RequestDelegate next;

        public AuthenticationMiddleware(RequestDelegate next, ILogger<AuthenticationMiddleware> logger)
        {
            this.next = next;
            this.logger = logger;
        }

        public async Task InvokeAsync(HttpContext context, DataContext dataContext)
        {
            try
            {
                // Skip authentication for Hangfire dashboard
                if (context.Request.Path.StartsWithSegments("/jobs"))
                {
                    await this.next(context);
                    return;
                }

                var authType = DetermineAuthType(context);
                
                if(authType == "Auth0")
                {
                    // Your existing Auth0 authentication logic
                    if (context != null)
                    {
                        var headerAccessToken = context.Request.Headers["Authorization"].FirstOrDefault();
                        var userEmail = context.Request.Headers["Useremail"].FirstOrDefault();
                        User user = await dataContext.Users.Where(x => x.EmailAddress == userEmail).FirstOrDefaultAsync();

                        var token = headerAccessToken?.StartsWith("Bearer ") == true ? headerAccessToken.Substring("Bearer ".Length).Trim() : null;

                        if (token != null && user != null)
                        {
                            var claims = new List<Claim>
                        {
                            new Claim(ClaimTypes.Email, user.EmailAddress),
                        };

                            var identity = new ClaimsIdentity(claims, token);
                            var principal = new ClaimsPrincipal(identity);

                            context.User = principal;

                            await this.next(context);
                        } else if (token != null && userEmail == null)
                        {
                            context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
                            throw new Exception($"User with Email Address {userEmail} could not be found.");
                        } else
                        {
                            context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
                            throw new Exception("User not Authorised");
                        }
                    }
                } else
                {
                    await this.next(context);
                }
            }
            catch (Exception ex)
            {
                await HandleException(context, ex, ex.Message);
            }
        }
        private string DetermineAuthType(HttpContext context)
        {
            if (context.Request.Headers.ContainsKey("Useremail"))
            {
                return "Auth0";
            } else
            {
                return "Legacy";
            }
        }

        private async Task HandleException(HttpContext context, Exception ex, string message)
        {
            context.Response.ContentType = "application/json";
            context.Response.StatusCode = (int)HttpStatusCode.MethodNotAllowed;

            ProblemDetails internalServerError = new()
            {
                Type = "https://datatracker.ietf.org/doc/html/rfc7231#section-6.6.1",
                Status = (int)HttpStatusCode.MethodNotAllowed,
                Title = "[Critical] Access not allowed",
                Detail = message
            };

            string traceId = Activity.Current?.Id ?? context.TraceIdentifier ?? Guid.NewGuid().ToString();
            internalServerError.Extensions["traceId"] = traceId;

            await HttpResponseWritingExtensions.WriteAsync(context.Response, JsonSerializer.Serialize(internalServerError));

            var userEmail = context.Request.Headers["Useremail"].FirstOrDefault();
            var application = context?.Request.Headers["Application"];

            //log exception to any logging framework
            this.logger.LogCritical($"User with email {userEmail} was refused access to app ${application}. Trace ID: {traceId}. Reason: {ex.Message}");
            return;
        }
    }
}
