﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace Red20.Data.Migrations
{
    public partial class AddOrderNAvigationalPropertyToJobEntity : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_Jobs_OrderId",
                table: "Jobs",
                column: "OrderId");

            migrationBuilder.AddForeignKey(
                name: "FK_Jobs_Orders_OrderId",
                table: "Jobs",
                column: "OrderId",
                principalTable: "Orders",
                principalColumn: "OrderId",
                onDelete: ReferentialAction.NoAction);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Jobs_Orders_OrderId",
                table: "Jobs");

            migrationBuilder.DropIndex(
                name: "IX_Jobs_OrderId",
                table: "Jobs");
        }
    }
}
