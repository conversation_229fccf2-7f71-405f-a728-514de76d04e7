﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Red20.Data.Model;
using Red20.Model.Data;
using Red20.Model.Data.Job;
using Red20.Model.Data.Order;
using Red20.Model.Data.PurchaseOrder;
using Red20.Model.Entity;
using Red20.Service.Data.Interface;

namespace Red20.Controllers {
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class OrderCreditNoteItemController : ControllerBase {

        private IJobService jobService;
        private IJobInvoiceService jobInvoiceService;
        private IJobInvoiceItemService jobInvoiceItemService;
        private IOrderService orderService;
        private IOrderItemService orderItemService;
        private IOrderCreditNoteItemService orderCreditNoteItemService;
        private IUserService userService;
        private ILogger<AuthController> logger;
        IUnitOfWork unitOfWork;
        private IOrderAssemblyService orderAssemblyService;


        public OrderCreditNoteItemController(
            IOrderService orderService,
            IOrderItemService orderItemService,
            IOrderCreditNoteItemService orderCreditNoteItemService,
            IUserService userService,
            IJobService jobService,
            IJobInvoiceService jobInvoiceService,
            IJobInvoiceItemService jobInvoiceItemService,
            IUnitOfWork unitOfWork,
            ILogger<AuthController> logger,
            IOrderAssemblyService orderAssemblyService) {

            this.orderAssemblyService = orderAssemblyService;
            this.orderService = orderService;
            this.orderItemService = orderItemService;
            this.orderCreditNoteItemService = orderCreditNoteItemService;
            this.userService = userService;
            this.logger = logger;
            this.unitOfWork = unitOfWork;
            this.jobService = jobService;
            this.jobInvoiceService = jobInvoiceService;
            this.jobInvoiceItemService = jobInvoiceItemService;
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(OrderCreditNoteItemModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id) {
            var purchaseOrderItem = await orderCreditNoteItemService.GetByIdAsync(id);
            return Ok(purchaseOrderItem);
        }

        [HttpGet("getByOrder/{id}")]
        [ProducesResponseType(200, Type = typeof(OrderCreditNoteNumberModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetNextCreditNoteNumberByOrder(Guid id) {
            var order = await orderService.GetAsync(id);
            var job = await jobService.GetByOrderIdAsync(order.OrderId);
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);

            int creditInvoicesCount = 1;
            if (job != null) {
                creditInvoicesCount = job.JobInvoices != null && job.JobInvoices.Any(job => job.JobType.Contains("CREDIT")) ?
                                           job.JobInvoices.Where(w => w.JobType.Contains("CREDIT")).Count() :
                                           0;
            }

            string nextCreditNoteNumber = $"{order.Number}/CR{creditInvoicesCount + 1}";

            return Ok(new OrderCreditNoteNumberModel { CreditNoteNumber = nextCreditNoteNumber });
        }

        [HttpGet("order/{id}")]
        [ProducesResponseType(200, Type = typeof(OrderCreditNoteItemModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetItemsByOrder(Guid id) {
            var orderCreditNoteItemModel = await orderCreditNoteItemService.GetItemsByOrderIdAsync(id);
            return Ok(orderCreditNoteItemModel);
        }

        [HttpGet("getTotal/{id}")]
        [ProducesResponseType(200, Type = typeof(OrderCreditNoteItemModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetItemsTotal(Guid id) {
            var orderCreditNoteItemModel = await orderCreditNoteItemService.GetItemsByOrderIdAsync(id);
            var total = orderCreditNoteItemModel != null ? orderCreditNoteItemModel.Sum(c => c.TotalValue) : 0;
            return Ok(total);
        }

        [HttpGet("getDeliveryTotal/{id}")]
        [ProducesResponseType(200, Type = typeof(OrderCreditNoteItemModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetDeliveryItemsTotal(Guid id) {
            var orderCreditNoteItemModel = await orderCreditNoteItemService.GetItemsByOrderIdAsync(id);
            var total = orderCreditNoteItemModel != null ? orderCreditNoteItemModel.Where(x => x.IsCarriage).Sum(c => c.TotalValue) : 0;
            return Ok(total);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(OrderCreditNoteItemModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody] OrderCreditNoteItemUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }
            try {

                if (model.OrderId.HasValue && model.OrderId.Value.ToString() != Guid.Empty.ToString()) {
                    var order = await orderService.GetAsync(model.OrderId.Value);
                    model.Currency = order.Currency;

                    OrderItemModel orderItem = null;
                    OrderAssemblyModel orderAssembly = null;

                    if (model.OrderItemId.HasValue && model.OrderItemId.Value != Guid.Empty) {
                        orderItem = await orderItemService.GetAsync(model.OrderItemId.Value);
                    } else if (model.OrderAssemblyId.HasValue && model.OrderAssemblyId.Value != Guid.Empty) {
                        orderAssembly = await orderAssemblyService.GetAsync(model.OrderAssemblyId.Value);
                    }

                    OrderItemModel item = null;
                    OrderAssemblyModel assemblyItem = null;

                    if (model.IsDelivery)
                    {
                        order.OrderItems.Where(c => c.Description.Contains("Delivery Charge")).FirstOrDefault();
                    }
                    else
                    {
                        order.OrderItems.Where(c => !c.Description.Contains("Delivery Charge")).FirstOrDefault();
                    }

                    model.AccountCode =
                        model.IsDelivery ?
                            item != null ? item.AccountCode :
                            assemblyItem != null ? assemblyItem.AccountCode :
                            null :
                        order.AccountCode;

                    model.UnitPrice = item != null ? item.UnitPrice : assemblyItem != null ? assemblyItem.ItemValue : 0.0;

                    var job = await jobService.GetByOrderIdAsync(model.OrderId.Value);
                    var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
                    if (emailClaim == null)
                    {
                        return Unauthorized();
                    }

                    var user = await userService.GetUserByEmailAsync(emailClaim.Value);
                    var jobInvoice = new JobInvoiceModel();

                    if (job == null) {
                        job = await jobService.PostAsync(new JobModel {
                            CreatedBy = user != null ? $"{user.Firstname} {user.Lastname}" : null,
                            OrderId = order.OrderId,
                            CostCentre = user.Location == "Oldmeldrum" ? "001" : user.Location == "West Bromwich" ? "002" : "001",
                            OrderType = order.Type,
                            OrderNumber = order.Number,
                            OrderCustomerName = order.Customer != null ? order.Customer.Name : string.Empty,
                            Description = order.FirstItemDescription,
                            DateOrderRaised = order.Created,
                            Currency = order.Currency
                        });
                    }

                    jobInvoice.JobId = job.JobId;
                    jobInvoice.CreatedBy = $"{user.Firstname} {user.Lastname}";
                    jobInvoice.JobType = orderItem != null ? "SALE CREDIT" : orderAssembly != null ? "HIRE CREDIT" : "CREDIT";
                    jobInvoice.InvoiceNumber = model.CreditNoteNumber;
                    jobInvoice.InvoiceDate = DateTime.UtcNow;
                    jobInvoice.CustomerPO = order != null ? order.CustomerRef : string.Empty;

                    if (!await jobInvoiceService.JobInvoiceNumberExistsAsync(jobInvoice.InvoiceNumber)) {
                        jobInvoice = await jobInvoiceService.PostAsync(jobInvoice);
                    } else {
                        return BadRequest($"Invoice Number already exists in the system");
                    }

                    CurrencyRate currencyRate = null;

                    if (order.Type == "Sale") {
                        if (order.Currency == "Euro" || order.Currency == "US Dollar") {
                            currencyRate = unitOfWork.CurrencyRate.Query().Where(c => c.Name == order.Currency).OrderByDescending(o => o.Created).FirstOrDefault();
                        }
                    } //do we need the else statement for Hires here ?

                    var unitPrice = currencyRate != null ? model.UnitPrice / currencyRate.Rate : model.UnitPrice;

                    var jobInvoiceItem = new JobInvoiceItemModel {
                        JobId = job.JobId,
                        JobInvoiceId = jobInvoice.JobInvoiceId,
                        InvoiceNumber = jobInvoice.InvoiceNumber,
                        InvoiceType = orderItem != null ? "SALE CREDIT" : orderAssembly != null ? "HIRE CREDIT" : "CREDIT",
                        Quantity = 1,
                        UnitPrice = model.TotalValue,
                        StockCategory = string.Empty,
                        StockCode = string.Empty,
                        Value = unitPrice,
                        Currency = order.Currency,
                        Description = model.Description,
                        OrderItemId = orderItem != null ? orderItem.OrderItemId : orderAssembly != null ? orderAssembly.OrderAssemblyId : Guid.NewGuid(),
                        OrderItemStockItemId = null,
                        QuantityToDeliver = 0,
                        ToFollow = 0,
                        Vat = string.Empty,
                        AccountCode = orderItem == null ? order.AccountCode : orderAssembly != null ? orderAssembly.AccountCode : orderItem.OrderAccountCode,
                        IsStockItem = false,
                        InvoiceDate = jobInvoice.InvoiceDate,
                        SaleOrderNumber = order.Number,
                        IsDeliveryCharge = false,
                        IsInitialDeliveryCharge = false
                    };

                    await jobInvoiceItemService.PostAsync(jobInvoiceItem);
                }

                var orderCreditNoteItem = await orderCreditNoteItemService.PostAsync(model);
                return Ok(orderCreditNoteItem);
            } catch (Exception ex) {
                logger.LogError($"Cannot create order credit note- {ex}");
                return BadRequest();
            }
        }


        [HttpPost("creditNoteAll")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> UpdateToCreditNote([FromBody] List<OrderCreditNoteItemUpdateModel> models) {

            try {
                var id = models[0].OrderId.HasValue ? models[0].OrderId : null;
                var creditNoteNumber = models[0].CreditNoteNumber;
                var order = id.HasValue ? await orderService.GetAsync(id.Value) : null;

                if(order != null) {
                    var job = await jobService.GetByOrderIdAsync(order.OrderId);
                    var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
                    if (emailClaim == null)
                    {
                        return Unauthorized();
                    }

                    var user = await userService.GetUserByEmailAsync(emailClaim.Value);
                    var jobInvoice = new JobInvoiceModel();

                    if (job == null) {
                        job = await jobService.PostAsync(new JobModel {
                            CreatedBy = user != null ? $"{user.Firstname} {user.Lastname}" : null,
                            OrderId = order.OrderId,
                            CostCentre = user.Location == "Oldmeldrum" ? "001" : user.Location == "West Bromwich" ? "002" : "001",
                            OrderType = order.Type,
                            OrderNumber = order.Number,
                            OrderCustomerName = order.Customer != null ? order.Customer.Name : string.Empty,
                            Description = order.FirstItemDescription,
                            DateOrderRaised = order.Created,
                            Currency = order.Currency
                        });
                    }

                    jobInvoice.JobId = job.JobId;
                    jobInvoice.CreatedBy = $"{user.Firstname} {user.Lastname}";

                    jobInvoice.JobType =
                        models.All(a => a.OrderItemId.HasValue && !a.OrderAssemblyId.HasValue) ? "SALE CREDIT" :
                        models.All(a => a.OrderAssemblyId.HasValue && !a.OrderItemId.HasValue) ? "HIRE CREDIT" :
                        "CREDIT";

                    jobInvoice.InvoiceNumber = creditNoteNumber;
                    jobInvoice.InvoiceDate = DateTime.UtcNow;
                    jobInvoice.CustomerPO = order != null ? order.CustomerRef : string.Empty;

                    if (!await jobInvoiceService.JobInvoiceNumberExistsAsync(jobInvoice.InvoiceNumber)) {
                        jobInvoice = await jobInvoiceService.PostAsync(jobInvoice);
                    } else {
                        return BadRequest($"Invoice Number already exists in the system");
                    }

                    foreach (var model in models) {

                        OrderItemModel orderItem = null;
                        OrderAssemblyModel orderAssembly = null;

                        if (model.OrderItemId.HasValue && model.OrderItemId.Value != Guid.Empty) {
                            orderItem = await orderItemService.GetAsync(model.OrderItemId.Value);
                        } else if (model.OrderAssemblyId.HasValue && model.OrderAssemblyId != Guid.Empty)
                        {
                            orderAssembly = await orderAssemblyService.GetAsync(model.OrderAssemblyId.Value);
                        }

                        if (orderItem != null || orderAssembly != null) {
                            var jobInvoiceItem = new JobInvoiceItemModel {
                                JobId = job.JobId,
                                JobInvoiceId = jobInvoice.JobInvoiceId,
                                InvoiceNumber = jobInvoice.InvoiceNumber,
                                InvoiceType = "SALE CREDIT",
                                Quantity = 1,
                                UnitPrice = model.TotalValue,
                                StockCategory = string.Empty,
                                StockCode = string.Empty,
                                Value = model.UnitPrice,
                                Currency = order.Currency,
                                Description = orderItem != null ? orderItem.OrderItemFirstTwoLinesDescription : orderAssembly != null ? orderAssembly.OrderAssemblyFirstTwoLinesDescription : "CREDIT ITEM",
                                OrderItemId = orderItem != null ? orderItem.OrderItemId : orderAssembly != null ? orderAssembly.OrderAssemblyId : Guid.NewGuid(),
                                OrderItemStockItemId = null,
                                QuantityToDeliver = 0,
                                ToFollow = 0,
                                Vat = string.Empty,
                                AccountCode = orderItem == null ? order.AccountCode : orderAssembly != null ? orderAssembly.AccountCode : orderItem.OrderAccountCode,
                                IsStockItem = false,
                                InvoiceDate = jobInvoice.InvoiceDate,
                                SaleOrderNumber = order.Number,
                                IsDeliveryCharge = false,
                                IsInitialDeliveryCharge = false
                            };

                            await jobInvoiceItemService.PostAsync(jobInvoiceItem);
                        }
                    }

                    await orderCreditNoteItemService.PostAllAsync(models, order.Currency);
                }

                return Ok();
            } catch (Exception ex) {
                logger.LogError($"Cannot create order credit note- {ex}");
                return BadRequest();
            }
        }



        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Delete(Guid id) {
            var creditNoteItem = await orderCreditNoteItemService.GetByIdAsync(id);
            if (creditNoteItem.OrderItemId.HasValue) {
                var orderItem = await orderItemService.GetAsync(creditNoteItem.OrderItemId.Value);
                orderItem.HasCreditNote = false;
                await orderItemService.PutAsync(orderItem.OrderItemId, orderItem);
            } else if (creditNoteItem.OrderAssemblyId.HasValue)
            {
                var orderAssembly = await orderAssemblyService.GetAsync(creditNoteItem.OrderAssemblyId.Value);
                orderAssembly.HasCreditNote = false;
                await orderAssemblyService.PutAsync(orderAssembly.OrderAssemblyId, orderAssembly);
            }

            await orderCreditNoteItemService.DeleteAsync(id);
            return Ok();
        }

        [HttpPost("pushToXero")]
        [ProducesResponseType(200, Type = typeof(OrderCreditNoteItemUpdateModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> PushToXero(List<OrderCreditNoteItemModel> models) {
            var orderCreditNoteItem = await unitOfWork.OrderCreditNoteItem.Query().Where(c => c.OrderCreditNoteItemId == models[0].OrderCreditNoteItemId ).Include(x => x.OrderItem).ThenInclude(x => x.Order).ThenInclude(x => x.Customer).FirstOrDefaultAsync();
            var order = await unitOfWork.Order.Query().Where(c => c.OrderId == orderCreditNoteItem.OrderId).Include(x => x.Customer).FirstOrDefaultAsync();
            var customerXeroContactId = order.Customer.XeroContactId;
            try {
                    await orderCreditNoteItemService.PushAllToXero(models, customerXeroContactId.Value);
                return Ok();
            } catch {
                return BadRequest();
            }
        }
    }
}

