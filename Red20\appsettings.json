{
  "ConnectionStrings": {
    "ConnectionString": "Server=(local);Initial Catalog=Red20;Integrated Security=True;MultipleActiveResultSets=true"

  },
  "Client": {
    "Host": "https://red20.z33.web.core.windows.net"
  },
  "Security": {
    "Key": "{BAE6DD16-59D9-4092-AF82-F32F260B5207}",
    "TokenExpiryMins": "5",
    "RefreshTokenExpiryMins": "60"
  },
  "AzureBlobStorage": {
    "StorageAccountName": "red20",
    "StorageAccountKey": "****************************************************************************************",
    "Container": "files"
  },
  "XeroConfiguration": {
    //"ClientId": "C526F238727842C9944B2A2B973D1725",
    //"ClientSecret": "x8mqR2zbMElz4b5_Nm4_0K0KP9k9dkmnmaWb_qp4k-Fo9qkW",  
    "ClientId": "E4BC995AA0AC46D6A87ABC2FA1CDC606",
    "ClientSecret": "ylNUg-KiEAF7aZ1_8F_jL9qKDOOnf_hytOH4-ppJlSKcudZU",
    "CallbackUri": "https://localhost:44359/Authorization/Callback",
    "Scope": "openid profile email files offline_access accounting.transactions accounting.transactions.read accounting.reports.read accounting.journals.read accounting.settings accounting.settings.read accounting.contacts accounting.contacts.read accounting.attachments accounting.attachments.read assets assets.read",
    "State": "YOUR_STATE"
  },
  "Email": {
    "SmtpHost": "smtp.mandrillapp.com",
    "Sender": "<EMAIL>",
    "Name": "Red 20",
    "MandrillApiKey": "**********************",
    "CertificateExpiryEmail": "<EMAIL>"
  },
  "ElmahIo": {
    "ApiKey": "8fa238b6630d4e299547ae4ad9da8c4d",
    "LogId": "1b40cb39-14e9-4dd8-8e31-0296080c1b93",
    "LogLevel": {
      "Default": "Information"
    }
  },
  "AllowedHosts": "*",
  "KeyVaultUri": "https://safetyscannerkey.vault.azure.net/",
  "Auth0": {
    "Domain": "cometsignals.uk.auth0.com",
    "Audience": "https://cometsignals.uk.auth0.com/api/v2/",
    "DatabaseConnectionId": "",
    "DatabaseConnectionName": ""
  }
}