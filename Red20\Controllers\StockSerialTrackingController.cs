﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Red20.Excel.Export;
using Red20.Model.Data.StockSerialTracking;
using Red20.Service.Data.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Red20.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class StockSerialTrackingController : ControllerBase
    {
        private IStockSerialTrackingService stockSerialTrackingService;
        private IStockService stockService;
        private IUserService userService;
        private IStockAssemblyService stockAssemblyService;
        private IUnitOfWork unitOfWork;

        public StockSerialTrackingController(IStockSerialTrackingService stockSerialTrackingService, IStockService stockService, IStockAssemblyService stockAssemblyService, IUnitOfWork unitOfWork, IUserService userService)
        {
            this.stockSerialTrackingService = stockSerialTrackingService;
            this.stockService = stockService;
            this.stockAssemblyService = stockAssemblyService;
            this.unitOfWork = unitOfWork;
            this.userService = userService;
        }

        [HttpGet]
        [ProducesResponseType(200, Type = typeof(List<StockSerialTrackingModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get()
        {
            var stockSerialTrackings = await stockSerialTrackingService.GetAsync();
            return Ok(stockSerialTrackings);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(StockSerialTrackingModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id)
        {
            var stockSerialTracking = await stockSerialTrackingService.GetAsync(id);
            return Ok(stockSerialTracking);
        }

        [HttpGet("byStockOut/{id}")]
        [ProducesResponseType(200, Type = typeof(List<StockSerialTrackingModel>))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetByStockOutId(Guid id)
        {
            var stockSerialTrackings = await stockSerialTrackingService.GetBySerialNumberOutAsync(id);
            return Ok(stockSerialTrackings);
        }

        [HttpGet("byStock/{id}")]
        [ProducesResponseType(200, Type = typeof(List<StockSerialTrackingModel>))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetByStockId(Guid id)
        {
            var stockSerialTrackings = await stockSerialTrackingService.GetByStockIdAsync(id);
            return Ok(stockSerialTrackings);
        }

        [HttpGet("byStockForSaleInvoice/{id}")]
        [ProducesResponseType(200, Type = typeof(List<string>))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetByStockForSaleInvoice(Guid id)
        {
            var serialNumbers = await stockSerialTrackingService.GetByStockIdForSaleInvoiceAsync(id);
            return Ok(serialNumbers);
        }


        [HttpGet("byStockAssembly/{id}/{type}")]
        [ProducesResponseType(200, Type = typeof(List<StockSerialTrackingModel>))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetByStockAssemblyId(Guid id, string type)
        {
            var stockAssemblySerialNumbers = new List<StockSerialTrackingModel>();
            var stockAssemblies = await unitOfWork.StockAssembly.Query(c => c.StockId == id).Include(x => x.AssemblyStock).ToListAsync();
            var stockAssemblySerials = stockAssemblies.Where(c => c.AssemblyStock.Types.Contains("Serial Number"));

            if (stockAssemblies.Any())
            {
                foreach (var stockAssembly in stockAssemblySerials)
                {
                    var stockSerials = await stockSerialTrackingService.GetByStockIdAsync(stockAssembly.AssemblyStock.StockId);
                    if (type == "Assemble")
                    {
                        foreach (var stockSerial in stockSerials.Where(c => !c.IsOut))
                        {
                            stockAssemblySerialNumbers.Add(stockSerial);
                        }
                    } else
                    {
                        foreach (var stockSerial in stockSerials.Where(c => c.IsOut))
                        {
                            stockAssemblySerialNumbers.Add(stockSerial);
                        }
                    }
                }
            }

            return Ok(stockAssemblySerialNumbers);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(StockSerialTrackingModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody] StockSerialTrackingUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }

            var stockSerialTracking = await stockSerialTrackingService.PostAsync(model);
            return Ok(stockSerialTracking);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(StockSerialTrackingModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(404)]
        public async Task<IActionResult> Put(Guid id, [FromBody] StockSerialTrackingUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }

            if (await stockSerialTrackingService.GetAsync(id) is null)
            {
                return NotFound();
            }

            var stockSerialTracking = await stockSerialTrackingService.PutAsync(id, model);
            return Ok(stockSerialTracking);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Delete(Guid id)
        {
            if (await stockSerialTrackingService.GetAsync(id) is null)
            {
                return NotFound();
            }

            await stockSerialTrackingService.DeleteAsync(id);
            return Ok();
        }

        [HttpGet("stockSerialTrackingReport/{stockId}")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> PrintStockSerialTrackingListReport(Guid stockId)
        {
            try
            {
                var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
                if (emailClaim == null)
                {
                    return Unauthorized();
                }

                var user = await userService.GetUserByEmailAsync(emailClaim.Value);
                var currentUser = $"{user.Firstname} {user.Lastname}";

                var stockSerialTrackings = unitOfWork.StockSerialTracking.Query(c => c.StockId == stockId).Include(x => x.StockSerialTrackingLogs).OrderBy(c => c.IsOut).ThenBy(c => c.SerialNumber);
                var stockData = ExportUtility.ExportStockSerialTrackingReport(stockSerialTrackings, currentUser);
                return File(stockData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            } catch (Exception)
            {
                return NotFound();
            }
        }

        [HttpPut("updateSerialNumber/{id}/{serialNumber}")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> UpdateSerialNumber(Guid id, string serialNumber)
        {

            try
            {
                var serialTracking = unitOfWork.StockSerialTracking.Query().Where(c => c.StockSerialTrackingId == id).FirstOrDefault();

                serialTracking.SerialNumber = serialNumber;
                unitOfWork.StockSerialTracking.Update(serialTracking);

                await unitOfWork.SaveChangesAsync();

            } catch (Exception)
            {
                return BadRequest();
            }
            return Ok();
        }
    }
}
