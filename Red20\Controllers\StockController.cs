﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using Red20.Excel.Export;
using Red20.Excel.Model;
using Red20.Model.Data.Document;
using Red20.Model.Data.Order;
using Red20.Model.Data.PurchaseOrder;
using Red20.Model.Data.Stock;
using Red20.Model.Data.StockAssembly;
using Red20.Model.Data.StockTransaction;
using Red20.Model.Entity;
using Red20.Service.Data.Interface;
using Red20.Service.Storage.Interface;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using ClosedXML.Excel;
using Red20.Service.Data;

namespace Red20.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class StockController : ControllerBase
    {

        private IStockService stockService;
        private IStockTransactionService stockTransactionService;
        private IStockFifoService stockFifoService;
        private IStockTakeService stockTakeService;
        private IStockAssemblyService stockAssemblyService;
        private IOrderStockAllocationService orderStockAllocationService;
        private IPurchaseOrderItemService purchaseOrderItemService;
        private ILogger<AuthController> logger;
        private IOrderItemService orderItemService;
        private IUserService userService;
        private IDocumentService documentService;
        private IStorageService blobStorage;
        IUnitOfWork unitOfWork;
        private IOrderItemStockItemService orderItemStockItemService;

        public StockController(
            IStockService stockService,
            IStockTakeService stockTakeService,
            IStockAssemblyService stockAssemblyService,
            IOrderStockAllocationService orderStockAllocationService,
            IStockFifoService stockFifoService,
            IPurchaseOrderItemService purchaseOrderItemService,
            IStockTransactionService stockTransactionService,
            IUnitOfWork unitOfWork,
            IOrderItemService orderItemService,
            IUserService userService,
           IDocumentService documentService,
            IStorageService blobStorage,
        ILogger<AuthController> logger,
        IOrderItemStockItemService orderItemStockItemService)
        {

            this.stockService = stockService;
            this.stockTakeService = stockTakeService;
            this.stockAssemblyService = stockAssemblyService;
            this.orderStockAllocationService = orderStockAllocationService;
            this.purchaseOrderItemService = purchaseOrderItemService;
            this.orderItemService = orderItemService;
            this.unitOfWork = unitOfWork;
            this.logger = logger;
            this.userService = userService;
            this.documentService = documentService;
            this.blobStorage = blobStorage;
            this.orderItemStockItemService = orderItemStockItemService;
            this.stockTransactionService = stockTransactionService;
            this.stockFifoService = stockFifoService;
        }

        [HttpGet]
        [ProducesResponseType(200, Type = typeof(IList<StockModel>))]
        [ProducesResponseType(401)]
        public IActionResult Get()
        {
            var stocks = stockService.GetAllUnarchivedStocks();
            return Ok(stocks);
        }

        [HttpGet("getAllStocks")]
        [ProducesResponseType(200, Type = typeof(IList<StockModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAllStocks([FromQuery] StockFilterParams filterParams)
        {
            var stocks = await stockService.GetPaginatedStocks(filterParams);
            return Ok(stocks);
        }

        [HttpGet("lookups")]
        [ProducesResponseType(200, Type = typeof(IList<StockLookupModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetStockLookups()
        {
            var stocks = await stockService.GetStockLookups();
            return Ok(stocks);
        }

        [HttpGet("archivedStocks")]
        [ProducesResponseType(200, Type = typeof(IList<StockModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetArchivedStocks()
        {
            var stocks = stockService.GetAllStocks();
            var archivedStocks = stocks.Where(c => c.ArchivedDate.HasValue).ToList();
            return Ok(archivedStocks);
        }

        [HttpGet("stockTake")]
        [ProducesResponseType(200, Type = typeof(IList<StockTakeModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetStockTakes()
        {
            var stocks = stockService.GetStockTakes();
            return Ok(stocks);
        }


        [HttpPost("stockAllTake/{date}")]
        [ProducesResponseType(200, Type = typeof(IList<StockTakeModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAllStockTakes(DateTime? date, [FromBody] List<StockTakeModel> models)
        {
            var stocks = stockService.GetAllStockTakes(date, models);
            return Ok(stocks);
        }

        [HttpGet("assemblyStocks")]
        [ProducesResponseType(200, Type = typeof(IList<StockLookupModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAssemblyStocks()
        {
            var stocks = stockService.GetAllAssemblyStocks();
            return Ok(stocks.Result);
        }

        [HttpGet("getAssemblyStockPrice/{id}")]
        [ProducesResponseType(200, Type = typeof(IList<StockModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAssemblyStockPrice(Guid id)
        {
            var stockAssemblies = await stockAssemblyService.GetByStockIdAsync(id);
            var costPrice = stockAssemblies.Sum(c => c.Quantity * c.CostPrice);
            decimal step = (decimal)Math.Pow(10, 3);
            decimal tmp = Math.Truncate(step * (decimal)costPrice);
            var cp = tmp / step;
            return Ok(cp);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(StockModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id)
        {

            var stock = await stockService.GetForDetailsPageAsync(id);

            if (stock == null)
            {
                return BadRequest();
            }

            var stockOrderItemsStockItems = stock.OrderItemStockItems;
            var orderItems = stockOrderItemsStockItems != null && stockOrderItemsStockItems.Any() ? stockOrderItemsStockItems.Select(s => s.OrderItem).ToList() : null;
            var orders = orderItems != null && orderItems.Any() ? orderItems.Select(s => s.Order).ToList() : null;
            var orderIds = orders != null && orders.Any() ? orders.Where(w => w.GridStatus != "Complete").Select(s => s.OrderId).ToArray() : null;
            var internalOrderItems = await unitOfWork.OrderItem.Query().Include(x => x.Order).Where(c => c.Order.Internal && c.StockId.HasValue && !c.Order.InvoiceDate.HasValue && c.StockId == id).ToListAsync();


            var stockAllocations = orderIds != null && orderIds.Any() ? await orderStockAllocationService.GetByStockIdAndOrderIdsAsync(id, orderIds) : null;

            if (stockAllocations != null && stockAllocations.Any())
            {
                foreach (var allocation in stockAllocations)
                {
                    var order = orders.FirstOrDefault(f => f.OrderId == allocation.OrderId);
                    var orderItemss = orderItems.Where(w => w.OrderId == order.OrderId);

                    var orderItemStockItemsAllItems = new List<Red20.Model.Data.Order.OrderItemStockItemModel>();

                    if (orderItemss != null && orderItemss.Any())
                    {
                        foreach (var orderItem in orderItemss)
                        {
                            var orderItemStockItemss = stockOrderItemsStockItems.Where(w => w.OrderItemId == orderItem.OrderItemId).ToList();

                            if (orderItemStockItemss != null && orderItemStockItemss.Any())
                            {
                                orderItemStockItemsAllItems.AddRange(orderItemStockItemss);
                            }
                        }
                    }

                    var orderItemModel = orderItemStockItemsAllItems != null && orderItemStockItemsAllItems.Any() ?
                        await orderItemService.GetAsync(orderItemStockItemsAllItems[0].OrderItemId) : null;

                    var orderAllocationModel = orderItemStockItemsAllItems != null && orderItemStockItemsAllItems.Any() ?
                        new OrderItemStockItemAllocationModel
                        {
                            OrderItemId = orderItemStockItemsAllItems[0].OrderItemId,
                            StockId = orderItemStockItemsAllItems[0].StockId,
                            Allocated = orderItemStockItemsAllItems.Sum(s => s.Allocated),
                            Quantity = orderItemStockItemsAllItems.Sum(s => s.Quantity),
                            Unallocated = orderItemStockItemsAllItems.Sum(s => s.Unallocated),
                            Invoiced = orderItemStockItemsAllItems.Sum(s => s.Invoiced),
                            OrderItemDescription = orderItemModel != null ? orderItemModel.OrderItemFirstTwoLinesDescription : string.Empty,
                            QuantityToAllocate = 0,
                            QuantityToUnallocate = 0,
                            StockCode = orderItemStockItemsAllItems[0].StockCode
                        } : null;

                    if (orderAllocationModel != null)
                    {
                        allocation.OrderItemStockItemAllocationModels = new List<OrderItemStockItemAllocationModel> { orderAllocationModel };
                    }
                }

                stock.OrderStockAllocations = stockAllocations.ToList();
            }

            var stockOrderInModels = await purchaseOrderItemService.GetByStockIdAsync(id);
            if (stockOrderInModels != null && stockOrderInModels.Any(a => !a.TotalReceived.HasValue || (a.TotalReceived.HasValue && a.TotalReceived < a.Quantity)))
            {
                var orderInModels = stockOrderInModels
                    .Where(a => !a.TotalReceived.HasValue || (a.TotalReceived.HasValue && a.TotalReceived < a.Quantity))
                    .Select(s => new PurchaseOrderStockOrderInModel
                    {
                        PurchaseOrderNumber = s.PurchaseOrder.Number,
                        OrderIn = s.TotalReceived.HasValue && s.Quantity.HasValue ? (s.Quantity.Value - s.TotalReceived.Value) : s.Quantity.HasValue ? Double.Parse(s.Quantity.ToString()) : 0.0,
                        PurchaseOrderId = s.PurchaseOrderId,
                        Created = s.Created,
                        Received = s.TotalReceived.HasValue ? s.TotalReceived.Value : 0.0
                    }).ToList();
                var latestOrderInModel = orderInModels.OrderByDescending(o => o.Created).ToList();
                stock.PurchaseOrderStockOrderInModels = latestOrderInModel;
            }

            if (internalOrderItems != null && internalOrderItems.Any())
            {
                stock.OrderInternalStockModels = internalOrderItems.Select(s => new OrderInternalStockModel
                {
                    OrderId = s.OrderId,
                    Quantity = s.Quantity,
                    OrderNumber = s.Order.Number
                }).ToList();
            }

            //stock.TotalOrderIn = stock.PurchaseOrderStockOrderInModels != null && stock.PurchaseOrderStockOrderInModels.Any() ?
            //        stock.PurchaseOrderStockOrderInModels.Sum(s => s.OrderIn) : 0.0;

            return Ok(stock);
        }

        [HttpGet("getForOrder/{id}")]
        [ProducesResponseType(200, Type = typeof(StockModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetForOrder(Guid id)
        {

            var stock = await stockService.GetAsync(id);
            return Ok(stock);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(StockModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody] StockUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }

            if (await stockService.StockCodeExistsAsync(model.Code))
            {
                return BadRequest();
            }

            var stock = await stockService.PostAsync(model);

            return Ok(stock);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(StockModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Put(Guid id, [FromBody] StockUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }

            var stock = await stockService.GetAsync(id);

            if (stock is null)
            {
                return BadRequest();
            }

            model.FreeStock = stock.PhysicalStock - stock.AllocatedStock;

            stock = await stockService.PutAsync(id, model);

            return Ok(stock);
        }

        [HttpPut("archive/{id}")]
        [ProducesResponseType(200, Type = typeof(StockModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> ArchiveStock(Guid id, [FromBody] StockUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }

            var stock = await stockService.GetAsync(id);

            if (stock is null)
            {
                return BadRequest();
            }

            model.ArchivedDate = DateTime.Now;

            stock = await stockService.PutAsync(id, model);

            return Ok(stock);
        }

        [HttpPut("unArchive/{id}")]
        [ProducesResponseType(200, Type = typeof(StockModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> UnArchiveStock(Guid id, [FromBody] StockUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }

            var stock = await stockService.GetAsync(id);

            if (stock is null)
            {
                return BadRequest();
            }

            model.ArchivedDate = null;

            stock = await stockService.PutAsync(id, model);

            return Ok(stock);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Delete(Guid id)
        {
            await stockService.DeleteAsync(id);
            return Ok();
        }

        [HttpGet("assembly-stock/{id}")]
        [ProducesResponseType(200, Type = typeof(List<StockModel>))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAssemblyStock(Guid id)
        {
            var stocks = await stockService.GetAssemblyStock(id);
            return Ok(stocks);
        }

        [HttpGet("stockCheck/{id}/{quantity}")]
        [ProducesResponseType(200, Type = typeof(List<StockAvailabilityModel>))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> CheckStock(Guid id, int quantity)
        {
            var stock = await stockService.GetAsync(id);
            var stockAssemblies = await stockAssemblyService.GetByStockIdAsync(id);
            var canBuild = stockAssemblies.Any() ? stockAssemblies.Min(c => (int)(Math.Floor((double)(c.TotalAvailable / c.Quantity)))) : 0;

            var stockAvailability = new StockAvailabilityModel
            {
                StockId = stock.StockId,
                Description = stock.Description,
                Quantity = quantity,
                StockCode = stock.Code,
                CanBuild = canBuild,
                TotalAvailable = stock.PhysicalStock,
                TotalNeeded = quantity,
                StockAssemblyModels = stockAssemblies.Select(s => new StockAssemblyModel
                {
                    AssemblyStockCode = s.AssemblyStockCode,
                    Quantity = s.Quantity,
                    TotalAvailable = s.TotalAvailable,
                    TotalNeeded = quantity,
                    CostPrice = s.CostPrice
                }).ToList()
            };
            return Ok(stockAvailability);
        }

        [HttpGet("stockCheckReport")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> PrintStockCheckReport()
        {
            try
            {
                var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
                if (emailClaim == null)
                {
                    return Unauthorized();
                }

                var user = await userService.GetUserByEmailAsync(emailClaim.Value);
                var currentUser = $"{user.Firstname} {user.Lastname}";

                var stocks = stockService.GetStocks();
                var nonArchivedStocks = stocks.Where(c => !c.ArchivedDate.HasValue).ToList();
                var orderedStocks = nonArchivedStocks.OrderBy(s => s.Category.Code).ThenBy(s => s.Code).ToList();
                var stockData = ExportUtility.ExportStockCheckReport(orderedStocks, currentUser);
                return File(stockData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            } catch (Exception)
            {
                return NotFound();
            }
        }

        [HttpGet("stockListReport")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> PrintStockListReport()
        {
            try
            {
                var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                      c.Type == ClaimTypes.Email);
                if (emailClaim == null)
                {
                    return Unauthorized();
                }

                var user = await userService.GetUserByEmailAsync(emailClaim.Value);
                var currentUser = $"{user.Firstname} {user.Lastname}";

                IEnumerable<Stock> stocks = (IEnumerable<Stock>)stockService.GetStocks().Where(c => !c.ArchivedDate.HasValue);
                var stockData = ExportUtility.ExportStockListReport(stocks, currentUser);
                return File(stockData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            } catch (Exception)
            {
                return NotFound();
            }
        }

        [HttpGet("stockAssemblyReport/{stockId}")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> PrintStockAssemblyReport(Guid stockId)
        {
            try
            {
                var stock = await stockService.GetAsync(stockId);

                var stocks = await stockAssemblyService.GetByStockIdAsync(stockId);
                var stockData = ExportUtility.ExportStockAssemblyReport(stocks, stock.Code);
                return File(stockData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            } catch (Exception)
            {
                return NotFound();
            }
        }

        [HttpGet("stockValuationReport/{date}/{categoryId}")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> PrintStockValuationReport(DateTime date, Guid categoryId)
        {
            try
            {
                var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
                if (emailClaim == null)
                {
                    return Unauthorized();
                }

                var user = await userService.GetUserByEmailAsync(emailClaim.Value);
                var document = new DocumentModel();

                IQueryable<Stock> stocks = null;

                if (categoryId.ToString() == "00000000-0000-0000-0000-000000000000")
                {
                    stocks = stockService.GetAllStocksForValuationReport();
                } else
                {
                    stocks = stockService.GetStocksByCategory(categoryId);
                }

                var newStocks = new List<Stock>();

                if (stocks != null && stocks.Any())
                {
                    foreach (var stock in stocks)
                    {
                        stock.ValuationDate = date;
                        unitOfWork.Stock.Update(stock);

                        var sTakes = stock.StockTakes;

                        var latestStockTake = stock.StockTakes
                         .Where(w => w.Created.Year == date.Year && !w.IsArchived && w.Created.Date <= date.Date)
                         .OrderByDescending(d => d.Created)
                         .FirstOrDefault();

                        if (latestStockTake == null)
                        {
                            latestStockTake = stock.StockTakes
                                .Where(w => w.Date.Year == date.Year - 1 && !w.IsArchived)
                                .OrderByDescending(d => d.Created)
                                .FirstOrDefault();
                        }

                        var latestStockTransaction = stock.StockTransactions
                             .Where(w => w.Created.Year == date.Year && !w.IsArchived && w.Created.Date <= date.Date)
                             .OrderByDescending(d => d.Created)
                             .FirstOrDefault();

                        if (latestStockTransaction == null)
                        {
                            latestStockTransaction = stock.StockTransactions
                                .Where(w => w.Created.Year == date.Year - 1 && !w.IsArchived)
                                .OrderByDescending(d => d.Created)
                                .FirstOrDefault();
                        }

                        var category = stock.Category;

                        if (latestStockTake != null)
                        {
                            var newStock = new Stock
                            {
                                StockId = stock.StockId,
                                CategoryId = stock.CategoryId,
                                Category = category,
                                Code = stock.Code,
                                Description = stock.Description,
                                PhysicalStock = latestStockTransaction != null && latestStockTransaction.PreviousStockQuantity.HasValue ? latestStockTransaction.PreviousStockQuantity.Value : latestStockTake.Quantity,
                                CostPrice = stock.CostPrice,
                            };
                            if (newStock.PhysicalStock > 0)
                            {
                                newStocks.Add(newStock);
                            }
                        } else
                        {
                            var newStock = new Stock
                            {
                                StockId = stock.StockId,
                                CategoryId = stock.CategoryId,
                                Category = category,
                                Code = stock.Code,
                                Description = stock.Description,
                                PhysicalStock = stock.PhysicalStock,
                                CostPrice = stock.CostPrice,
                            };
                            if (newStock.PhysicalStock > 0)
                            {
                                newStocks.Add(newStock);
                            }
                        }
                    }
                }

                await unitOfWork.SaveChangesAsync();

                var filteredStocks = newStocks.OrderBy(c => c.Code).ToList();

                var stockIds = filteredStocks.Select(s => s.StockId).ToArray();
                var stockFifoRecords = await GetStockFifoRecordsAsync(date, stockIds);

                var stockLookup = filteredStocks.ToDictionary(s => s.StockId);

                foreach (var stockFifo in stockFifoRecords)
                {
                    if (stockLookup.TryGetValue(stockFifo.StockId, out var stock))
                    {
                        stockFifo.Stock = stock;
                    }
                }

                var stockData = ExportUtility.ExportStockValuationReport(filteredStocks, date, stockFifoRecords);

                document = await documentService.PostAsync("Stock_Valuation_Report.xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"{user.Firstname} {user.Lastname}", "Stock Valuation Report", null, null, null, false, false, true, date);
                var file = File(stockData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                await blobStorage.UploadAsync(document.DocumentId, stockData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                return file;
            } catch (Exception)
            {
                return NotFound();
            }
        }

        // Add this at the top of your controller/service
        private async Task<List<StockFifo>> GetStockFifoRecordsAsync(DateTime date, Guid[] stockIds)
        {
            try
            {
                if (stockIds == null || !stockIds.Any())
                {
                    return new List<StockFifo>();
                }

                var cutoffDate = date.AddYears(-4);

                var query = unitOfWork.StockFifo.Query()
                    .Where(q => stockIds.Contains(q.StockId))
                    .Where(q => !q.IsArchived)
                    .Where(q => q.Created.Date <= date.Date)
                    .Where(q => q.Created >= cutoffDate);

                return await query
                    .Include(i => i.Stock)
                    .OrderByDescending(c => c.Created)
                    .AsSplitQuery()
                    .AsNoTracking()
                    .ToListAsync();
            } catch (Exception ex)
            {
                throw new ApplicationException($"Error retrieving stock FIFO records: {ex.Message}", ex);
            }
        }

        [HttpGet("stockAllValuationReport/{date}")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> PrintAllStockValuationReport(DateTime date)
        {
            try
            {
                var stopwatch = Stopwatch.StartNew();
                var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
                if (emailClaim == null)
                {
                    return Unauthorized();
                }

                var user = await userService.GetUserByEmailAsync(emailClaim.Value);

                // Get stocks with necessary includes to avoid N+1 queries
                var cutoffDate = date.AddYears(-4);
                var stocks = await stockService.GetAllStocksForValuationReport()
                    .Include(s => s.Category)
                    .Include(s => s.StockTakes.Where(st =>
                        !st.IsArchived &&
                        st.Created.Date <= date &&
                        st.Created.Date >= cutoffDate))
                    .Include(s => s.StockTransactions.Where(st =>
                        !st.IsArchived &&
                        st.Created.Date <= date &&
                        st.Created.Date >= cutoffDate))
                    .AsSplitQuery()
                    .ToListAsync();

                var newStocks = new List<Stock>();
                const int batchSize = 500;

                // Process stocks in batches
                foreach (var stockBatch in stocks.Chunk(batchSize))
                {
                    foreach (var stock in stockBatch)
                    {
                        var stockValuationStock = CalculateValuationStock(stock);

                        // Update stock valuation
                        stock.ValuationDate = date;
                        stock.ValuationPhysicalStock = stockValuationStock;

                        var latestStockTake = GetLatestStockTake(stock.StockTakes, date);
                        var latestStockTransaction = GetLatestStockTransaction(stock.StockTransactions, date);

                        var newStock = CreateNewStock(stock, latestStockTake, latestStockTransaction);
                        if (newStock != null)
                        {
                            newStocks.Add(newStock);
                        }
                    }

                    // Save changes for each batch
                    await unitOfWork.SaveChangesAsync();
                }

                var stockIds = newStocks.Select(s => s.StockId).ToArray();
                var stockFifoRecords = await GetStockFifoRecordsAsync(date, stockIds);

                // Use lookup for better performance
                var stockLookup = newStocks.ToDictionary(s => s.StockId);
                foreach (var stockFifo in stockFifoRecords)
                {
                    if (stockLookup.TryGetValue(stockFifo.StockId, out var stock))
                    {
                        stockFifo.Stock = stock;
                    }
                }

                var stockData = ExportUtility.ExportStockValuationReport(newStocks, date, stockFifoRecords);

                var document = await documentService.PostAsync(
                    "Stock_Valuation_Report.xlsx",
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    $"{user.Firstname} {user.Lastname}",
                    "Stock Valuation Report",
                    null, null, null, false, false, true, date);

                var file = File(stockData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                await blobStorage.UploadAsync(document.DocumentId, stockData,
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

                stopwatch.Stop();

                return file;
            } catch (Exception)
            {
                return StatusCode(500, "An error occurred while generating the report");
            }
        }

        private double CalculateValuationStock(Stock stock)
        {
            if (stock.ValuationPhysicalStock.HasValue && stock.ValuationPhysicalStock.Value > 0 && stock.ValuationPhysicalStock != stock.PhysicalStock && stock.PhysicalStock > stock.ValuationPhysicalStock)
            {
                var diff = stock.PhysicalStock - stock.ValuationPhysicalStock;
                return stock.ValuationPhysicalStock.Value + diff.Value;
            } else if (stock.ValuationPhysicalStock.HasValue && stock.ValuationPhysicalStock.Value > 0 && stock.ValuationPhysicalStock == stock.PhysicalStock)
            {
                return stock.ValuationPhysicalStock.Value;
            } else if (!stock.ValuationPhysicalStock.HasValue || stock.ValuationPhysicalStock == 0)
            {
                return stock.PhysicalStock;
            }

            // Fallback case (though with the conditions above, this should never be reached)
            return stock.PhysicalStock;
        }

        private StockTake GetLatestStockTake(IEnumerable<StockTake> stockTakes, DateTime date)
        {
            return stockTakes
                .OrderByDescending(d => d.Created)
                .FirstOrDefault();
        }

        private StockTransaction GetLatestStockTransaction(IEnumerable<StockTransaction> transactions, DateTime date)
        {
            return transactions
                .OrderByDescending(d => d.Created)
                .FirstOrDefault();
        }

        private Stock CreateNewStock(Stock stock, StockTake latestStockTake, StockTransaction latestStockTransaction)
        {
            var newStock = new Stock
            {
                StockId = stock.StockId,
                CategoryId = stock.CategoryId,
                Category = stock.Category,
                Code = stock.Code,
                Description = stock.Description,
                CostPrice = stock.CostPrice
            };

            if (latestStockTake != null)
            {
                newStock.PhysicalStock = latestStockTransaction != null &&
                    latestStockTransaction.PreviousStockQuantity.HasValue
                        ? latestStockTransaction.PreviousStockQuantity.Value
                        : latestStockTake.Quantity;
            } else
            {
                newStock.PhysicalStock = stock.PhysicalStock;
            }

            return newStock;
        }

        [HttpPost("updateStockTake")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> UpdateStockTake(List<StockTakeUpdateModel> models)
        {
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);
            var currentUser = $"{user.Firstname} {user.Lastname}";
            try
            {
                var updates = await stockService.UpdateStockTake(models, currentUser);

                return Ok(updates);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot update Stock - {ex}");
                return BadRequest("An error occurred while updating stocks.");
            }
        }

        [HttpGet("shortFallReport")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> PrintStockShortFallReport()
        {
            var models = stockService.GetAllStocks();
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);
            var currentUser = $"{user.Firstname} {user.Lastname}";
            var itemData = ExportUtility.ExportStockShortfallReport(models, currentUser);
            return File(itemData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        }

        [HttpGet("stockTakeReport")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> PrintStockTakeReport()
        {
            try
            {
                var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
                if (emailClaim == null)
                {
                    return Unauthorized();
                }

                var user = await userService.GetUserByEmailAsync(emailClaim.Value);
                var currentUser = $"{user.Firstname} {user.Lastname}";

                IEnumerable<StockTake> stockTakes = unitOfWork.StockTake.Query().Include(x => x.Stock).ThenInclude(x => x.Category).Where(c => c.Date.ToString() == "2021-03-31").ToList();
                var stockData = ExportUtility.ExportStockTakeReport(stockTakes);
                return File(stockData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            } catch (Exception)
            {
                return NotFound();
            }
        }

        [HttpGet("soldReport/{from}/{to}")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> PrintStockSoldReport(DateTime from, DateTime to)
        {
            var stockTransactions = unitOfWork.StockTransaction.Query().Include(x => x.Stock).ThenInclude(x => x.Category).Where(c => c.Created.Date >= from.Date && c.Created.Date <= to.Date && (c.Type == "S (OUT)" || c.Type == "P (IN)")).OrderBy(c => c.Stock.Code).ThenByDescending(c => c.Created);
            var stockTransationData = ExportUtility.ExportStockSoldReport(stockTransactions, from, to);

            return File(stockTransationData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        }

        [HttpGet("nextStock/{id}")]
        [ProducesResponseType(200)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> NextStock(Guid id)
        {
            var stocks = unitOfWork.Stock.Query(c => !c.ArchivedDate.HasValue).Include(c => c.Category)
                .OrderBy(c => c.Category.Code).ThenBy(c => c.Code)
                .AsSplitQuery()
                .ToList();
            var stock = unitOfWork.Stock.Query(c => c.StockId == id).FirstOrDefault();
            var nextStock = new Stock();
            nextStock = stocks.SkipWhile(c => c != stock).Skip(1).DefaultIfEmpty(stocks[0]).FirstOrDefault();
            return Ok(nextStock.StockId);
        }

        [HttpGet("previousStock/{id}")]
        [ProducesResponseType(200)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> PreviousStock(Guid id)
        {
            var stocks = unitOfWork.Stock.Query(c => !c.ArchivedDate.HasValue).Include(c => c.Category)
                 .OrderBy(c => c.Category.Code).ThenBy(c => c.Code)
                .AsSplitQuery()
                .ToList();
            var stock = unitOfWork.Stock.Query(c => c.StockId == id).FirstOrDefault();
            var previousStock = new Stock();
            previousStock = stocks.TakeWhile(x => !x.Equals(stock)).Last();
            return Ok(previousStock.StockId);
        }

        #region Update Stock Transaction

        [HttpGet("updateStockValuation/{date}")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> UpdateStockTransaction(DateTime date)
        {
            try
            {
                var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
                if (emailClaim == null)
                {
                    return Unauthorized();
                }

                var user = await userService.GetUserByEmailAsync(emailClaim.Value);
                var document = new DocumentModel();

                var stocks = stockService.GetAllStocksForValuationReport();
                var newStocks = new List<Stock>();

                foreach (var stock in stocks)
                {
                    var latestStockTake = stock.StockTakes.Where(w => w.Date.Month <= date.Month && w.Date.Year == date.Year).OrderByDescending(d => d.Date).FirstOrDefault();
                    var category = stock.Category;

                    if (latestStockTake != null)
                    {
                        var newStock = new Stock
                        {
                            StockId = stock.StockId,
                            CategoryId = stock.CategoryId,
                            Category = category,
                            Code = stock.Code,
                            Description = stock.Description,
                            PhysicalStock = latestStockTake.Quantity,
                            CostPrice = stock.CostPrice,
                        };
                        newStocks.Add(newStock);
                    } else
                    {
                        var newStock = new Stock
                        {
                            StockId = stock.StockId,
                            CategoryId = stock.CategoryId,
                            Category = category,
                            Code = stock.Code,
                            Description = stock.Description,
                            PhysicalStock = stock.PhysicalStock,
                            CostPrice = stock.CostPrice,
                        };
                        newStocks.Add(newStock);
                    }
                }

                await unitOfWork.SaveChangesAsync();

                var stockIds = newStocks.Select(s => s.StockId).ToArray();

                var stockFifoRecords =
                    await unitOfWork.StockFifo
                                    .Query(q =>
                                        stockIds.Contains(q.StockId) &&
                                        ((q.Created.Year == date.Year && q.Created.Month <= date.Month) || (q.Created.Year == date.Year - 1))
                                     )
                                    .Include(i => i.Stock)
                                    .AsSplitQuery()
                                    .AsNoTracking()
                                    .ToListAsync();

                foreach (var stockFifo in stockFifoRecords)
                {
                    var stock = newStocks.FirstOrDefault(f => f.StockId == stockFifo.StockId);
                    if (stock != null)
                    {
                        stockFifo.Stock = stock;
                    }
                } // I am replacing the database taken Stock items that relate to the Stock Fifo items with the Stock items that have been built above (filteredStocks). Because the filtered stocks have the new PhysicalStock number.


                var groupedFifoRecords = stockFifoRecords.GroupBy(g => g.StockId).ToList();

                var listOfStockFifoModels = new List<StockValuationFifoModel>();

                foreach (var groupedFifos in groupedFifoRecords)
                {
                    var totalStockQuantityEver = groupedFifos.Sum(s => s.Quantity);
                    var currentPhysicalStock = groupedFifos.First().Stock.PhysicalStock;

                    var orderedGroupedFifos = groupedFifos.OrderBy(o => o.Created).ToList();

                    int count = 0;

                    foreach (var fifo in orderedGroupedFifos)
                    {
                        while (currentPhysicalStock > 0)
                        {
                            if (count == 0)
                            {
                                if (orderedGroupedFifos.Count() > 1)
                                {
                                    var numberToCheck = totalStockQuantityEver - fifo.Quantity;
                                    totalStockQuantityEver = totalStockQuantityEver - fifo.Quantity;

                                    if (numberToCheck < currentPhysicalStock)
                                    {
                                        var quantityRemainingThisCurrentFifo = currentPhysicalStock - numberToCheck;

                                        currentPhysicalStock = currentPhysicalStock > fifo.Quantity ? currentPhysicalStock - fifo.Quantity : currentPhysicalStock;

                                        listOfStockFifoModels.Add(new StockValuationFifoModel
                                        {
                                            Stock = fifo.Stock,
                                            Quantity = quantityRemainingThisCurrentFifo > 0 ? quantityRemainingThisCurrentFifo : 0,
                                            FifoPrice = fifo.CostPrice.Value
                                        });

                                        count++;
                                        break;
                                    } else
                                    {
                                        break;
                                    }
                                } else
                                {
                                    var quantityRemainingThisCurrentFifo = currentPhysicalStock;

                                    currentPhysicalStock = currentPhysicalStock - quantityRemainingThisCurrentFifo;

                                    listOfStockFifoModels.Add(new StockValuationFifoModel
                                    {
                                        Stock = fifo.Stock,
                                        Quantity = quantityRemainingThisCurrentFifo,
                                        FifoPrice = fifo.CostPrice.Value
                                    });

                                    count++;
                                    break;
                                }
                            } else
                            {
                                currentPhysicalStock = currentPhysicalStock > fifo.Quantity ? currentPhysicalStock - fifo.Quantity : currentPhysicalStock;

                                listOfStockFifoModels.Add(new StockValuationFifoModel
                                {
                                    Stock = fifo.Stock,
                                    Quantity = fifo.Quantity,
                                    FifoPrice = fifo.CostPrice.Value,
                                });

                                break;
                            }
                        }

                        if (fifo == orderedGroupedFifos.Last())
                        {
                            break;
                        }
                    }
                }

                var groupedStockFifoModels = listOfStockFifoModels.GroupBy(c => c.Stock).ToList();

                foreach (var fifo in groupedStockFifoModels)
                {
                    //if (fifo.Any(c => c.Stock.Code == "10T/SPACER"))
                    //{
                    var totalCount = fifo.Count();
                    var totalQuantity = fifo.Sum(fifo => fifo.Quantity);
                    var totalCostPrice = fifo.Sum(f => f.Quantity * f.FifoPrice);
                    var physicalStock = fifo.Select(c => c.Stock.PhysicalStock).First();
                    var costPrice = totalCostPrice / totalQuantity;
                    var totalNet = totalQuantity * costPrice;
                    var transactionModel = new StockTransactionModel
                    {
                        StockId = fifo.Select(s => s.Stock.StockId).First(),
                        CostPrice = costPrice,
                        Quantity = physicalStock,
                        PreviousStockQuantity = physicalStock,
                        TotalNet = totalNet,
                        Type = "Opening Stock",
                        Status = "IN",
                        AccountCode = "N/A",
                        Created = DateTime.Parse("2022-04-01"),
                        OrderNumber = "N/A",
                        Description = totalCount > 1 ? "Manual Movement through import - Cost price calculated as average" : "Manual Movement through import"
                    };
                    await stockTransactionService.PostAsync(transactionModel);

                    var stockFifo = new StockFifoModel
                    {
                        StockFifoId = Guid.NewGuid(),
                        StockId = fifo.Select(s => s.Stock.StockId).First(),
                        CostPrice = costPrice,
                        Quantity = physicalStock,
                        Created = DateTime.Parse("2022-04-01"),
                        Modified = DateTime.Parse("2022-04-01"),
                        CreatedBy = "System",
                        From = 1,
                        To = physicalStock
                    };

                    await stockFifoService.PostAsync(stockFifo);

                    var stockTakeModel = new StockTake
                    {
                        StockTakeId = Guid.NewGuid(),
                        StockId = fifo.Select(s => s.Stock.StockId).First(),
                        CategoryId = fifo.Select(s => s.Stock.CategoryId.Value).First(),
                        Created = DateTime.Parse("2022-04-01"),
                        Date = DateTime.Parse("2022-04-01"),
                        CreatedBy = "System",
                        Quantity = physicalStock,
                        PhysicalStock = physicalStock,
                        Adjustment = 0
                    };

                    await stockTakeService.PostAsync(stockTakeModel);
                    //}
                }
                return Ok();
            } catch (Exception)
            {
                return BadRequest();
            }
        }

        #endregion

        #region Check Stock Assembly date

        [HttpPost("checkStockAssemblyDates")]
        public async Task<IActionResult> CheckStockAssemblyDates([FromBody] StockAssemblyDateRequest request)
        {
            if (!DateTime.TryParse(request.InvoiceDate, out DateTime parsedInvoiceDate))
            {
                return BadRequest("Invalid invoice date format.");
            }

            var stockIds = request.StockItems.Select(m => m.StockId).ToList();

            var allStockAssemblyMovements = await unitOfWork.StockAssemblyMovement.Query()
                .Include(sam => sam.StockAssemblyMovementItems).ThenInclude(sam => sam.Stock).ToListAsync();

            var stockAssemblyMovements = allStockAssemblyMovements.Where(sam => !sam.IsArchived && sam.StockAssemblyMovementItems.Any(sami => stockIds.Contains(sami.StockId) && sami.Stock.Types.Contains("Assembly"))).ToList();

            if (stockAssemblyMovements != null && stockAssemblyMovements.Count > 0)
            {
                var results = new Dictionary<Guid, bool>();

                foreach (var stockId in stockIds)
                {
                    var relevantMovements = stockAssemblyMovements
                        .Where(sam => sam.StockAssemblyMovementItems.Any(sami => sami.StockId == stockId && sami.Type == "Assemble"))
                        .ToList();

                    if (!relevantMovements.Any())
                    {
                        results[stockId] = true;
                        continue;
                    }

                    var latestMovement = relevantMovements
                        .OrderByDescending(sam => sam.MovementDate)
                        .FirstOrDefault();

                    if (latestMovement == null)
                    {
                        results[stockId] = true;
                        continue;
                    }

                    // Mark as invalid (false) if movement date is after invoice date
                    results[stockId] = !(latestMovement.MovementDate.Date > parsedInvoiceDate.Date);
                }
                return Ok(results);
            } else
            {
                return Ok(new Dictionary<Guid, bool>());
            }
        }

        public class StockAssemblyDateRequest
        {
            public List<OrderItemStockItemInvoiceModel> StockItems { get; set; }
            public string InvoiceDate { get; set; }
        }

        #endregion

        [HttpPost("UpdateFreeStock")]
        public async Task<IActionResult> UpdateFreeStock()
        {
            var stocks = await unitOfWork.Stock.Query()
                .Include(s => s.OrderItemStockItems)
                .ToListAsync();

            foreach (var stock in stocks)
            {
                var saleOrderStock = stock.OrderItemStockItems != null && stock.OrderItemStockItems.Any() ?
                                     stock.OrderItemStockItems.Sum(s => s.Allocated) : 0;

                var allocatedStock = saleOrderStock > stock.PhysicalStock ? stock.PhysicalStock : saleOrderStock;

                stock.FreeStock = Math.Round(stock.PhysicalStock - allocatedStock < 0 ? 0 : stock.PhysicalStock - allocatedStock, 2);
            }

            await unitOfWork.SaveChangesAsync();

            return Ok();
        }

        [HttpPost("updateFIFOStock")]
        public async Task<IActionResult> UpdateFIFOStock()
        {
            try
            {
                // Determine file path
                string baseDir = AppDomain.CurrentDomain.BaseDirectory;
                string contentDir = Path.Combine(baseDir, "Content");
                string excelFilePath = "C:\\Working\\red-20-api\\Red20\\Content\\Stock.xlsx";

                // Validate file exists


                // Read Excel file
                var stockUpdates = ReadExcelFile(excelFilePath);

                if (stockUpdates.Count == 0)
                {
                    return BadRequest("No valid records found in the Excel file.");
                }

                var result = new UpdateResult();

                // Process in batches for better performance
                const int batchSize = 500;

                for (int i = 0; i < stockUpdates.Count; i += batchSize)
                {
                    var batch = stockUpdates.Skip(i).Take(batchSize).ToList();

                    // 1. Update Stock table
                    result.StocksUpdated += await UpdateStockTable(batch);

                    // 2. Update StockTransaction table
                    result.TransactionsUpdated += await UpdateStockTransactionTable(batch);

                    // 3. Update StockFifo table
                    result.FifoRecordsUpdated += await UpdateStockFifoTable(batch);

                    // 4. Update JobInvoiceItem table
                    result.InvoiceItemsUpdated += await UpdateJobInvoiceItemTable(batch);
                }

                return Ok(new
                {
                    Success = true,
                    Message = "Stock price updates completed successfully",
                    UpdatedRecords = result
                });
            } catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    Success = false,
                    Message = "An error occurred while updating stock prices",
                    Error = ex.Message
                });
            }
        }

        // Helper methods
        private List<StockUpdate> ReadExcelFile(string filePath)
        {
            var updates = new List<StockUpdate>();

            using (var workbook = new XLWorkbook(filePath))
            {
                var worksheet = workbook.Worksheet(1); // Assuming first worksheet

                // Determine header row
                var headerRow = worksheet.FirstRowUsed();

                // Find column indexes (case insensitive)
                int stockCodeIndex = -1;
                int costPriceIndex = -1;

                for (int i = 1; i <= headerRow.CellsUsed().Count(); i++)
                {
                    string header = headerRow.Cell(i).Value.ToString().Trim();
                    if (header.Equals("StockCode", StringComparison.OrdinalIgnoreCase))
                        stockCodeIndex = i;
                    else if (header.Equals("CostPrice", StringComparison.OrdinalIgnoreCase))
                        costPriceIndex = i;
                }

                if (stockCodeIndex == -1 || costPriceIndex == -1)
                    throw new Exception("Required columns 'StockCode' and 'CostPrice' not found in Excel.");

                // Read data rows
                foreach (var row in worksheet.RowsUsed().Skip(1)) // Skip header
                {
                    if (!row.Cell(stockCodeIndex).IsEmpty() && !row.Cell(costPriceIndex).IsEmpty())
                    {
                        updates.Add(new StockUpdate
                        {
                            StockCode = row.Cell(stockCodeIndex).Value.ToString().Trim(),
                            CostPrice = double.Parse(row.Cell(costPriceIndex).Value.ToString())
                        });
                    }
                }
            }

            return updates;
        }

        private async Task<int> UpdateStockTable(List<StockUpdate> updates)
        {
            int count = 0;

            foreach (var update in updates)
            {
                var stock = unitOfWork.Stock.Query().Where(s => s.Code == update.StockCode).FirstOrDefault();
                if (stock != null && stock.CostPrice > 0)
                {
                    stock.CostPrice = update.CostPrice;
                    stock.FifoValue = update.CostPrice;
                    unitOfWork.Stock.Update(stock);
                    count++;
                }
            }

            await unitOfWork.SaveChangesAsync();
            return count;
        }

        private async Task<int> UpdateStockTransactionTable(List<StockUpdate> updates)
        {
            int count = 0;
            var date = new DateTime(2025, 3, 31);

            foreach (var update in updates)
            {
                // Get corresponding stock first
                var stock = unitOfWork.Stock.Query().Where(s => s.Code == update.StockCode).FirstOrDefault();
                if (stock != null)
                {
                    // Find transactions for this stock on specified date
                    var transaction = unitOfWork.StockTransaction.Query()
                        .Where(t => t.StockId == stock.StockId && t.Created.Date == date.Date)
                        .FirstOrDefault();

                    if(transaction != null && update.CostPrice > 0)
                    {
                        transaction.CostPrice = update.CostPrice;
                        transaction.TotalNet = update.CostPrice * transaction.Quantity;
                        unitOfWork.StockTransaction.Update(transaction);

                        var previousTransactions = unitOfWork.StockTransaction.Query()
                            .Where(t => t.StockId == stock.StockId && t.Created.Date > date.Date && t.Type != "P (IN)")
                            .ToList();

                        if (previousTransactions.Count > 0)
                        {
                            foreach (var previousTransaction in previousTransactions)
                            {
                                previousTransaction.CostPrice = update.CostPrice;
                                previousTransaction.TotalNet = update.CostPrice * previousTransaction.Quantity;
                                unitOfWork.StockTransaction.Update(previousTransaction);
                            }
                        }

                        count++;
                    }
                }
            }

            await unitOfWork.SaveChangesAsync();
            return count;
        }

        private async Task<int> UpdateStockFifoTable(List<StockUpdate> updates)
        {
            int count = 0;

            foreach (var update in updates)
            {
                var stock = unitOfWork.Stock.Query().Where(s => s.Code == update.StockCode).FirstOrDefault();
                if (stock != null && update.CostPrice > 0)
                {
                    var fifoRecords = unitOfWork.StockFifo.Query()
                        .Where(f => f.StockId == stock.StockId && !f.IsArchived)
                        .ToList();

                    foreach (var fifo in fifoRecords)
                    {
                        fifo.CostPrice = update.CostPrice;
                        unitOfWork.StockFifo.Update(fifo);
                        count++;
                    }
                }
            }

            await unitOfWork.SaveChangesAsync();
            return count;
        }

        private async Task<int> UpdateJobInvoiceItemTable(List<StockUpdate> updates)
        {
            int count = 0;
            var cutoffDate = new DateTime(2025, 3, 31);

            foreach (var update in updates)
            {
                var invoiceItems = unitOfWork.JobInvoiceItem.Query()
                    .Where(i => i.StockCode == update.StockCode && i.InvoiceDate > cutoffDate)
                    .ToList();

                foreach (var item in invoiceItems)
                {
                    if (update.CostPrice > 0)
                    {
                    item.UnitPrice = update.CostPrice;
                    item.TotalGross = update.CostPrice * item.Quantity;

                    // Handle VAT calculations if VAT is 20%
                    if (item.Vat.Contains("20"))
                    {
                        item.TotalNet = item.TotalGross / 1.2;
                        item.TotalVat = item.TotalGross - item.TotalNet;
                    }

                    unitOfWork.JobInvoiceItem.Update(item);
                    count++;
                    }
                }
            }

            await unitOfWork.SaveChangesAsync();
            return count;
        }

        // Helper classes
        private class StockUpdate
        {
            public string StockCode { get; set; }
            public double CostPrice { get; set; }
        }

        private class UpdateResult
        {
            public int StocksUpdated { get; set; }
            public int TransactionsUpdated { get; set; }
            public int FifoRecordsUpdated { get; set; }
            public int InvoiceItemsUpdated { get; set; }
            public int TotalRecordsUpdated => StocksUpdated + TransactionsUpdated + FifoRecordsUpdated + InvoiceItemsUpdated;
        }

        [HttpPost("updateStockFifoCostPrice")]
        [ProducesResponseType(200, Type = typeof(UpdateResult))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> UpdateStockFifoCostPrice()
        {
            // Fetch relevant transactions
            var transactions = await unitOfWork.StockTransaction.Query()
                .Include(t => t.Stock)
                .Where(t => t.Type == "P (IN)" && t.Created.Date > new DateTime(2025, 3, 31) && t.Created.Date < new DateTime(2025, 5, 1))
                .ToListAsync();

            // Fetch relevant stock FIFOs
            var stockFifos = await unitOfWork.StockFifo.Query()
                .Where(f => f.Created.Date > new DateTime(2025, 3, 31) && f.Created.Date < new DateTime(2025, 5, 1))
                .ToListAsync();

            // Group transactions by stock ID and date for easier lookup
            var transactionLookup = transactions
                .GroupBy(t => new { t.StockId, Date = t.Created.Date })
                .ToDictionary(g => g.Key, g => g.ToList());

            // Update FIFO cost prices
            int updatedCount = 0;
            foreach (var fifo in stockFifos)
            {
                var key = new { StockId = fifo.StockId, Date = fifo.Created.Date };
                if (transactionLookup.TryGetValue(key, out var matchingTransactions))
                {
                    // Find the matching transaction for this FIFO
                    var matchingTransaction = matchingTransactions.FirstOrDefault();
                    if (matchingTransaction != null && fifo.CostPrice != matchingTransaction.CostPrice)
                    {
                        fifo.CostPrice = matchingTransaction.CostPrice;
                        unitOfWork.StockFifo.Update(fifo);
                        updatedCount++;
                    }
                }
            }

            // Save changes
            await unitOfWork.SaveChangesAsync();

            return Ok(new { Message = $"Updated {updatedCount} FIFO records" });
        }
    }
}