﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Red20.Data.Migrations
{
    public partial class UserTimesheetEntry : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_UserTimesheets_Orders_OrderId",
                table: "UserTimesheets");

            migrationBuilder.DropIndex(
                name: "IX_UserTimesheets_OrderId",
                table: "UserTimesheets");

            migrationBuilder.DropColumn(
                name: "OrderId",
                table: "UserTimesheets");

            migrationBuilder.AddColumn<string>(
                name: "Location",
                table: "UserTimesheets",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "OvertimePeriod",
                table: "UserTimesheets",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "OvertimeYear",
                table: "UserTimesheets",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ResourceCode",
                table: "UserTimesheets",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "WeekendDate",
                table: "UserTimesheets",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "UserTimesheetEntries",
                columns: table => new
                {
                    UserTimesheetEntryId = table.Column<Guid>(nullable: false),
                    UserTimesheetId = table.Column<Guid>(nullable: false),
                    OrderId = table.Column<Guid>(nullable: false),
                    OrderDescription = table.Column<string>(nullable: true),
                    CostCode = table.Column<string>(nullable: true),
                    ClientCode = table.Column<string>(nullable: true),
                    Description = table.Column<string>(nullable: true),
                    PayType = table.Column<string>(nullable: true),
                    DimensionPayType = table.Column<string>(nullable: true),
                    Monday = table.Column<int>(nullable: true),
                    Tuesday = table.Column<int>(nullable: true),
                    Wednesday = table.Column<int>(nullable: true),
                    Thursday = table.Column<int>(nullable: true),
                    Friday = table.Column<int>(nullable: true),
                    Saturday = table.Column<int>(nullable: true),
                    Sunday = table.Column<int>(nullable: true),
                    TotalHours = table.Column<int>(nullable: false),
                    Created = table.Column<DateTime>(nullable: false),
                    Modified = table.Column<DateTime>(nullable: false),
                    CreatedBy = table.Column<string>(nullable: true),
                    ModifiedBy = table.Column<string>(nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserTimesheetEntries", x => x.UserTimesheetEntryId);
                    table.ForeignKey(
                        name: "FK_UserTimesheetEntries_Orders_OrderId",
                        column: x => x.OrderId,
                        principalTable: "Orders",
                        principalColumn: "OrderId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_UserTimesheetEntries_UserTimesheets_UserTimesheetId",
                        column: x => x.UserTimesheetId,
                        principalTable: "UserTimesheets",
                        principalColumn: "UserTimesheetId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_UserTimesheetEntries_OrderId",
                table: "UserTimesheetEntries",
                column: "OrderId");

            migrationBuilder.CreateIndex(
                name: "IX_UserTimesheetEntries_UserTimesheetId",
                table: "UserTimesheetEntries",
                column: "UserTimesheetId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "UserTimesheetEntries");

            migrationBuilder.DropColumn(
                name: "Location",
                table: "UserTimesheets");

            migrationBuilder.DropColumn(
                name: "OvertimePeriod",
                table: "UserTimesheets");

            migrationBuilder.DropColumn(
                name: "OvertimeYear",
                table: "UserTimesheets");

            migrationBuilder.DropColumn(
                name: "ResourceCode",
                table: "UserTimesheets");

            migrationBuilder.DropColumn(
                name: "WeekendDate",
                table: "UserTimesheets");

            migrationBuilder.AddColumn<Guid>(
                name: "OrderId",
                table: "UserTimesheets",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.CreateIndex(
                name: "IX_UserTimesheets_OrderId",
                table: "UserTimesheets",
                column: "OrderId");

            migrationBuilder.AddForeignKey(
                name: "FK_UserTimesheets_Orders_OrderId",
                table: "UserTimesheets",
                column: "OrderId",
                principalTable: "Orders",
                principalColumn: "OrderId",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
