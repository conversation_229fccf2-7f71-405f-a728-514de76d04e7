﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Red20.Excel.Export;
using Red20.Model.Data;
using Red20.Model.Data.Customer;
using Red20.Model.Data.Document;
using Red20.Model.Entity;
using Red20.Service.Data.Interface;
using Red20.Service.Storage.Interface;
using Red20.Service.Xero.Interface;
using Xero.NetStandard.OAuth2.Model.Accounting;

namespace Red20.Controllers {
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class CustomerCallSheetController : ControllerBase {

        private ICustomerCallSheetService service;
        private ICustomerCallSheetVisitService customerCallSheetVisitService;
        private IXeroService xeroService;
        private IDocumentService documentService;
        private IStorageService blobStorage;
        private IUserService userService;
        private ILogger<AuthController> logger;
        IUnitOfWork unitOfWork;

        public CustomerCallSheetController(
            ICustomerCallSheetService service,
            ICustomerCallSheetVisitService customerCallSheetVisitService,
            IDocumentService documentService,
            IStorageService blobStorage,
            IUserService userService,
            ILogger<AuthController> logger,
            IXeroService xeroService,
            IUnitOfWork unitOfWork ) {

            this.service = service;
            this.logger = logger;
            this.unitOfWork = unitOfWork;
            this.xeroService = xeroService;
            this.documentService = documentService;
            this.blobStorage = blobStorage;
            this.userService = userService;
            this.customerCallSheetVisitService = customerCallSheetVisitService;
        }

        [HttpGet]
        [ProducesResponseType(200, Type = typeof(IList<CustomerCallSheetModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get() {
            var customerCallSheets = await service.GetAllCustomerCallSheets();

            return Ok(customerCallSheets);
        }

        [HttpGet("archive")]
        [ProducesResponseType(200, Type = typeof(IList<CustomerCallSheetModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetArchivedCustomerCallSheets()
        {
            var customerCallSheets = await service.GetAllCustomerCallSheets();
            var archived = customerCallSheets.Where(c => c.ArchivedDate.HasValue).ToList();
            return Ok(archived);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(CustomerCallSheetModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id) {

            var enquiries = unitOfWork.Enquiry.Query().Include(c => c.Customer);
            var quotes = unitOfWork.Quote.Query().Include(c => c.Customer);

            var customerCallSheet = await service.GetAsync(id);

            customerCallSheet.TotalEnquiries = enquiries.Where(c => c.Customer.Name == customerCallSheet.NewCustomer).Count();
            customerCallSheet.TotalQuotes = quotes.Where(c => c.Customer.Name == customerCallSheet.NewCustomer && !c.IsVoid).Count();

            return Ok(customerCallSheet);
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(CustomerCallSheetModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody]CustomerCallSheetUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                       c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);

            model.CreatedBy = $"{user.Firstname} {user.Lastname}";
            model.CreatedByEmail = user.EmailAddress;

            model.FollowUpDate = DateTime.UtcNow.AddDays(model.FollowUp.Value);

            var customerCallSheet = await service.PostAsync(model);

            return Ok(customerCallSheet);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(CustomerCallSheetModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Put(Guid id, [FromBody]CustomerCallSheetUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }
            model.FollowUpDate = DateTime.UtcNow.AddDays(model.FollowUp.Value);
            var customerCallSheet = await service.PutAsync(id, model);

            return Ok(customerCallSheet);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Delete(Guid id) {

            await service.DeleteAsync(id);
            return Ok();
        }

        #region Customer Call Sheet Vist

        [HttpGet("byCustomerCallSheet/{id}")]
        [ProducesResponseType(200, Type = typeof(CustomerCallSheetVisitModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetByCustomerCallSheetId(Guid id)
        {

            var customerCallSheetVisits = customerCallSheetVisitService.GetByCustomerCallSheetId(id);

            return Ok(customerCallSheetVisits);
        }

        [HttpPost("saveVisit")]
        [ProducesResponseType(200, Type = typeof(CustomerCallSheetVisitModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> PostCustomerCallSheetVisit([FromBody] CustomerCallSheetVisitUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);

            model.CreatedBy = $"{user.Firstname} {user.Lastname}";

            var customerCallSheetVisit = await customerCallSheetVisitService.PostAsync(model);

            return Ok(customerCallSheetVisit);
        }

        [HttpPut("updateVisit/{id}")]
        [ProducesResponseType(200, Type = typeof(CustomerCallSheetVisitModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> PutCustomerCallSheetVisit(Guid id, [FromBody] CustomerCallSheetVisitUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);

            model.CreatedBy = $"{user.Firstname} {user.Lastname}";

            var customerCallSheetVisit = await customerCallSheetVisitService.PutAsync(id, model);

            return Ok(customerCallSheetVisit);
        }

        [HttpDelete("deleteVisit/{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> DeleteCustomerCallSheetVisit(Guid id)
        {
            await customerCallSheetVisitService.DeleteAsync(id);
            return Ok();
        }

        #endregion
    }
}