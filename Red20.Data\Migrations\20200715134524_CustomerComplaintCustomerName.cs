﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace Red20.Data.Migrations
{
    public partial class CustomerComplaintCustomerName : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CustomerName",
                table: "CustomerComplaints");

            migrationBuilder.DropColumn(
                name: "JobNumber",
                table: "CustomerComplaints");

            migrationBuilder.AddColumn<string>(
                name: "CustomerComplaintCustomerName",
                table: "CustomerComplaints",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CustomerComplaintJobNumber",
                table: "CustomerComplaints",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CustomerComplaintCustomerName",
                table: "CustomerComplaints");

            migrationBuilder.DropColumn(
                name: "CustomerComplaintJobNumber",
                table: "CustomerComplaints");

            migrationBuilder.AddColumn<string>(
                name: "CustomerName",
                table: "CustomerComplaints",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "JobNumber",
                table: "CustomerComplaints",
                type: "nvarchar(max)",
                nullable: true);
        }
    }
}
