﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Red20.Model.Data.Order;
using Red20.Model.Data.Stock;
using Red20.Service.Data.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Red20.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class OrderItemStockItemController : ControllerBase
    {
        private IOrderItemJobRecordSheetService jobRecordSheetService;
        private IOrderItemStockItemService service;
        private IStockService stockService;
        private IOrderItemService orderItemService;
        private IOrderService orderService;
        private IJobService jobService;
        private IOrderStockAllocationService orderStockAllocationService;
        private ILogger<AuthController> logger;
        private IUnitOfWork unitOfWork;

        public OrderItemStockItemController(
            IOrderItemStockItemService service,
            IOrderItemJobRecordSheetService jobRecordSheetService,
            IStockService stockService,
            ILogger<AuthController> logger,
            IOrderItemService orderItemService,
            IOrderService orderService,
            IJobService jobService,
            IOrderStockAllocationService orderStockAllocationService,
            IUnitOfWork unitOfWork)
        {
            this.service = service;
            this.jobRecordSheetService = jobRecordSheetService;
            this.stockService = stockService;
            this.logger = logger;
            this.orderItemService = orderItemService;
            this.orderService = orderService;
            this.jobService = jobService;
            this.orderStockAllocationService = orderStockAllocationService;
            this.unitOfWork = unitOfWork;
        }

        [HttpGet("byOrderItem/{id}")]
        [ProducesResponseType(200, Type = typeof(IList<OrderItemStockItemModel>))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id)
        {
            return Ok(await service.GetByOrderItemAsync(id));
        }

        [HttpGet("byOrderItemForClone/{id}")]
        [ProducesResponseType(200, Type = typeof(IList<OrderItemStockItemModel>))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetForOrderItemClone(Guid id)
        {
            var results = new List<OrderItemStockItemModel>();
            var orderItemStockItems = await service.GetByOrderItemForCloneAsync(id);

            if (orderItemStockItems != null && orderItemStockItems.Any())
            {
                foreach (var orderItemStockItem in orderItemStockItems)
                {
                    var stock = await stockService.GetAsync(orderItemStockItem.StockId);
                    results.Add(new OrderItemStockItemModel
                    {
                        OrderItemStockItemId = orderItemStockItem.OrderItemStockItemId,
                        OrderItemId = orderItemStockItems.First().OrderItemId,
                        Stock = stock,
                        Quantity = orderItemStockItem.Quantity,
                        StockCode = orderItemStockItem.StockCode,
                        StockId = orderItemStockItem.StockId,
                        Unallocated = orderItemStockItem.Unallocated
                    });
                }
            }

            return Ok(results);
        }

        [HttpGet("byOrderItemForJobRecordSheet/{id}")]
        [ProducesResponseType(200, Type = typeof(IList<OrderItemStockItemModel>))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetJobRecordSheetItems(Guid id)
        {
            return Ok(await service.GetJobRecordSheetStockItemsAsync(id, "jobRecordSheet"));
        }

        [HttpGet("byOrderItemForWorkSiteSheet/{id}")]
        [ProducesResponseType(200, Type = typeof(IList<OrderItemStockItemModel>))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetWorkSiteSheetItems(Guid id)
        {
            return Ok(await service.GetWorkSiteSheetStockItemsAsync(id, "workSiteSheet"));
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(OrderItemStockItemModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody] OrderItemStockItemUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }

            var stock = await unitOfWork.Stock.Query(c => c.StockId == model.StockId).Include(c => c.OrderItemStockItems).FirstOrDefaultAsync();

            var allocatedStock = stock.OrderItemStockItems != null && stock.OrderItemStockItems.Any() ?
                        stock.OrderItemStockItems.Sum(s => s.Allocated) : 0;

            stock.FreeStock = stock.PhysicalStock - allocatedStock;

            if (stock is null)
            {
                return BadRequest();
            }

            var jobRecordSheetStockItem = await service.GetByOrderItemAndStockAsync(model.OrderItemId, stock.StockId, model.SheetType);
            if (jobRecordSheetStockItem != null)
            {
                var modelAllocated = model.Quantity > stock.FreeStock ? stock.FreeStock : model.Quantity;
                var modelUnallocated = model.Quantity - stock.FreeStock > 0 ? model.Quantity - stock.FreeStock : 0;

                jobRecordSheetStockItem.Allocated = jobRecordSheetStockItem.Allocated + modelAllocated;
                jobRecordSheetStockItem.Unallocated = jobRecordSheetStockItem.Unallocated + modelUnallocated;
                jobRecordSheetStockItem.Quantity = jobRecordSheetStockItem.Quantity + model.Quantity;
                jobRecordSheetStockItem.SheetType = model.SheetType;

                try
                {
                    jobRecordSheetStockItem = await service.PutAsync(jobRecordSheetStockItem.OrderItemStockItemId, jobRecordSheetStockItem);
                } catch (Exception ex)
                {
                    logger.LogError($"Cannot update Order Item Stock Item - {ex}");
                    return BadRequest();
                }
            } else
            {
                model.StockCode = stock.Code;
                model.Description = stock.Description;
                model.Allocated = model.Quantity > stock.FreeStock ? stock.FreeStock : model.Quantity;
                model.Unallocated = model.Quantity - stock.FreeStock > 0 ? model.Quantity - stock.FreeStock : 0;

                try
                {
                    jobRecordSheetStockItem = await service.PostAsync(model);
                } catch (Exception ex)
                {
                    logger.LogError($"Cannot create stock item - {ex}");
                }

                var freeStock = stock.FreeStock - model.Allocated;

                stock.FreeStock = freeStock > 0 ? freeStock : 0;

                unitOfWork.Stock.Update(stock);
            }

            try
            {
                var orderItem = await orderItemService.GetAsyncWithStockItems(model.OrderItemId);
                if (orderItem != null)
                {
                    var order = await orderService.GetAsync(orderItem.OrderId);
                    var orderItems = await orderItemService.GetByOrderAsync(order.OrderId);
                    var existingToDeliver = jobRecordSheetStockItem.Quantity;

                    orderItems = orderItems.Where(w => w.OrderItemId != orderItem.OrderItemId).ToList();

                    if (orderItems != null && orderItems.Any())
                    {
                        foreach (var item in orderItems)
                        {
                            if (item.OrderItemStockItems != null && item.OrderItemStockItems.Any())
                            {
                                if (item.OrderItemStockItems.Any(a => a.StockId == stock.StockId))
                                {
                                    existingToDeliver = existingToDeliver + item.OrderItemStockItems.Where(w => w.StockId == stock.StockId).Sum(s => s.Quantity);
                                }
                            }
                        }
                    }

                    var stockAllocation = await orderStockAllocationService.GetByOrderIdAndStockIdAsync(orderItem.OrderId, stock.StockId);
                    var newStockAllocation = new OrderStockAllocationUpdateModel();
                    newStockAllocation.OrderId = orderItem.OrderId;
                    newStockAllocation.StockId = stock.StockId;
                    //newStockAllocation.ExistingAllocation = existingStockAllocation;
                    newStockAllocation.QuantityToDeliver = existingToDeliver;
                    //newStockAllocation.Unallocated = existingUnallocated;
                    newStockAllocation.Allocate = 0.0;
                    newStockAllocation.Invoiced = stockAllocation != null ? stockAllocation.Invoiced : 0.0;

                    stockAllocation = stockAllocation == null ?
                    await orderStockAllocationService.PostAsync(newStockAllocation) :
                    await orderStockAllocationService.PutAsync(stockAllocation.OrderStockAllocationId, newStockAllocation);
                }
                await unitOfWork.SaveChangesAsync();
                return Ok(jobRecordSheetStockItem);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot create Job Record Sheet Stock Item - {ex}");
                return BadRequest();
            }
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(OrderItemStockItemModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Put(Guid id, [FromBody] OrderItemStockItemUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }

            var jobRecordSheetStockItem = await service.GetAsync(id);
            if (jobRecordSheetStockItem is null)
            {
                return BadRequest();
            }

            var stock = await stockService.GetAsync(model.StockId);
            if (stock is null)
            {
                return BadRequest();
            }

            try
            {
                model.Allocated = jobRecordSheetStockItem.Allocated + (model.Quantity > stock.FreeStock ? stock.FreeStock : model.Quantity);
                model.Unallocated = jobRecordSheetStockItem.Unallocated + (model.Quantity - stock.FreeStock > 0 ? model.Quantity - stock.FreeStock : 0);
                model.Quantity = model.Quantity + jobRecordSheetStockItem.Quantity;

                jobRecordSheetStockItem = await service.PutAsync(jobRecordSheetStockItem.OrderItemStockItemId, model);

                var orderItem = await orderItemService.GetAsyncWithStockItems(model.OrderItemId);
                if (orderItem != null)
                {
                    var order = await orderService.GetAsync(orderItem.OrderId);

                    var orderItems = await orderItemService.GetByOrderAsync(order.OrderId);
                    var existingToDeliver = 0.0;

                    if (orderItems != null && orderItems.Any())
                    {
                        foreach (var item in orderItems)
                        {
                            if (item.OrderItemStockItems != null && item.OrderItemStockItems.Any())
                            {
                                if (item.OrderItemStockItems.Any(a => a.StockId == stock.StockId))
                                {
                                    existingToDeliver = existingToDeliver + item.OrderItemStockItems.Where(w => w.StockId == stock.StockId).Sum(s => s.Quantity);
                                }
                            }
                        }
                    }

                    var stockAllocation = await orderStockAllocationService.GetByOrderIdAndStockIdAsync(orderItem.OrderId, stock.StockId);
                    var newStockAllocation = new OrderStockAllocationUpdateModel();
                    newStockAllocation.OrderId = orderItem.OrderId;
                    newStockAllocation.StockId = stock.StockId;
                    newStockAllocation.QuantityToDeliver = existingToDeliver;
                    newStockAllocation.Allocate = 0.0;
                    newStockAllocation.Invoiced = stockAllocation != null ? stockAllocation.Invoiced : 0.0;

                    stockAllocation = stockAllocation == null ?
                    await orderStockAllocationService.PostAsync(newStockAllocation) :
                    await orderStockAllocationService.PutAsync(stockAllocation.OrderStockAllocationId, newStockAllocation);
                }

                return Ok(jobRecordSheetStockItem);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot update Job Record Sheet Stock Item - {ex}");
                return BadRequest();
            }
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Delete(Guid id)
        {
            if (!await service.OrderItemStockItemIdExistsAsync(id))
            {
                return BadRequest();
            }

            var orderItemStockItem = await service.GetAsync(id);

            var stock = await unitOfWork.Stock.GetAsync(orderItemStockItem.StockId);

            stock.FreeStock = stock.FreeStock + orderItemStockItem.Allocated;

            unitOfWork.Stock.Update(stock);

            var orderItem = await orderItemService.GetAsyncWithStockItems(orderItemStockItem.OrderItemId);
            if (orderItem != null)
            {
                var order = await orderService.GetAsync(orderItem.OrderId);

                await service.DeleteAsync(id);

                var orderItems = await orderItemService.GetByOrderAsync(order.OrderId);
                var existingToDeliver = 0.0;

                bool noStockLeft = true;
                if (orderItems != null && orderItems.Any())
                {
                    foreach (var item in orderItems)
                    {
                        if (item.OrderItemStockItems != null && item.OrderItemStockItems.Any())
                        {
                            if (item.OrderItemStockItems.Any(a => a.StockId == stock.StockId && a.Invoiced < 1))
                            {
                                existingToDeliver = existingToDeliver + item.OrderItemStockItems.Where(w => w.StockId == stock.StockId && w.Invoiced < 1).Sum(s => s.Quantity);
                                noStockLeft = false;
                            }
                        }
                    }
                }

                var stockAllocation = await orderStockAllocationService.GetByOrderIdAndStockIdAsync(orderItem.OrderId, stock.StockId);
                var newStockAllocation = new OrderStockAllocationUpdateModel();
                newStockAllocation.OrderId = orderItem.OrderId;
                newStockAllocation.StockId = stock.StockId;
                newStockAllocation.QuantityToDeliver = existingToDeliver;
                newStockAllocation.Allocate = 0.0;

                if (noStockLeft && existingToDeliver == 0 && stockAllocation != null)
                {
                    await orderStockAllocationService.DeleteAsync(stockAllocation.OrderStockAllocationId);
                } else
                {
                    stockAllocation = stockAllocation == null ?
                        await orderStockAllocationService.PostAsync(newStockAllocation) :
                        await orderStockAllocationService.PutAsync(stockAllocation.OrderStockAllocationId, newStockAllocation);
                }
            }

            await unitOfWork.SaveChangesAsync();

            return Ok();
        }
    }
}