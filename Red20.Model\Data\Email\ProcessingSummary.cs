﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Red20.Model.Data.Email
{
    public class ProcessingSummary
    {
        public int TotalOrders { get; set; }
        public int SuccessfulInvoices { get; set; }
        public int FailedInvoices { get; set; }
        public List<InvoiceProcessingResult> Results { get; set; } = new List<InvoiceProcessingResult>();
        public DateTime ProcessingStarted { get; set; }
        public DateTime ProcessingCompleted { get; set; }
        public TimeSpan TotalProcessingTime => ProcessingCompleted - ProcessingStarted;
    }
}
