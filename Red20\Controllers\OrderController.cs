﻿
using Hangfire;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Red20.Excel.Export;
using Red20.Model.Data;
using Red20.Model.Data.Account;
using Red20.Model.Data.Document;
using Red20.Model.Data.Job;
using Red20.Model.Data.Order;
using Red20.Model.Data.Quote;
using Red20.Service;
using Red20.Service.Data.Interface;
using Red20.Service.Email.Interface;
using Red20.Service.Storage.Interface;
using Red20.Service.Xero.Interface;
using Red20.Utility;
using Syncfusion.Pdf;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Claims;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Red20.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class OrderController : ControllerBase
    {

        private static readonly List<string> saleOrderAccountCodes = new List<string> { "1000", "1010", "1030", "1001", "1011", "1032", "1083", "1082", "1008", "1002", "1050", "1003", "1004", "1014", "1005", "1009", "1015", "1061", "1095" };
        private static readonly List<string> saleInternalOrderAccountCodes = new List<string> { "1000", "1006", "1007", "1010", "1016", "1020", "1025", "1030", "1095", "1001", "1011", "1016", "1032", "1083", "1082", "1008", "1002", "1050", "1003", "1004", "1014", "1005", "1009", "1015",
        "0017","0076", "0037", "3000","3001","3002","3003","3004","3005","3006","3007","3008","3009","3010","3011","3012","3013","3014","3015","3016","3017","3018","3019","3020","3021","3022","3023","3024","3025","3026","3027","3028","3030","3031",
            "3032","3033","3034","3035","3037","3038","3040","3041","3042","3043","3044","3045","3046","3047","3050","3051","3052","3053","3054","3055","3056","3057","3058","3060","3061","3062","3063","3064","3065","3066","3067","3068","3072",
            "3073","3074","3075","3076","3077","3078","3079","3080","3081","3082","3083","3084","3085","3086","3088","3089","3090","3091","3092","3093","3094","3095","3096","3097","3098","3099","3100","3106","3107","3110","3111","3112","3113","3114",
            "3115", "3116","3117","3118", "3119", "3120", "3200","3206","3208","3211","3212","3213","3214","3215","3216","3217","3218","3219","3220","3221","3300","3400","3401","3402"};
        private static readonly List<string> hireOrderAccountCodes = new List<string> { "1006", "1016", "1007", "1020", "1021", "1025", "1086", "1019", "1095" };

        private IOrderService service;
        private IQuoteService quoteService;
        private IQuoteItemService quoteItemService;
        private IUserService userService;
        private ILogger<AuthController> logger;
        private ReportService reportService;
        private IStorageService blobStorage;
        private IDocumentService documentService;
        private IEmailService emailService;
        private ICustomerService customerService;
        private ICustomerAddressService customerAddressService;
        private IPurchaseOrderService purchaseOrderService;
        private IPurchaseOrderItemService purchaseOrderItemService;
        private IPurchaseRequisitionService purchaseRequisitionService;
        private ICustomerComplaintService customerComplaintService;
        private IOrderAssemblyService assemblyService;
        private IJobService jobService;
        private IJobInvoiceService jobInvoiceService;
        private IUserTimesheetEntryService userTimesheetEntryService;
        private IUserTimesheetService userTimesheetService;
        private ICurrencyRateService currencyRateService;
        IHireEquipmentService hireEquipmentService;
        IXeroService xeroService;
        IUnitOfWork unitOfWork;
        private IAccountCodeService accountCodeService;

        public OrderController(
            IOrderService service,
            IQuoteService quoteService,
            IQuoteItemService quoteItemService,
            IUserService userService,
            IUnitOfWork unitOfWork,
            ILogger<AuthController> logger,
            ReportService reportService,
            IStorageService blobStorage,
            IDocumentService documentService,
            IEmailService emailService,
            ICustomerService customerService,
            ICustomerAddressService customerAddressService,
            IPurchaseOrderService purchaseOrderService,
            IPurchaseRequisitionService purchaseRequisitionService,
            IOrderAssemblyService assemblyService,
            IHireEquipmentService hireEquipmentService,
            ICustomerComplaintService customerComplaintService,
            IJobInvoiceService jobInvoiceService,
            IXeroService xeroService,
            IPurchaseOrderItemService purchaseOrderItemService,
            IJobService jobService,
            IUserTimesheetEntryService userTimesheetEntryService,
            IUserTimesheetService userTimesheetService,
            IAccountCodeService accountCodeService,
            ICurrencyRateService currencyRateService)
        {

            this.accountCodeService = accountCodeService;
            this.service = service;
            this.quoteService = quoteService;
            this.quoteItemService = quoteItemService;
            this.userService = userService;
            this.unitOfWork = unitOfWork;
            this.reportService = reportService;
            this.blobStorage = blobStorage;
            this.documentService = documentService;
            this.emailService = emailService;
            this.logger = logger;
            this.customerService = customerService;
            this.customerAddressService = customerAddressService;
            this.purchaseOrderService = purchaseOrderService;
            this.purchaseOrderItemService = purchaseOrderItemService;
            this.purchaseRequisitionService = purchaseRequisitionService;
            this.assemblyService = assemblyService;
            this.jobService = jobService;
            this.hireEquipmentService = hireEquipmentService;
            this.customerComplaintService = customerComplaintService;
            this.jobInvoiceService = jobInvoiceService;
            this.xeroService = xeroService;
            this.userTimesheetEntryService = userTimesheetEntryService;
            this.userTimesheetService = userTimesheetService;
            this.currencyRateService = currencyRateService;
        }

        [HttpGet("{type:alpha}")]
        [ProducesResponseType(200, Type = typeof(IList<OrderModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(string type)
        {
            var orders = await service.GetAllAsync(type);
            return Ok(orders);
        }

        [HttpGet("lookups")]
        [ProducesResponseType(200, Type = typeof(IList<OrderLookupModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetByType()
        {
            var orders = await service.GetOrderIdsAndNumbersAsync();
            return Ok(orders);
        }

        [HttpGet("byTypeForPurchaseRequisition/{type:alpha}")]
        [ProducesResponseType(200, Type = typeof(IList<OrderModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAllForPurchaseRequisition(string type)
        {
            return Ok(await service.GetAllForPurchaseRequisition(type));
        }

        [HttpGet("getForGrid/{type:alpha}")]
        [ProducesResponseType(200, Type = typeof(PaginatedResult<OrderModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetForGrid(string type, [FromQuery] OrderFilterParams filterParams)
        {
            var paginatedOrders = await service.GetPaginatedOrdersAsync(type, filterParams);
            return Ok(paginatedOrders);
        }


        [HttpGet("accountCodesForQuotes/{type:alpha}/{isInternal}")]
        [ProducesResponseType(200, Type = typeof(IList<AccountCodeLookupModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAccountCodesForQuotes(string type, bool isInternal)
        {

            var requiredCodes = type == "Sale" && isInternal ? saleInternalOrderAccountCodes : type == "Sale" && !isInternal ? saleOrderAccountCodes : hireOrderAccountCodes;
            var accountCodes = await accountCodeService.GetAllAsync();
            var result = accountCodes.Where(w => requiredCodes.Contains(w.Code)).Select(s => new AccountCodeLookupModel
            {
                Code = s.Code,
                Name = s.Name
            }).OrderBy(o => o.Code).ToList();
            return Ok(result);
        }

        [HttpGet("accountCodes/{type:alpha}/{isInternal}")]
        [ProducesResponseType(200, Type = typeof(IList<Red20.Model.Data.Lookup.AccountCodeModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAccountCodes(string type, bool isInternal)
        {
            var requiredCodes = type == "Sale" && isInternal ? saleInternalOrderAccountCodes : type == "Sale" && !isInternal ? saleOrderAccountCodes : hireOrderAccountCodes;
            var accountCodes = await accountCodeService.GetAllAsync();
            var result = accountCodes.Where(w => requiredCodes.Contains(w.Code)).Select(s => new AccountCodeLookupModel
            {
                Code = s.Code,
                Name = s.Name
            }).OrderBy(o => o.Code).ToList();
            return Ok(result);
        }

        [HttpGet("quoteAccountCodes/{type}")]
        [ProducesResponseType(200)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetQuoteAccountCodes(string type)
        {
            var accountCodes = await accountCodeService.GetAllAsync();
            var saleAccountCodes = type == "Sale" ? accountCodes.Where(c => Int32.Parse(c.Code) >= 1000 && Int32.Parse(c.Code) <= 1999).ToList() : accountCodes;
            return Ok(saleAccountCodes);
        }

        [HttpGet("getAll")]
        [ProducesResponseType(200, Type = typeof(IList<OrderModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAll()
        {
            var orders = await service.GetAllOrders();
            return Ok(orders);
        }

        [HttpGet("getAllForPurchaseRequisition")]
        [ProducesResponseType(200, Type = typeof(IList<OrderModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAllForPurchaseRequisition()
        {
            var orders = await service.GetAllOrdersForPurchaseRequisition();
            return Ok(orders);
        }

        [HttpGet("getAllOrders")]
        [ProducesResponseType(200, Type = typeof(IList<AllOrderPurchaseReqModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAllOrders()
        {
            var orders = await service.GetOrders();
            return Ok(orders);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(OrderModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id)
        {
            var order = await service.GetAsync(id);
            if (order != null)
            {
                int number = 1;
                var job = await jobService.GetByOrderIdAsync(order.OrderId);
                if (job != null)
                {
                    var jobInvoices = await jobInvoiceService.GetByJobIdAsync(job.JobId);
                    number = jobInvoices.Any(a => a.JobType == "Sale" || a.JobType == "Hire") ? jobInvoices.Where(a => a.JobType == "Sale" || a.JobType == "Hire").Count() + 1 : number;
                }
                order.NextInvoiceNumber = $"{order.Number}/{number}";
            }
            return Ok(order);
        }

        [HttpGet("customerComplaintOrder/{id}")]
        [ProducesResponseType(200, Type = typeof(OrderModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetOrder(Guid id)
        {
            var order = await unitOfWork.Order.Query().Include(c => c.Customer).Where(c => c.OrderId == id).FirstOrDefaultAsync();

            return Ok(order);
        }

        [HttpGet("byNumber/{number}")]
        [ProducesResponseType(200, Type = typeof(OrderModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetByNumber(string number)
        {
            var order = await service.GetByNumber(number);
            return Ok(order);
        }

        [HttpGet("purchaseOrderCount/{id}")]
        [ProducesResponseType(200, Type = typeof(int))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetPurchaseOrderCount(Guid id)
        {

            var purchaseOrderItems = unitOfWork.PurchaseOrderItem.Query(c => c.OrderId == id && !c.PurchaseOrder.ArchivedDate.HasValue).Include(x => x.PurchaseOrder).ToList();
            var groupedPurchaseOrders = purchaseOrderItems.GroupBy(c => c.PurchaseOrder.Number);
            var total = groupedPurchaseOrders.Count();

            return Ok(total);
        }

        [HttpGet("archivedPurchaseOrderCount/{id}")]
        [ProducesResponseType(200, Type = typeof(int))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetArchivedPurchaseOrderCount(Guid id)
        {

            var purchaseOrderItems = unitOfWork.PurchaseOrderItem.Query(c => c.OrderId == id && c.PurchaseOrder.ArchivedDate.HasValue).Include(x => x.PurchaseOrder).ToList();
            var groupedPurchaseOrders = purchaseOrderItems.GroupBy(c => c.PurchaseOrder.Number);
            var total = groupedPurchaseOrders.Count();

            return Ok(total);
        }

        [HttpGet("complaintCount/{id}")]
        [ProducesResponseType(200, Type = typeof(int))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetComplaintsTotal(Guid id)
        {
            var totalOrders = await customerComplaintService.GetComplaintsTotal(id);

            return Ok(totalOrders.Count);
        }

        [HttpGet("byQuote/{id}")]
        [ProducesResponseType(200, Type = typeof(IList<OrderModel>))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetByQuote(Guid id)
        {
            var orders = await service.GetByQuote(id);
            return Ok(orders.ToList());
        }

        [HttpGet("orderCountByQuote/{id}")]
        [ProducesResponseType(200, Type = typeof(int))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> OrderCountByQuote(Guid id)
        {
            var orderCount = await service.GetCountByQuote(id);
            return Ok(orderCount);
        }

        [HttpGet("byCustomer/{id}")]
        [ProducesResponseType(200, Type = typeof(IList<OrderModel>))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetByCustomer(Guid id)
        {

            var orders = await service.GetByCustomer(id);

            return Ok(orders);
        }

        [HttpPut("updateDeliveryCharge/{id}")]
        [ProducesResponseType(200, Type = typeof(OrderModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> UpdateDeliveryCharge(Guid id)
        {
            if (!await service.OrderIdExistsAsync(id))
            {
                return NotFound();
            }

            await service.UpdateDeliveryCharge(id);

            return Ok();
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(OrderModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody] OrderUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }

            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                      c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);

            if (user != null)
            {
                model.CreatedBy = $"{user.Firstname} {user.Lastname}";
            }

            var quote = model.QuoteId.HasValue ? await quoteService.GetAsync(model.QuoteId.Value) : null;
            //if(quote is null) {
            //    return BadRequest();
            //}

            if (quote != null)
            {
                quote.QuoteStatus = model.Type;
                quote.ArchivedDate = DateTime.UtcNow;
                await quoteService.SaveQuoteStatusAsync(quote);
            }

            if (model.Internal)
            {
                var name = "Red Rooster Lifting Ltd.";
                var street = "Nauta House";
                var street1 = "The Meadows";
                var town = "Oldmeldrum";
                var county = "Aberdeenshire";
                var country = "United Kingdom";
                var postCode = "AB51 0EZ";

                var customerAddress = await customerAddressService.GetByFields(name, street, street1, town, county, country, postCode);
                var customer = await customerService.GetByName("Red Rooster Lifting Ltd.");
                if (customerAddress == null)
                {
                    if (customer == null)
                    {
                        customer = new CustomerModel();
                        customer.Created = DateTime.UtcNow;
                        customer.Modified = DateTime.UtcNow;
                        customer.Name = "Red Rooster Lifting Ltd.";
                        customer.CreditLimit = 0;
                        customer.IsActive = false;
                        customer.ShowCustomer = false;

                        customer = await customerService.PostRedRoosterAsync(customer);
                    }

                    customerAddress = new CustomerAddressModel();
                    customerAddress.Name = name;
                    customerAddress.Street = street;
                    customerAddress.Street1 = street1;
                    customerAddress.Town = town;
                    customerAddress.County = county;
                    customerAddress.Country = country;
                    customerAddress.PostCode = postCode;
                    customerAddress.CustomerId = customer.CustomerId;
                    customerAddress.Modified = DateTime.UtcNow;
                    customerAddress.Created = DateTime.UtcNow;
                    customerAddress.IsInvoiceDefault = true;
                    customerAddress.IsShipping = false;

                    customerAddress = await customerAddressService.PostRedRoosterAddressAsync(customerAddress);
                }
                model.CustomerInvoiceAddressId = customerAddress.CustomerAddressId;
                model.CustomerShippingAddressId = customerAddress.CustomerAddressId;
                var westBromName = "Red Rooster Lifting Ltd.";
                var westBromStreet = "Unit 26 Kelvin Way Trading Estate";
                var westBromStreet1 = "Kelvin Way";
                var westBromTown = "West Bromwich";
                var westBromCounty = "West Midlands";
                var westBromCountry = "United Kingdom";
                var westBromPostCode = "B70 7TW";

                var westBromShippingAddress = await customerAddressService.GetByFields(westBromName, westBromStreet, westBromStreet1, westBromTown, westBromCounty, westBromCountry, westBromPostCode);
                if (westBromShippingAddress == null)
                {
                    westBromShippingAddress = new CustomerAddressModel();
                    westBromShippingAddress.Name = westBromName;
                    westBromShippingAddress.Street = westBromStreet;
                    westBromShippingAddress.Street1 = westBromStreet1;
                    westBromShippingAddress.Town = westBromTown;
                    westBromShippingAddress.County = westBromCounty;
                    westBromShippingAddress.Country = westBromCountry;
                    westBromShippingAddress.PostCode = westBromPostCode;
                    westBromShippingAddress.CustomerId = customer.CustomerId;
                    westBromShippingAddress.Modified = DateTime.UtcNow;
                    westBromShippingAddress.Created = DateTime.UtcNow;
                    westBromShippingAddress.IsInvoiceDefault = false;
                    westBromShippingAddress.IsShipping = true;

                    westBromShippingAddress = await customerAddressService.PostRedRoosterAddressAsync(westBromShippingAddress);
                }

                model.CustomerId = customer.CustomerId;
                model.Currency = customer.Currency;
            }

            model.OrderCustomerName = await unitOfWork.Customer.Query(q => q.CustomerId == model.CustomerId).Select(s => s.Name).FirstOrDefaultAsync();

            model.Number = await NumberSequenceUtility.GetNextOrderNumber(unitOfWork, user.Location, model.Type, model.Internal);
            try
            {
                var customer = await customerService.GetAsync(model.CustomerId.Value);
                model.Currency = model.Internal ? "Pounds Sterling" : customer.Currency;
                model.TaxCode = customer.TaxCode;
                model.DeliveryChargeType = quote == null ? "N/A" : model.DeliveryChargeType;
                var order = await service.PostAsync(model, user);
                if (quote != null)
                {
                    var quoteItems = await quoteItemService.GetByQuoteIdAsync(quote.QuoteId);
                    if (quoteItems != null && quoteItems.Any())
                    {
                        await service.CloneItems(quoteItems.ToList(), order.OrderId, order.Type, order.Type == "Hire" ? order.HirePeriod : null);
                    }
                }
                return Ok(order);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot create Order - {ex}");
                return BadRequest();
            }
        }

        [HttpPost("applyHireStartDate/{id}/{date}")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> ApplyStartDate(Guid id, DateTime date)
        {
            var orderAssembly = await assemblyService.GetAsync(id);

            if (orderAssembly != null)
            {
                try
                {
                    bool update = await assemblyService.ApplyDate(orderAssembly, date, "start");
                    return Ok();
                } catch (Exception ex)
                {
                    logger.LogError($"Cannot apply start date - {ex}");
                    return BadRequest();
                }
            }

            return Ok();
        }

        [HttpPost("applyHireEndDate/{id}/{date}")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> ApplyEndDate(Guid id, DateTime date)
        {
            var orderAssembly = await assemblyService.GetAsync(id);

            if (orderAssembly != null)
            {
                try
                {
                    bool update = await assemblyService.ApplyDate(orderAssembly, date, "end");
                    return Ok();
                } catch (Exception ex)
                {
                    logger.LogError($"Cannot apply start date - {ex}");
                    return BadRequest();
                }
            }

            return Ok();
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(OrderModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Put(Guid id, [FromBody] OrderUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }

            var order = await service.GetAsync(id);

            if (order is null)
            {
                return BadRequest();
            }

            if (model.CustomerShippingAddressId != null && order.CustomerShippingAddressId != null && model.CustomerShippingAddressId != order.CustomerShippingAddressId)
            {
                var customerId = customerAddressService.GetAsync(model.CustomerShippingAddressId.Value).Result.CustomerId;
                var shippingCustomerName = customerService.GetAsync(customerId).Result.Name;
                model.ShippingAddressCustomerName = shippingCustomerName;
            }

            model.OrderCustomerName = await unitOfWork.Customer.Query(q => q.CustomerId == model.CustomerId).Select(s => s.Name).FirstOrDefaultAsync();

            try
            {
                var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
                if (emailClaim == null)
                {
                    return Unauthorized();
                }

                var user = await userService.GetUserByEmailAsync(emailClaim.Value);
                order = await service.PutAsync(id, model, user);

                return Ok(order);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot update Order - {ex}");
                return BadRequest();
            }
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Delete(Guid id)
        {
            if (!await service.OrderIdExistsAsync(id))
            {
                return BadRequest();
            }
            //await service.DeleteAsync(id);
            return Ok();
        }

        [HttpPut("cancel/{id}/{date}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Cancel(Guid id, DateTime date)
        {
            if (!await service.OrderIdExistsAsync(id))
            {
                return BadRequest();
            }

            await service.CancelAsync(id, date);
            return Ok();
        }

        [HttpPut("complete/{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Complete(Guid id)
        {
            if (!await service.OrderIdExistsAsync(id))
            {
                return BadRequest();
            }

            await service.CompleteAsync(id);
            return Ok();
        }

        [HttpPost("issueOrder")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> IssueOrder(OrderUpdateModel model)
        {
            try
            {
                List<Tuple<string, string, byte[]>> attachments = new List<Tuple<string, string, byte[]>>();
                var order = await service.GetByNumberAsync(model.Number, model.Type);
                var emailAddress = userService.GetByNameAsync(order.CreatedBy.Split(" ")[0], order.CreatedBy.Split(" ")[1]).Result.EmailAddress;
                var orderReportModel = await service.GetOrderReportModel(order.OrderId, emailAddress);
                var stream = reportService.GetOrderReport(orderReportModel, null, orderReportModel.IsIssued is false, orderReportModel.Type == "Hire");

                var orderPdfContentType = "application/pdf";
                var orderPdf = new byte[] { };
                if (stream.Length > 0)
                {
                    using (var memoryStream = new MemoryStream())
                    {
                        stream.CopyTo(memoryStream);
                        orderPdf = memoryStream.ToArray();
                    }
                    attachments.Add(new Tuple<string, string, byte[]>(orderPdfContentType, $"{order.Number}.pdf", orderPdf));
                }

                var user = await userService.GetByNameAsync(order.CreatedBy.Split(" ")[0], order.CreatedBy.Split(" ")[1]);
                await emailService.SendAttachments(
                    order.EmailBody.Replace("\n", "<br/>").Replace("<p></p>", "<br/>").Replace("<p>", "<div class='row'>").Replace("</p>", "</div>"),
                    order.EmailSubject,
                    order.ContactEmailAddress,
                    attachments);

                return Ok();
            } catch (Exception ex)
            {
                logger.LogError($"Cannot email order - {ex}");
                return BadRequest();
            }
        }


        [HttpPost("triggerRecurringHireInvoiceForOrder/{id}")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> HireRecurringInvoiceForOrder(Guid id)
        {
            try
            {
                BackgroundJob.Enqueue<TaskUtility>(task => task.OnHireAssembliesRecurringInvoicePerOrder(id));
                return Ok();
            } catch (Exception ex)
            {
                logger.LogError($"Cannot trigger Recurring invoicing hangfire job - {ex}");
                return BadRequest();
            }
        }


        [HttpGet("printWip/{to}")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> PrintWipReport(DateTime to)
        {
            try
            {
                var document = new DocumentModel();
                var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                      c.Type == ClaimTypes.Email);
                if (emailClaim == null)
                {
                    return Unauthorized();
                }

                var user = await userService.GetUserByEmailAsync(emailClaim.Value);
                var currentUser = $"{user.Firstname} {user.Lastname}";
                List<JobWorkInProgressModel> models = new List<JobWorkInProgressModel>();

                var jobs = await unitOfWork.Job
                    .Query(q => !q.Order.IsCancelled && ((q.Order.InvoiceDate.HasValue && q.Order.InvoiceDate.Value.Date > to.Date) || !q.Order.InvoiceDate.HasValue) && ((q.Order.Type == "Sale" && !q.Order.Internal) || (q.Order.Type == "Hire" && !q.JobInvoices.Any(j => j.JobType == "Hire") && !q.Order.Internal)))
                    .Include(i => i.Order)
                    .Include(i => i.JobInvoices)
                    .ToListAsync();

                if (jobs != null && jobs.Any())
                {
                    foreach (var job in jobs)
                    {
                        //if(job.OrderNumber == "SH000485")
                        //{
                        //    var x = 0;
                        //}
                        var order = await service.GetOrderByIdForWipReportAsync(job.OrderId);

                        if (order != null && ((!order.OrderItems.Any(a => a.InvoiceDate.HasValue && !a.Order.IsCancelled) || order.OrderItems.Any(a => a.InvoiceDate.HasValue && !a.Order.IsCancelled && a.InvoiceDate.Value.Date.Month == DateTime.Now.Month))))
                        {
                            double stockValue = 0.0;
                            double labourValue = 0.0;
                            double purchasesValue = 0.0;
                            double adjValue = 0.0;

                            try
                            {
                                var stockItems = await unitOfWork.JobInvoiceItem.Query(q => q.JobId == job.JobId && q.IsStockMovementItem && q.InvoiceDate <= to).ToListAsync();
                                if (stockItems != null && stockItems.Any())
                                {
                                    foreach (var stockItem in stockItems)
                                    {
                                        var stock = await unitOfWork.Stock.Query(q => q.Code == stockItem.StockCode).FirstOrDefaultAsync();
                                        if (stock != null && stock.CostPrice.HasValue)
                                        {
                                            stockValue = stockValue + (stockItem.Quantity * stock.CostPrice.Value);
                                        }
                                    }
                                }

                                var jobPoInvoices = unitOfWork.JobInvoiceItem.Query(c => c.JobId == job.JobId && c.InvoiceType == "PO" && c.InvoiceDate.Date <= to.Date).ToList();

                                var totalPurchaseOrder = jobPoInvoices.Sum(c => c.TotalNet);
                                purchasesValue = totalPurchaseOrder.Value;

                                var jobInvoiceItems = await unitOfWork.JobInvoiceItem.Query(c => c.JobId == job.JobId && (c.InvoiceType == "ADJUSTMENT IN" || c.InvoiceType == "ADJUSTMENT OUT") && ((!c.AdjustmentDate.HasValue && c.InvoiceDate <= to) || (c.AdjustmentDate.HasValue && c.AdjustmentDate.Value <= to))).ToListAsync();
                                if (jobInvoiceItems.Any())
                                {
                                    foreach (var item in jobInvoiceItems)
                                    {
                                        if (item.InvoiceType == "ADJUSTMENT IN")
                                        {
                                            adjValue = adjValue + item.TotalNet.Value;
                                        } else if (item.InvoiceType == "ADJUSTMENT OUT")
                                        {
                                            adjValue = adjValue - item.TotalNet.Value;
                                        }
                                    }
                                }

                                var orderHourlyTimesheetEntries = await unitOfWork.UserTimesheetEntry.Query(q => q.OrderId == order.OrderId && q.Created.Date <= to.Date && q.PayType == "Hourly Rate").Include(i => i.UserTimesheet).ToListAsync();
                                var orderNonHourlyimesheetEntries = await unitOfWork.UserTimesheetEntry.Query(q => q.OrderId == order.OrderId && q.Created.Date <= to.Date && q.PayType != "Hourly Rate").Include(i => i.UserTimesheet).ToListAsync();

                                //if (order.Number == "SH000485")
                                //{
                                //    var x = 0;
                                //}

                                var jobLabourHourlyInvoices = unitOfWork.JobInvoiceItem.Query(c => c.JobId == job.JobId && c.InvoiceType == "LABOUR" && c.InvoiceDate.Date <= to.Date && c.LabourPayType == "Hourly Rate").ToList();

                                var totalHouryLabour = jobLabourHourlyInvoices.Sum(c => c.TotalNet);
                                labourValue = totalHouryLabour.Value;

                                var jobLabourOvertimeInvoices = unitOfWork.JobInvoiceItem.Query(c => c.JobId == job.JobId && c.InvoiceType == "LABOUR" && c.InvoiceDate.Date < to.Date && c.LabourPayType.Contains("Overtime")).ToList();

                                var totalOvertimeLabour = jobLabourOvertimeInvoices.Sum(c => c.TotalNet);
                                labourValue = labourValue + totalOvertimeLabour.Value;

                                var totalSum = stockValue + purchasesValue + labourValue + adjValue;
                                //if (totalSum > 0) {
                                var model = new JobWorkInProgressModel
                                {
                                    JobNumber = order.Number,
                                    NominalCode = order.AccountCode,
                                    StockTotal = stockValue,
                                    PurchasesTotal = purchasesValue,
                                    LabourTotal = labourValue,
                                    MiscTotal = adjValue,
                                    Total = totalSum
                                };
                                models.Add(model);
                                //}
                            } catch (Exception ex)
                            {
                                var x = ex;
                                return BadRequest();
                            }
                        }
                    }
                }

                var itemData = ExportUtility.ExportWipReport(currentUser, models, to);

                document = await documentService.PostAsync("WIP_Report.xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"{user.Firstname} {user.Lastname}", "Users", null, null, null, false, false, false);
                var file = File(itemData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                await blobStorage.UploadAsync(document.DocumentId, itemData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                return file;
            } catch (Exception)
            {
                return NotFound();
            }
        }
        [HttpPost("export/{type}")]
        [ProducesResponseType(200)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        [DisableRequestSizeLimit]
        public async Task<IActionResult> GenerateReport(List<Guid> orderIds, string type)
        {
            try
            {
                var orders = await service.GetAllForGridAsync(type, orderIds);

                var document = new DocumentModel();
                var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
                if (emailClaim == null)
                {
                    return Unauthorized();
                }

                var user = await userService.GetUserByEmailAsync(emailClaim.Value);
                var currentUser = $"{user.Firstname} {user.Lastname}";
                var itemData = ExportUtility.ExportOrderReport(orders, currentUser, type);
                document = await documentService.PostAsync("Stock_Month_End_Report.xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"{user.Firstname} {user.Lastname}", "Stock Items", null, null, null, false, false, false);
                var file = File(itemData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                await blobStorage.UploadAsync(document.DocumentId, itemData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                return file;
            } catch (Exception)
            {
                return NotFound();
            }
        }

        [HttpPost("exportWithFilters")]
        [ProducesResponseType(200)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        [DisableRequestSizeLimit]
        public async Task<IActionResult> GenerateReportWithFilters([FromBody] OrderExportWithFiltersRequest request)
        {
            try
            {
                var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                  c.Type == ClaimTypes.Email);
                if (emailClaim == null)
                {
                    return Unauthorized();
                }

                var user = await userService.GetUserByEmailAsync(emailClaim.Value);
                var currentUser = $"{user.Firstname} {user.Lastname}";

                // Set page size to max value to get all records
                request.FilterParams.Page = 1;
                request.FilterParams.PageSize = int.MaxValue;

                // Use your existing method but with a large page size
                var result = await service.GetPaginatedOrdersAsync(request.Type, request.FilterParams);
                var orders = result.Data; // This will contain all orders matching the filters

                var itemData = ExportUtility.ExportOrderReport(orders, currentUser, request.Type);
                var document = await documentService.PostAsync(
                    "Order_Export_Report.xlsx",
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    $"{user.Firstname} {user.Lastname}",
                    "Orders",
                    null, null, null, false, false, false
                );

                var file = File(itemData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                await blobStorage.UploadAsync(
                    document.DocumentId,
                    itemData,
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                );

                return file;
            } catch (Exception ex)
            {
                // Consider logging the exception
                return NotFound();
            }
        }

        [HttpGet("downloadReport")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> DownloadReport()
        {
            try
            {
                var lastDocument = await unitOfWork.Document.Query().OrderByDescending(c => c.Created).FirstOrDefaultAsync();
                var download = await blobStorage.DownloadAsync(lastDocument.DocumentId);
                var file = File(download.Item1, download.Item2);
                return file;
            } catch (Exception)
            {
                return NotFound();
            }
        }

        [HttpPost("targetDispatchReport")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> PrintTargetDispatchReport(List<OrderModel> orders)
        {
            //var orders = await service.GetAllAsync(type);
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);
            var currentUser = $"{user.Firstname} {user.Lastname}";

            if (orders != null && orders.Any())
            {
                var itemData = ExportUtility.ExportOrderDispatchReport(orders, currentUser, orders[0].Type);
                return File(itemData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            } else
            {
                return File(new byte[] { }, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            }
        }


        [HttpPost("markOrderComplete/{id}")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> MarkOrderComplete(Guid id)
        {
            var order = await service.GetAsync(id);

            if (order != null)
            {
                try
                {
                    order = await service.MarkAsComplete(id);
                    return Ok(order);
                } catch (Exception ex)
                {
                    logger.LogError($"Cannot mark Order as Complete - {ex}");
                    return BadRequest();
                }
            }

            return Ok(order);
        }

        [HttpGet("jobRecordSheet/{id}")]
        [ProducesResponseType(200, Type = typeof(File))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> OrderJobRecord(Guid id)
        {
            var firstName = "";
            var lastName = "";
            var order = await service.GetAsync(id);
            var createdBy = order.CreatedBy;
            string[] createdBySplit = createdBy.Split(" ");
            if (createdBySplit.Length == 2)
            {
                firstName = !string.IsNullOrWhiteSpace(order.CreatedBy) ? order.CreatedBy.Split(" ")[0] : string.Empty;
                lastName = !string.IsNullOrWhiteSpace(order.CreatedBy) ? order.CreatedBy.Split(" ")[1] : string.Empty;
            } else if (createdBySplit.Length == 3)
            {
                firstName = !string.IsNullOrWhiteSpace(order.CreatedBy) ? $"{order.CreatedBy.Split(" ")[0]} {order.CreatedBy.Split(" ")[1]}" : string.Empty;
                lastName = !string.IsNullOrWhiteSpace(order.CreatedBy) ? order.CreatedBy.Split(" ")[2] : string.Empty;
            }

            var user = await userService.GetByNameAsync(firstName, lastName);
            var orderReportModel = await service.GetOrderReportModel(id, user.EmailAddress);

            List<MemoryStream> streams = new List<MemoryStream>();
            MemoryStream finalStream = new MemoryStream();
            PdfDocument pdfDocument = new PdfDocument();
            pdfDocument.EnableMemoryOptimization = false;
            if (orderReportModel.LineItems != null && orderReportModel.LineItems.Any())
            {
                int itemNumber = 1;

                foreach (var lineItem in orderReportModel.LineItems)
                {
                    orderReportModel.ItemNumber = itemNumber;
                    itemNumber++;

                    var stream = reportService.GetWorksJobRecordSheetLC(orderReportModel);

                    streams.Add(stream);
                }
                PdfDocumentBase.Merge(pdfDocument, streams.ToArray());

                pdfDocument.Save(finalStream);
                pdfDocument.Close(true);
                finalStream.Position = 0;
            }

            return File(finalStream, "application/pdf", $"Works_Job_Record{Regex.Replace(orderReportModel.OrderNumber, "[^a - zA - Z0 - 9_] + ", "")}_{DateTime.Now.ToString("ddMMMyyyy")}.pdf");
        }

        [HttpPut("updateLastInvoicedDate")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> UpdateLastInvoicedDate()
        {
            try
            {
                var orders = await unitOfWork.Order
                    .Query()
                    .Include(c => c.OrderItems)
                    .Where(c => c.InvoiceDate == null && c.Type == "Sale" && !c.Internal && c.OrderItems != null)
                    .ToListAsync();

                foreach (var order in orders)
                {
                    var lastInvoiceDate = order.OrderItems
                        .Where(c => c.InvoiceDate.HasValue)
                        .OrderByDescending(c => c.InvoiceDate)
                        .FirstOrDefault()?.InvoiceDate;

                    if (lastInvoiceDate.HasValue)
                    {
                        order.InvoiceDate = lastInvoiceDate;
                        unitOfWork.Order.Update(order);
                    }
                }

                // Save all changes in a single batch
                await unitOfWork.SaveChangesAsync();

                return Ok();
            } catch (Exception ex)
            {
                logger.LogError($"Cannot update last invoiced date - {ex}");
                return BadRequest();
            }
        }

        public class OrderExportWithFiltersRequest
        {
            public string Type { get; set; }
            public OrderFilterParams FilterParams { get; set; }
        }
    }
}