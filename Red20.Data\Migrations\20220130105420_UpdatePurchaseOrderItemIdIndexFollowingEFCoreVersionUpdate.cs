﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Red20.Data.Migrations
{
    public partial class UpdatePurchaseOrderItemIdIndexFollowingEFCoreVersionUpdate : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
           migrationBuilder.CreateIndex(
                name: "IX_PurchaseOrderItemLogs_PurchaseOrderItemId",
                table: "PurchaseOrderItemLogs",
                column: "PurchaseOrderItemId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_PurchaseOrderItemLogs_PurchaseOrderItemId",
                table: "PurchaseOrderItemLogs");            
        }
    }
}
