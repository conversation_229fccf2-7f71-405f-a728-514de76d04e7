﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Red20.Data.Migrations
{
    public partial class customercallsheetnewcustomername : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CustomerCallSheets_Customers_CustomerId",
                table: "CustomerCallSheets");

            migrationBuilder.AlterColumn<Guid>(
                name: "CustomerId",
                table: "CustomerCallSheets",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");

            migrationBuilder.AddColumn<string>(
                name: "NewCustomerName",
                table: "CustomerCallSheets",
                nullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_CustomerCallSheets_Customers_CustomerId",
                table: "CustomerCallSheets",
                column: "CustomerId",
                principalTable: "Customers",
                principalColumn: "CustomerId",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CustomerCallSheets_Customers_CustomerId",
                table: "CustomerCallSheets");

            migrationBuilder.DropColumn(
                name: "NewCustomerName",
                table: "CustomerCallSheets");

            migrationBuilder.AlterColumn<Guid>(
                name: "CustomerId",
                table: "CustomerCallSheets",
                type: "uniqueidentifier",
                nullable: false,
                oldClrType: typeof(Guid),
                oldNullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_CustomerCallSheets_Customers_CustomerId",
                table: "CustomerCallSheets",
                column: "CustomerId",
                principalTable: "Customers",
                principalColumn: "CustomerId",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
