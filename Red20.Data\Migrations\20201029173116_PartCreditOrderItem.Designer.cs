﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Red20.Data.Context;

namespace Red20.Data.Migrations
{
    [DbContext(typeof(DataContext))]
    [Migration("20201029173116_PartCreditOrderItem")]
    partial class PartCreditOrderItem
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "3.1.0")
                .HasAnnotation("Relational:MaxIdentifierLength", 128)
                .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

            modelBuilder.Entity("Red20.Model.Entity.AnalysisCode", b =>
                {
                    b.Property<Guid>("AnalysisCodeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Code")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("Modified")
                        .HasColumnType("datetime2");

                    b.HasKey("AnalysisCodeId");

                    b.ToTable("AnalysisCodes");
                });

            modelBuilder.Entity("Red20.Model.Entity.Asset", b =>
                {
                    b.Property<Guid>("AssetId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("ArchivedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("AssetCategoryId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("AssetSubCategoryId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("AssetTypeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AssetTypeName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BaseValueBasedOn")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("BoughtDate")
                        .HasColumnType("datetime2");

                    b.Property<double?>("BoughtValue")
                        .HasColumnType("float");

                    b.Property<double?>("CostEndofYear")
                        .HasColumnType("float");

                    b.Property<double?>("CostStartofYear")
                        .HasColumnType("float");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<double?>("CurrentBaseValue")
                        .HasColumnType("float");

                    b.Property<double?>("CurrentCostAdditionalCharge")
                        .HasColumnType("float");

                    b.Property<double?>("CurrentCostStartofYear")
                        .HasColumnType("float");

                    b.Property<double?>("CurrentDepreciationAdditionalCharge")
                        .HasColumnType("float");

                    b.Property<double?>("CurrentDepreciationStartofYear")
                        .HasColumnType("float");

                    b.Property<double?>("DepreciationCharge")
                        .HasColumnType("float");

                    b.Property<double?>("DepreciationEndofYear")
                        .HasColumnType("float");

                    b.Property<string>("DepreciationMethod")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double?>("DepreciationStartofYear")
                        .HasColumnType("float");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("HireEquipmentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<double?>("HistoricalCostAdditionalCharge")
                        .HasColumnType("float");

                    b.Property<double?>("HistoricalCostStartofYear")
                        .HasColumnType("float");

                    b.Property<double?>("HistoricalDepreciationAdditionalCharge")
                        .HasColumnType("float");

                    b.Property<double?>("HistoricalDepreciationStartofYear")
                        .HasColumnType("float");

                    b.Property<int?>("InServicePeriod")
                        .HasColumnType("int");

                    b.Property<int?>("InServiceYear")
                        .HasColumnType("int");

                    b.Property<bool>("InXero")
                        .HasColumnType("bit");

                    b.Property<DateTime>("Modified")
                        .HasColumnType("datetime2");

                    b.Property<int?>("NextPosting")
                        .HasColumnType("int");

                    b.Property<int?>("NoofPostings")
                        .HasColumnType("int");

                    b.Property<string>("Number")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("PostingFrequency")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("PostingMonths")
                        .HasColumnType("int");

                    b.Property<int?>("PostingYears")
                        .HasColumnType("int");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<string>("SerialNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Status")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SubCategory")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SupplierNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("XeroAssetId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Year")
                        .HasColumnType("int");

                    b.Property<double?>("YearlyPercentage")
                        .HasColumnType("float");

                    b.HasKey("AssetId");

                    b.HasIndex("AssetCategoryId");

                    b.HasIndex("AssetSubCategoryId");

                    b.HasIndex("HireEquipmentId");

                    b.HasIndex("Number");

                    b.ToTable("Assets");
                });

            modelBuilder.Entity("Red20.Model.Entity.AssetCategory", b =>
                {
                    b.Property<Guid>("AssetCategoryId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AdditionsWIP")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AssetCost")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<string>("DefaultFrequency")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DefaultMethod")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("DefaultPercentage")
                        .HasColumnType("float");

                    b.Property<int>("DefaultPeriod")
                        .HasColumnType("int");

                    b.Property<string>("DepreciationBS")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DepreciationPL")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Disposals")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DisposalsBankAccount")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ManagementReportColumn")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("Modified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RevaluationBS")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RevaluationPL")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("AssetCategoryId");

                    b.ToTable("AssetCategories");
                });

            modelBuilder.Entity("Red20.Model.Entity.AssetSubCategory", b =>
                {
                    b.Property<Guid>("AssetSubCategoryId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("Modified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Number")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("AssetSubCategoryId");

                    b.ToTable("AssetSubCategories");
                });

            modelBuilder.Entity("Red20.Model.Entity.Category", b =>
                {
                    b.Property<Guid>("CategoryId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Code")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("Modified")
                        .HasColumnType("datetime2");

                    b.HasKey("CategoryId");

                    b.ToTable("Categories");
                });

            modelBuilder.Entity("Red20.Model.Entity.CurrencyRate", b =>
                {
                    b.Property<Guid>("CurrencyRateId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("ExpiryDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("Modified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("Rate")
                        .HasColumnType("float");

                    b.HasKey("CurrencyRateId");

                    b.ToTable("CurrencyRates");
                });

            modelBuilder.Entity("Red20.Model.Entity.Customer", b =>
                {
                    b.Property<Guid>("CustomerId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<float>("CreditLimit")
                        .HasColumnType("real");

                    b.Property<string>("Currency")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<DateTime>("Modified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(255)")
                        .HasMaxLength(255);

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PrimaryInvoiceAddressCountry")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PrimaryInvoiceAddressCounty")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PrimaryInvoiceAddressName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PrimaryInvoiceAddressPostCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PrimaryInvoiceAddressStreet")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PrimaryInvoiceAddressStreet1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PrimaryInvoiceAddressTown")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PrimaryInvoiceContactDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PrimaryInvoiceContactEmailAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PrimaryInvoiceContactFirstName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PrimaryInvoiceContactMobile")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PrimaryInvoiceContactPhone")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PrimaryInvoiceContactSurname")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("ShowCustomer")
                        .HasColumnType("bit");

                    b.Property<string>("Status")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TaxCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VatRegistrationNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("XeroContactId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("CustomerId");

                    b.HasIndex("Name");

                    b.ToTable("Customers");
                });

            modelBuilder.Entity("Red20.Model.Entity.CustomerAddress", b =>
                {
                    b.Property<Guid>("CustomerAddressId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Country")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("County")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsInvoiceDefault")
                        .HasColumnType("bit");

                    b.Property<bool>("IsShipping")
                        .HasColumnType("bit");

                    b.Property<DateTime>("Modified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PostCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PrimaryContactDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PrimaryContactEmailAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PrimaryContactFirstName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PrimaryContactMobile")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PrimaryContactPhone")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PrimaryContactSurName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Street")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Street1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Town")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("CustomerAddressId");

                    b.HasIndex("CustomerId");

                    b.ToTable("CustomerAddress");
                });

            modelBuilder.Entity("Red20.Model.Entity.CustomerComplaint", b =>
                {
                    b.Property<Guid>("CustomerComplaintId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ActionTakenDetails")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AuthorisedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("AuthorisedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ComplaintAnalysisDetails")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ComplaintAnalysisThirdPartyName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ComplaintDetails")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CorectiveActionRequired")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double?>("CostOfComplaint")
                        .HasColumnType("float");

                    b.Property<string>("CostOfComplaintDetails")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CustomerComplaintCustomerName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CustomerComplaintJobNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CustomerComplaintNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CustomerContact")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CustomerReference")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DateClosed")
                        .HasColumnType("datetime2");

                    b.Property<string>("Department")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("HasAssemblyShop")
                        .HasColumnType("bit");

                    b.Property<bool>("HasCalibration")
                        .HasColumnType("bit");

                    b.Property<bool>("HasCreditNote")
                        .HasColumnType("bit");

                    b.Property<bool>("HasDelivery")
                        .HasColumnType("bit");

                    b.Property<bool>("HasDeliveryPackagingTransit")
                        .HasColumnType("bit");

                    b.Property<bool>("HasDocumentation")
                        .HasColumnType("bit");

                    b.Property<bool>("HasHire")
                        .HasColumnType("bit");

                    b.Property<bool>("HasInvoice")
                        .HasColumnType("bit");

                    b.Property<bool>("HasInvoicing")
                        .HasColumnType("bit");

                    b.Property<bool>("HasNonConformance")
                        .HasColumnType("bit");

                    b.Property<bool>("HasOther")
                        .HasColumnType("bit");

                    b.Property<bool>("HasOurManufacture")
                        .HasColumnType("bit");

                    b.Property<bool>("HasPrice")
                        .HasColumnType("bit");

                    b.Property<bool>("HasQuality")
                        .HasColumnType("bit");

                    b.Property<bool>("HasReIssueDocument")
                        .HasColumnType("bit");

                    b.Property<bool>("HasRepairRepairFoc")
                        .HasColumnType("bit");

                    b.Property<bool>("HasRepairReplaceCharge")
                        .HasColumnType("bit");

                    b.Property<bool>("HasResponseTime")
                        .HasColumnType("bit");

                    b.Property<bool>("HasShortage")
                        .HasColumnType("bit");

                    b.Property<bool>("HasSpecification")
                        .HasColumnType("bit");

                    b.Property<bool>("HasThirdPartyManufacture")
                        .HasColumnType("bit");

                    b.Property<string>("InvestigationCarriedOutBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("InvestigationCarriedOutDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("InvestigationFindings")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("InvoiceNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsCustomer")
                        .HasColumnType("bit");

                    b.Property<bool>("IsNoneRequired")
                        .HasColumnType("bit");

                    b.Property<string>("LoggedByQhse")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("LoggedByQhseDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("Modified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Number")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("OrderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("PurchaseOrderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("QhseNotes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RemedialActionOther")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RemedialActionThirdPartyName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SerialNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Severity")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Status")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SupplierContact")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("SupplierId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("TargetDate")
                        .HasColumnType("datetime2");

                    b.Property<double>("TotalDays")
                        .HasColumnType("float");

                    b.Property<string>("Type")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("CustomerComplaintId");

                    b.HasIndex("CustomerId");

                    b.HasIndex("OrderId");

                    b.HasIndex("PurchaseOrderId");

                    b.HasIndex("SupplierId");

                    b.ToTable("CustomerComplaints");
                });

            modelBuilder.Entity("Red20.Model.Entity.CustomerContact", b =>
                {
                    b.Property<Guid>("CustomerContactId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("CustomerAddressId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EmailAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FirstName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("bit");

                    b.Property<string>("Mobile")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("Modified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Phone")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Surname")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("CustomerContactId");

                    b.HasIndex("CustomerAddressId");

                    b.HasIndex("CustomerId");

                    b.ToTable("CustomerContact");
                });

            modelBuilder.Entity("Red20.Model.Entity.Document", b =>
                {
                    b.Property<Guid>("DocumentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("CompanyId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("nvarchar(255)")
                        .HasMaxLength(255);

                    b.Property<string>("Filename")
                        .IsRequired()
                        .HasColumnType("nvarchar(255)")
                        .HasMaxLength(255);

                    b.Property<bool>("IsOrderAttachment")
                        .HasColumnType("bit");

                    b.Property<bool>("IsQuoteAttachment")
                        .HasColumnType("bit");

                    b.Property<string>("MimeType")
                        .IsRequired()
                        .HasColumnType("nvarchar(255)")
                        .HasMaxLength(255);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(255)")
                        .HasMaxLength(255);

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("nvarchar(255)")
                        .HasMaxLength(255);

                    b.HasKey("DocumentId");

                    b.HasIndex("CompanyId");

                    b.HasIndex("DocumentId");

                    b.ToTable("Documents");
                });

            modelBuilder.Entity("Red20.Model.Entity.Enquiry", b =>
                {
                    b.Property<Guid>("EnquiryId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AccountCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("CustomerAddressId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CustomerContactId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("HasQuote")
                        .HasColumnType("bit");

                    b.Property<DateTime>("Modified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Number")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Origin")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("QuoteCount")
                        .HasColumnType("int");

                    b.HasKey("EnquiryId");

                    b.HasIndex("CustomerAddressId");

                    b.HasIndex("CustomerContactId");

                    b.HasIndex("CustomerId");

                    b.HasIndex("EnquiryId");

                    b.HasIndex("Number");

                    b.ToTable("Enquiries");
                });

            modelBuilder.Entity("Red20.Model.Entity.EquipmentServiceHistory", b =>
                {
                    b.Property<Guid>("EquipmentServiceHistoryId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("HireEquipmentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("InspectedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("InspectedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("OrderAssemblyHireEquipmentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("OrderAssemblyId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("OrderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("OrderNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("SetHireEquipmentAvailable")
                        .HasColumnType("bit");

                    b.HasKey("EquipmentServiceHistoryId");

                    b.HasIndex("HireEquipmentId");

                    b.HasIndex("OrderId");

                    b.ToTable("EquipmentServiceHistories");
                });

            modelBuilder.Entity("Red20.Model.Entity.EquipmentServiceStockItem", b =>
                {
                    b.Property<Guid>("EquipmentServiceStockItemId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("EquipmentServiceHistoryId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("HireEquipmentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("OrderAssemblyId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("OrderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("OrderNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<string>("StockCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("StockId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("EquipmentServiceStockItemId");

                    b.HasIndex("EquipmentServiceHistoryId");

                    b.HasIndex("StockId");

                    b.ToTable("EquipmentServiceStockItems");
                });

            modelBuilder.Entity("Red20.Model.Entity.HireEquipment", b =>
                {
                    b.Property<Guid>("HireEquipmentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ArchiveComments")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("Archived")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("ArchivedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("AssetSubCategoryId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("BaseLocation")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Category")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Length")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Model")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OrderNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SWL")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SWLUnit")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SerialNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Status")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Unit")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("HireEquipmentId");

                    b.HasIndex("AssetSubCategoryId");

                    b.HasIndex("HireEquipmentId");

                    b.ToTable("HireEquipments");
                });

            modelBuilder.Entity("Red20.Model.Entity.Job", b =>
                {
                    b.Property<Guid>("JobId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CostCentre")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("OrderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("OrderType")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("JobId");

                    b.HasIndex("JobId");

                    b.ToTable("Jobs");
                });

            modelBuilder.Entity("Red20.Model.Entity.JobInvoice", b =>
                {
                    b.Property<Guid>("JobInvoiceId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CustomerPO")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("InvoiceDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("InvoiceNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("JobId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("JobType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OrderNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("PurchaseOrderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("XeroInvoiceId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("JobInvoiceId");

                    b.HasIndex("JobId");

                    b.HasIndex("JobInvoiceId");

                    b.ToTable("JobInvoices");
                });

            modelBuilder.Entity("Red20.Model.Entity.JobInvoiceItem", b =>
                {
                    b.Property<Guid>("JobInvoiceItemId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AccountCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Currency")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double?>("DiscountRate")
                        .HasColumnType("float");

                    b.Property<DateTime>("InvoiceDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("InvoiceNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("InvoiceType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsDeliveryCharge")
                        .HasColumnType("bit");

                    b.Property<bool>("IsStockItem")
                        .HasColumnType("bit");

                    b.Property<string>("JobCostCentre")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("JobId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("JobInvoiceId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("OrderItemId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("PurchaseOrderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<double>("Quantity")
                        .HasColumnType("float");

                    b.Property<double>("QuantityToDeliver")
                        .HasColumnType("float");

                    b.Property<string>("StockCategory")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StockCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("ToFollow")
                        .HasColumnType("float");

                    b.Property<double?>("TotalGross")
                        .HasColumnType("float");

                    b.Property<double?>("TotalNet")
                        .HasColumnType("float");

                    b.Property<double?>("TotalVat")
                        .HasColumnType("float");

                    b.Property<double>("UnitPrice")
                        .HasColumnType("float");

                    b.Property<double>("Value")
                        .HasColumnType("float");

                    b.Property<string>("Vat")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("JobInvoiceItemId");

                    b.HasIndex("JobId");

                    b.HasIndex("JobInvoiceId");

                    b.ToTable("JobInvoiceItem");
                });

            modelBuilder.Entity("Red20.Model.Entity.NumberSequence", b =>
                {
                    b.Property<Guid?>("NumberSequenceId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Letter")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(20)")
                        .HasMaxLength(20);

                    b.Property<int>("Number")
                        .HasColumnType("int");

                    b.Property<string>("Type")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("NumberSequenceId");

                    b.ToTable("NumberSequences");
                });

            modelBuilder.Entity("Red20.Model.Entity.Order", b =>
                {
                    b.Property<Guid>("OrderId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AccountCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double?>("CollectionCharge")
                        .HasColumnType("float");

                    b.Property<string>("CollectionChargeAccountCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Currency")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("CustomerContactId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("CustomerInvoiceAddressId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CustomerRef")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("CustomerShippingAddressId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<double?>("DeliveryCharge")
                        .HasColumnType("float");

                    b.Property<string>("DeliveryChargeType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EmailBody")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EmailSubject")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("EnquiryId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int?>("HirePeriod")
                        .HasColumnType("int");

                    b.Property<bool>("Internal")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("InvoiceDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("InvoicedInPart")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeliveryChargeInvoiced")
                        .HasColumnType("bit");

                    b.Property<bool>("IsIssued")
                        .HasColumnType("bit");

                    b.Property<bool>("IsManual")
                        .HasColumnType("bit");

                    b.Property<string>("IssuedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("IssuedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("Modified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Number")
                        .HasColumnType("nvarchar(450)");

                    b.Property<Guid?>("QuoteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ShippingAddressCustomerName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ShippingAddressNote")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("StockProcessed")
                        .HasColumnType("bit");

                    b.Property<bool>("StockProcessedInPart")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("TargetDispatch")
                        .HasColumnType("datetime2");

                    b.Property<string>("TaxCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Type")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("OrderId");

                    b.HasIndex("CustomerContactId");

                    b.HasIndex("CustomerId");

                    b.HasIndex("CustomerInvoiceAddressId");

                    b.HasIndex("CustomerShippingAddressId");

                    b.HasIndex("EnquiryId");

                    b.HasIndex("Number");

                    b.HasIndex("QuoteId");

                    b.ToTable("Orders");
                });

            modelBuilder.Entity("Red20.Model.Entity.OrderAssembly", b =>
                {
                    b.Property<Guid>("OrderAssemblyId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AccountCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double?>("CollectionCharge")
                        .HasColumnType("float");

                    b.Property<string>("CollectionChargeAccountCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<double>("DayRate")
                        .HasColumnType("float");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double?>("Discount")
                        .HasColumnType("float");

                    b.Property<DateTime?>("HireEnd")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("HireStart")
                        .HasColumnType("datetime2");

                    b.Property<string>("InspectedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("InvoiceDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsInitialDeliveryCharge")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSimpleAssembly")
                        .HasColumnType("bit");

                    b.Property<string>("ItemDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("OrderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("PreparedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ReceivedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("ReceivedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("OrderAssemblyId");

                    b.HasIndex("OrderId");

                    b.ToTable("OrderAssemblies");
                });

            modelBuilder.Entity("Red20.Model.Entity.OrderAssemblyHireEquipment", b =>
                {
                    b.Property<Guid>("OrderAssemblyHireEquipmentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Category")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("EquipmentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Model")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("OrderAssemblyId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Serial")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Status")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("OrderAssemblyHireEquipmentId");

                    b.HasIndex("OrderAssemblyId");

                    b.ToTable("OrderAssemblyHireEquipments");
                });

            modelBuilder.Entity("Red20.Model.Entity.OrderAssemblyHirePreparationSheet", b =>
                {
                    b.Property<Guid>("HirePreparationSheetId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AirService")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Capacity")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ChainCollector")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Classification")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ColourCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ControlLength")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ControlType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FittingType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Model")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("OrderAssemblyId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ProofLoad")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("Replicate")
                        .HasColumnType("bit");

                    b.Property<string>("SWL")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SpecialInstructions")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SpecialInstructionsLoadCell")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TrolleyFlangeSize")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TrolleyType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Type")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("HirePreparationSheetId");

                    b.HasIndex("OrderAssemblyId");

                    b.ToTable("OrderAssemblyHirePreparationSheets");
                });

            modelBuilder.Entity("Red20.Model.Entity.OrderCreditNoteItem", b =>
                {
                    b.Property<Guid>("OrderCreditNoteItemId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AccountCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("CreditNoteDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreditNoteNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Currency")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double?>("Discount")
                        .HasColumnType("float");

                    b.Property<Guid?>("OrderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("OrderItemId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<double>("TotalValue")
                        .HasColumnType("float");

                    b.Property<double>("UnitPrice")
                        .HasColumnType("float");

                    b.Property<Guid?>("XeroCreditNoteId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("OrderCreditNoteItemId");

                    b.HasIndex("OrderItemId");

                    b.ToTable("OrderCreditNoteItems");
                });

            modelBuilder.Entity("Red20.Model.Entity.OrderItem", b =>
                {
                    b.Property<Guid>("OrderItemId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AccountCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<double?>("CurrencyRate")
                        .HasColumnType("float");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double?>("Discount")
                        .HasColumnType("float");

                    b.Property<bool>("HasCreditNote")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeliveryChargeItem")
                        .HasColumnType("bit");

                    b.Property<bool>("IsInitialDeliveryCharge")
                        .HasColumnType("bit");

                    b.Property<double>("NumberToInvoice")
                        .HasColumnType("float");

                    b.Property<Guid>("OrderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("PartCredit")
                        .HasColumnType("bit");

                    b.Property<double?>("PoundValue")
                        .HasColumnType("float");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<int>("TotalCredited")
                        .HasColumnType("int");

                    b.Property<double>("TotalDelivered")
                        .HasColumnType("float");

                    b.Property<double>("TotalInvoiced")
                        .HasColumnType("float");

                    b.Property<double>("UnitPrice")
                        .HasColumnType("float");

                    b.HasKey("OrderItemId");

                    b.HasIndex("OrderId");

                    b.ToTable("OrderItems");
                });

            modelBuilder.Entity("Red20.Model.Entity.OrderItemJobRecordSheet", b =>
                {
                    b.Property<Guid>("JobRecordSheetId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AdditionalFacilities")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("OrderItemId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<double?>("ProofLoad")
                        .HasColumnType("float");

                    b.Property<string>("ProofLoadUnit")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Quantity")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double?>("SWL")
                        .HasColumnType("float");

                    b.Property<string>("SWLUnit")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SerialNo")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("WorkInstructions")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("JobRecordSheetId");

                    b.HasIndex("OrderItemId");

                    b.ToTable("OrderItemJobRecordSheets");
                });

            modelBuilder.Entity("Red20.Model.Entity.OrderItemStockItem", b =>
                {
                    b.Property<Guid>("OrderItemStockItemId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<double>("Allocated")
                        .HasColumnType("float");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("Invoiced")
                        .HasColumnType("float");

                    b.Property<double>("NumberToInvoice")
                        .HasColumnType("float");

                    b.Property<Guid>("OrderItemId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<double>("Quantity")
                        .HasColumnType("float");

                    b.Property<string>("SheetType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StockCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("StockId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<double>("TotalDelivered")
                        .HasColumnType("float");

                    b.Property<string>("Type")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("Unallocated")
                        .HasColumnType("float");

                    b.HasKey("OrderItemStockItemId");

                    b.HasIndex("OrderItemId");

                    b.HasIndex("StockId");

                    b.ToTable("OrderItemStockItems");
                });

            modelBuilder.Entity("Red20.Model.Entity.OrderItemWorkSiteSheet", b =>
                {
                    b.Property<Guid>("WorkSiteSheetId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AccessPlatform")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AddressCountry")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AddressCounty")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AddressName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AddressPostCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AddressStreet")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AddressStreet1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AddressTown")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Customer")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("Date")
                        .HasColumnType("datetime2");

                    b.Property<string>("DetailsOfWork")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EngineerName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("OrderItemId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("OrderNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SiteContact")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("WorkSiteSheetId");

                    b.HasIndex("OrderItemId");

                    b.ToTable("OrderItemWorkSiteSheets");
                });

            modelBuilder.Entity("Red20.Model.Entity.OrderStockAllocation", b =>
                {
                    b.Property<Guid>("OrderStockAllocationId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<double>("Allocate")
                        .HasColumnType("float");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<double>("Invoiced")
                        .HasColumnType("float");

                    b.Property<DateTime>("Modified")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("OrderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<double>("QuantityToDeliver")
                        .HasColumnType("float");

                    b.Property<Guid>("StockId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("OrderStockAllocationId");

                    b.HasIndex("OrderId");

                    b.HasIndex("StockId");

                    b.ToTable("OrderStockAllocations");
                });

            modelBuilder.Entity("Red20.Model.Entity.PredefinedItem", b =>
                {
                    b.Property<Guid>("PredefinedItemId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Model")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("Modified")
                        .HasColumnType("datetime2");

                    b.HasKey("PredefinedItemId");

                    b.ToTable("PredefinedItems");
                });

            modelBuilder.Entity("Red20.Model.Entity.PredefinedItemStock", b =>
                {
                    b.Property<Guid>("PredefinedItemStockId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Location")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("Modified")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("PredefinedItemId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("StockId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("PredefinedItemStockId");

                    b.HasIndex("PredefinedItemId");

                    b.HasIndex("StockId");

                    b.ToTable("PredefinedItemStocks");
                });

            modelBuilder.Entity("Red20.Model.Entity.PurchaseOrder", b =>
                {
                    b.Property<Guid>("PurchaseOrderId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AccountCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedByEmailAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Currency")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DateCompleted")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateReceived")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeliveryAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DepartmentNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsCollection")
                        .HasColumnType("bit");

                    b.Property<bool>("IsInternalDeliveryAddress")
                        .HasColumnType("bit");

                    b.Property<bool>("IsManualOrder")
                        .HasColumnType("bit");

                    b.Property<DateTime>("Modified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Number")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("OrderDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("PurchaseRequisitionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("RequiredDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Status")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("SupplierId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("VatCode")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("PurchaseOrderId");

                    b.HasIndex("PurchaseRequisitionId");

                    b.HasIndex("SupplierId");

                    b.ToTable("PurchaseOrders");
                });

            modelBuilder.Entity("Red20.Model.Entity.PurchaseOrderCreditNoteItem", b =>
                {
                    b.Property<Guid>("PurchaseOrderCreditNoteItemId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AccountCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("CreditNoteDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreditNoteNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double?>("Discount")
                        .HasColumnType("float");

                    b.Property<DateTime>("Modified")
                        .HasColumnType("datetime2");

                    b.Property<double?>("Price")
                        .HasColumnType("float");

                    b.Property<Guid>("PurchaseOrderItemId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int?>("Quantity")
                        .HasColumnType("int");

                    b.Property<double>("TotalGross")
                        .HasColumnType("float");

                    b.Property<double>("TotalNet")
                        .HasColumnType("float");

                    b.Property<double?>("Vat")
                        .HasColumnType("float");

                    b.Property<string>("VatCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("XeroCreditNoteId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("PurchaseOrderCreditNoteItemId");

                    b.HasIndex("PurchaseOrderItemId");

                    b.ToTable("PurchaseOrderCreditNoteItems");
                });

            modelBuilder.Entity("Red20.Model.Entity.PurchaseOrderItem", b =>
                {
                    b.Property<Guid>("PurchaseOrderItemId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AccountCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<double?>("CurrencyRate")
                        .HasColumnType("float");

                    b.Property<DateTime?>("DateReceived")
                        .HasColumnType("datetime2");

                    b.Property<string>("DepartmentNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double?>("Discount")
                        .HasColumnType("float");

                    b.Property<double?>("GrossPoundValue")
                        .HasColumnType("float");

                    b.Property<bool>("IsPartComplete")
                        .HasColumnType("bit");

                    b.Property<bool>("IsPartInvoiced")
                        .HasColumnType("bit");

                    b.Property<bool>("IsReceived")
                        .HasColumnType("bit");

                    b.Property<string>("JobNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("Modified")
                        .HasColumnType("datetime2");

                    b.Property<double?>("NettPoundValue")
                        .HasColumnType("float");

                    b.Property<Guid?>("OrderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("PartReceivedDate")
                        .HasColumnType("datetime2");

                    b.Property<double?>("Price")
                        .HasColumnType("float");

                    b.Property<Guid>("PurchaseOrderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int?>("Quantity")
                        .HasColumnType("int");

                    b.Property<Guid?>("StockId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<double>("TotalGross")
                        .HasColumnType("float");

                    b.Property<int?>("TotalInvoiced")
                        .HasColumnType("int");

                    b.Property<double>("TotalNet")
                        .HasColumnType("float");

                    b.Property<int?>("TotalReceived")
                        .HasColumnType("int");

                    b.Property<string>("Type")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double?>("Vat")
                        .HasColumnType("float");

                    b.Property<string>("VatCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double?>("VatPoundValue")
                        .HasColumnType("float");

                    b.HasKey("PurchaseOrderItemId");

                    b.HasIndex("OrderId");

                    b.HasIndex("PurchaseOrderId");

                    b.HasIndex("StockId");

                    b.ToTable("PurchaseOrderItems");
                });

            modelBuilder.Entity("Red20.Model.Entity.PurchaseOrderItemLog", b =>
                {
                    b.Property<Guid>("PurchaseOrderItemLogId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreditedDate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("InvoiceDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("InvoiceNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PartReceivedDate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("PurchaseOrderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("PurchaseOrderItemId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ReceivedDate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TotalCredited")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("TotalInvoiced")
                        .HasColumnType("float");

                    b.Property<string>("TotalReceived")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Username")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("PurchaseOrderItemLogId");

                    b.HasIndex("PurchaseOrderItemId");

                    b.ToTable("PurchaseOrderItemLogs");
                });

            modelBuilder.Entity("Red20.Model.Entity.PurchaseOrderItemSerialNumber", b =>
                {
                    b.Property<Guid>("PurchaseOrderItemSerialNumberId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("PurchaseOrderItemId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("SerialNumber")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("PurchaseOrderItemSerialNumberId");

                    b.HasIndex("PurchaseOrderItemId");

                    b.ToTable("PurchaseOrderItemSerialNumbers");
                });

            modelBuilder.Entity("Red20.Model.Entity.PurchaseRequisition", b =>
                {
                    b.Property<Guid>("PurchaseRequisitionId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AccountCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedByEmailAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Currency")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeliveryAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DepartmentNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DueDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsCollection")
                        .HasColumnType("bit");

                    b.Property<bool>("IsInternalDeliveryAddress")
                        .HasColumnType("bit");

                    b.Property<DateTime>("Modified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Number")
                        .HasColumnType("nvarchar(450)");

                    b.Property<Guid?>("OrderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("SupplierId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("PurchaseRequisitionId");

                    b.HasIndex("Number");

                    b.HasIndex("OrderId");

                    b.HasIndex("SupplierId");

                    b.ToTable("PurchaseRequisitions");
                });

            modelBuilder.Entity("Red20.Model.Entity.PurchaseRequisitionItem", b =>
                {
                    b.Property<Guid>("PurchaseRequisitionItemId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AccountCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<string>("DepartmentNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double?>("Discount")
                        .HasColumnType("float");

                    b.Property<DateTime>("Modified")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("PurchaseRequisitionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<string>("StockCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SupplierCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("TotalValue")
                        .HasColumnType("float");

                    b.Property<double>("UnitPrice")
                        .HasColumnType("float");

                    b.HasKey("PurchaseRequisitionItemId");

                    b.HasIndex("PurchaseRequisitionId");

                    b.ToTable("PurchaseRequisitionItems");
                });

            modelBuilder.Entity("Red20.Model.Entity.Quote", b =>
                {
                    b.Property<Guid>("QuoteId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AccountCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AdditionalInfo")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double?>("CollectionCharge")
                        .HasColumnType("float");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Currency")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("CustomerAddressId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CustomerContactId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CustomerFeedback")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CustomerRef")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double?>("DeliveryCharge")
                        .HasColumnType("float");

                    b.Property<string>("DeliveryChargeType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DeliveryTerms")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DeliveryTime")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EmailBody")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EmailSubject")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("EnquiryId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int?>("HirePeriod")
                        .HasColumnType("int");

                    b.Property<bool>("IsIssued")
                        .HasColumnType("bit");

                    b.Property<bool>("IsRevisionQuote")
                        .HasColumnType("bit");

                    b.Property<bool>("IsVoid")
                        .HasColumnType("bit");

                    b.Property<string>("IssuedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("IssuedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("Modified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Number")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("PaymentTerms")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("QuoteStatus")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("ReminderDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("TaxCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Type")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("Validity")
                        .HasColumnType("int");

                    b.HasKey("QuoteId");

                    b.HasIndex("CustomerAddressId");

                    b.HasIndex("CustomerContactId");

                    b.HasIndex("CustomerId");

                    b.HasIndex("EnquiryId");

                    b.HasIndex("Number");

                    b.ToTable("Quotes");
                });

            modelBuilder.Entity("Red20.Model.Entity.QuoteItem", b =>
                {
                    b.Property<Guid>("QuoteItemId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("Created")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double?>("Discount")
                        .HasColumnType("float");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<Guid>("QuoteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<double>("UnitPrice")
                        .HasColumnType("float");

                    b.HasKey("QuoteItemId");

                    b.HasIndex("QuoteId");

                    b.ToTable("QuoteItem");
                });

            modelBuilder.Entity("Red20.Model.Entity.Stock", b =>
                {
                    b.Property<Guid>("StockId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("AnalysisCodeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("CategoryId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Code")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double?>("CostPrice")
                        .HasColumnType("float");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double?>("FifoValue")
                        .HasColumnType("float");

                    b.Property<string>("Location")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("MaximumStock")
                        .HasColumnType("int");

                    b.Property<int?>("MinimumStock")
                        .HasColumnType("int");

                    b.Property<DateTime>("Modified")
                        .HasColumnType("datetime2");

                    b.Property<double>("PhysicalStock")
                        .HasColumnType("float");

                    b.Property<Guid?>("SupplierId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("SupplierStockCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("TotalOrderIn")
                        .HasColumnType("int");

                    b.Property<string>("_types")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("StockId");

                    b.HasIndex("AnalysisCodeId");

                    b.HasIndex("CategoryId");

                    b.HasIndex("SupplierId");

                    b.ToTable("Stocks");
                });

            modelBuilder.Entity("Red20.Model.Entity.StockAllocation", b =>
                {
                    b.Property<Guid>("StockAllocationId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("OrderNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Period")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("QuantitiyAllocated")
                        .HasColumnType("int");

                    b.Property<int>("QuantityDelivered")
                        .HasColumnType("int");

                    b.Property<int>("QuantityDeliveries")
                        .HasColumnType("int");

                    b.Property<int>("QuantityInvoiced")
                        .HasColumnType("int");

                    b.Property<int>("QuantityOrdered")
                        .HasColumnType("int");

                    b.Property<int>("QuantityUnallocated")
                        .HasColumnType("int");

                    b.Property<Guid>("StockId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Type")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("StockAllocationId");

                    b.HasIndex("StockId");

                    b.ToTable("StockAllocations");
                });

            modelBuilder.Entity("Red20.Model.Entity.StockAssembly", b =>
                {
                    b.Property<Guid>("StockAssemblyId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("AssemblyStockId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<double>("CostPrice")
                        .HasColumnType("float");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("Modified")
                        .HasColumnType("datetime2");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<Guid>("StockId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Type")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("StockAssemblyId");

                    b.HasIndex("AssemblyStockId");

                    b.ToTable("StockAssemblies");
                });

            modelBuilder.Entity("Red20.Model.Entity.StockAssemblyMovement", b =>
                {
                    b.Property<Guid>("StockAssemblyMovementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("MovementDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Reference")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("StockAssemblyMovementId");

                    b.ToTable("StockAssemblyMovements");
                });

            modelBuilder.Entity("Red20.Model.Entity.StockAssemblyMovementItem", b =>
                {
                    b.Property<Guid>("StockAssemblyMovementItemId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime2");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<Guid>("StockAssemblyMovementId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("StockId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Type")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("Value")
                        .HasColumnType("float");

                    b.Property<string>("_serialNumbers")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("StockAssemblyMovementItemId");

                    b.HasIndex("StockAssemblyMovementId");

                    b.HasIndex("StockId");

                    b.ToTable("StockAssemblyMovementItems");
                });

            modelBuilder.Entity("Red20.Model.Entity.StockMovement", b =>
                {
                    b.Property<Guid>("StockMovementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("MovementDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Reference")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("StockMovementId");

                    b.ToTable("StockMovements");
                });

            modelBuilder.Entity("Red20.Model.Entity.StockMovementItem", b =>
                {
                    b.Property<Guid>("StockMovementItemId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<double>("CostPrice")
                        .HasColumnType("float");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<string>("HireEquipmentSerialNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("JobInvoiceItemId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ManualSerialNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double?>("NetValue")
                        .HasColumnType("float");

                    b.Property<Guid?>("OrderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<string>("StockCodeNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("StockId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("StockMovementId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Type")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("_serialNumbers")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("StockMovementItemId");

                    b.HasIndex("OrderId");

                    b.HasIndex("StockId");

                    b.HasIndex("StockMovementId");

                    b.ToTable("StockMovementItems");
                });

            modelBuilder.Entity("Red20.Model.Entity.StockSerialTracking", b =>
                {
                    b.Property<Guid>("StockSerialTrackingId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsOut")
                        .HasColumnType("bit");

                    b.Property<bool>("IsReserved")
                        .HasColumnType("bit");

                    b.Property<string>("JobNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SerialNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Status")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StockCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("StockId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("StockSerialTrackingId");

                    b.HasIndex("StockId");

                    b.ToTable("StockSerialTrackings");
                });

            modelBuilder.Entity("Red20.Model.Entity.StockSerialTrackingLog", b =>
                {
                    b.Property<Guid>("StockSerialTrackingLogId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<string>("OrderReference")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SerialNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Status")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("StockSerialTrackingId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Type")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("StockSerialTrackingLogId");

                    b.HasIndex("StockSerialTrackingId");

                    b.ToTable("StockSerialTrackingLogs");
                });

            modelBuilder.Entity("Red20.Model.Entity.StockTransaction", b =>
                {
                    b.Property<Guid>("StockTransactionId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AccountCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("CostPrice")
                        .HasColumnType("float");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OrderNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("Quantity")
                        .HasColumnType("float");

                    b.Property<string>("Status")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("StockId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<double?>("TotalNet")
                        .HasColumnType("float");

                    b.Property<string>("Type")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("StockTransactionId");

                    b.HasIndex("StockId");

                    b.ToTable("StockTransactions");
                });

            modelBuilder.Entity("Red20.Model.Entity.Supplier", b =>
                {
                    b.Property<Guid>("SupplierId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("CertificateExpiryDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateApproved")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Is14DayEmailSent")
                        .HasColumnType("bit");

                    b.Property<bool>("Is2DayEmailSent")
                        .HasColumnType("bit");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<DateTime>("Modified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(255)")
                        .HasMaxLength(255);

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("QAStatus")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("QAStatusModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("QAStatusModifiedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("XeroContactId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("SupplierId");

                    b.HasIndex("Name");

                    b.ToTable("Suppliers");
                });

            modelBuilder.Entity("Red20.Model.Entity.SupplierAddress", b =>
                {
                    b.Property<Guid>("SupplierAddressId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Country")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("County")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsBranchOffice")
                        .HasColumnType("bit");

                    b.Property<bool>("IsHeadOfficeDefault")
                        .HasColumnType("bit");

                    b.Property<DateTime>("Modified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PostCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Street")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Street1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("SupplierId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Town")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("SupplierAddressId");

                    b.HasIndex("SupplierId");

                    b.ToTable("SupplierAddress");
                });

            modelBuilder.Entity("Red20.Model.Entity.SupplierContact", b =>
                {
                    b.Property<Guid>("SupplierContactId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EmailAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FirstName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Mobile")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("Modified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Phone")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("SupplierAddressId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("SupplierId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Surname")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("SupplierContactId");

                    b.HasIndex("SupplierAddressId");

                    b.HasIndex("SupplierId");

                    b.ToTable("SupplierContact");
                });

            modelBuilder.Entity("Red20.Model.Entity.User", b =>
                {
                    b.Property<Guid>("UserId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("Accessed")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<string>("EmailAddress")
                        .IsRequired()
                        .HasColumnType("nvarchar(255)")
                        .HasMaxLength(255);

                    b.Property<bool>("Enabled")
                        .HasColumnType("bit");

                    b.Property<string>("Firstname")
                        .IsRequired()
                        .HasColumnType("nvarchar(255)")
                        .HasMaxLength(255);

                    b.Property<string>("JobTitle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Lastname")
                        .IsRequired()
                        .HasColumnType("nvarchar(255)")
                        .HasMaxLength(255);

                    b.Property<string>("Location")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("Modified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasColumnType("nvarchar(255)")
                        .HasMaxLength(255);

                    b.Property<Guid?>("PasswordReset")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Role")
                        .IsRequired()
                        .HasColumnType("nvarchar(3)")
                        .HasMaxLength(3);

                    b.Property<string>("Salt")
                        .IsRequired()
                        .HasColumnType("nvarchar(255)")
                        .HasMaxLength(255);

                    b.HasKey("UserId");

                    b.HasIndex("EmailAddress");

                    b.HasIndex("Firstname");

                    b.HasIndex("Lastname");

                    b.ToTable("Users");
                });

            modelBuilder.Entity("Red20.Model.Entity.UserToken", b =>
                {
                    b.Property<Guid>("UserTokenId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("Expires")
                        .HasColumnType("datetime2");

                    b.Property<string>("Token")
                        .IsRequired()
                        .HasColumnType("nvarchar(255)")
                        .HasMaxLength(255);

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("UserTokenId");

                    b.HasIndex("Token");

                    b.HasIndex("UserId");

                    b.ToTable("UserTokens");
                });

            modelBuilder.Entity("Red20.Model.Entity.Asset", b =>
                {
                    b.HasOne("Red20.Model.Entity.AssetCategory", "AssetCategory")
                        .WithMany()
                        .HasForeignKey("AssetCategoryId");

                    b.HasOne("Red20.Model.Entity.AssetSubCategory", "AssetSubCategory")
                        .WithMany()
                        .HasForeignKey("AssetSubCategoryId");

                    b.HasOne("Red20.Model.Entity.HireEquipment", "HireEquipment")
                        .WithMany()
                        .HasForeignKey("HireEquipmentId");
                });

            modelBuilder.Entity("Red20.Model.Entity.CustomerAddress", b =>
                {
                    b.HasOne("Red20.Model.Entity.Customer", "Customer")
                        .WithMany("CustomerAddresses")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Red20.Model.Entity.CustomerComplaint", b =>
                {
                    b.HasOne("Red20.Model.Entity.Customer", "Customer")
                        .WithMany()
                        .HasForeignKey("CustomerId");

                    b.HasOne("Red20.Model.Entity.Order", "Order")
                        .WithMany("CustomerComplaints")
                        .HasForeignKey("OrderId");

                    b.HasOne("Red20.Model.Entity.PurchaseOrder", "PurchaseOrder")
                        .WithMany()
                        .HasForeignKey("PurchaseOrderId");

                    b.HasOne("Red20.Model.Entity.Supplier", "Supplier")
                        .WithMany()
                        .HasForeignKey("SupplierId");
                });

            modelBuilder.Entity("Red20.Model.Entity.CustomerContact", b =>
                {
                    b.HasOne("Red20.Model.Entity.CustomerAddress", "CustomerAddress")
                        .WithMany("CustomerContacts")
                        .HasForeignKey("CustomerAddressId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Red20.Model.Entity.Customer", "Customer")
                        .WithMany()
                        .HasForeignKey("CustomerId");
                });

            modelBuilder.Entity("Red20.Model.Entity.Enquiry", b =>
                {
                    b.HasOne("Red20.Model.Entity.CustomerAddress", "CustomerAddress")
                        .WithMany()
                        .HasForeignKey("CustomerAddressId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Red20.Model.Entity.CustomerContact", "CustomerContact")
                        .WithMany()
                        .HasForeignKey("CustomerContactId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Red20.Model.Entity.Customer", "Customer")
                        .WithMany()
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Red20.Model.Entity.EquipmentServiceHistory", b =>
                {
                    b.HasOne("Red20.Model.Entity.HireEquipment", "Equipment")
                        .WithMany()
                        .HasForeignKey("HireEquipmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Red20.Model.Entity.Order", "Order")
                        .WithMany()
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Red20.Model.Entity.EquipmentServiceStockItem", b =>
                {
                    b.HasOne("Red20.Model.Entity.EquipmentServiceHistory", "EquipmentServiceHistory")
                        .WithMany("EquipmentServiceStockItems")
                        .HasForeignKey("EquipmentServiceHistoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Red20.Model.Entity.Stock", "Stock")
                        .WithMany("EquipmentServiceStockItems")
                        .HasForeignKey("StockId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Red20.Model.Entity.HireEquipment", b =>
                {
                    b.HasOne("Red20.Model.Entity.AssetSubCategory", "AssetSubCategory")
                        .WithMany()
                        .HasForeignKey("AssetSubCategoryId");
                });

            modelBuilder.Entity("Red20.Model.Entity.JobInvoice", b =>
                {
                    b.HasOne("Red20.Model.Entity.Job", "Job")
                        .WithMany("JobInvoices")
                        .HasForeignKey("JobId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Red20.Model.Entity.JobInvoiceItem", b =>
                {
                    b.HasOne("Red20.Model.Entity.Job", "Job")
                        .WithMany()
                        .HasForeignKey("JobId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Red20.Model.Entity.JobInvoice", "JobInvoice")
                        .WithMany("JobInvoiceItems")
                        .HasForeignKey("JobInvoiceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Red20.Model.Entity.Order", b =>
                {
                    b.HasOne("Red20.Model.Entity.CustomerContact", "CustomerContact")
                        .WithMany()
                        .HasForeignKey("CustomerContactId");

                    b.HasOne("Red20.Model.Entity.Customer", "Customer")
                        .WithMany()
                        .HasForeignKey("CustomerId");

                    b.HasOne("Red20.Model.Entity.CustomerAddress", "CustomerInvoiceAddress")
                        .WithMany()
                        .HasForeignKey("CustomerInvoiceAddressId");

                    b.HasOne("Red20.Model.Entity.CustomerAddress", "CustomerShippingAddress")
                        .WithMany()
                        .HasForeignKey("CustomerShippingAddressId");

                    b.HasOne("Red20.Model.Entity.Enquiry", "Enquiry")
                        .WithMany()
                        .HasForeignKey("EnquiryId");

                    b.HasOne("Red20.Model.Entity.Quote", "Quote")
                        .WithMany("Orders")
                        .HasForeignKey("QuoteId");
                });

            modelBuilder.Entity("Red20.Model.Entity.OrderAssembly", b =>
                {
                    b.HasOne("Red20.Model.Entity.Order", "Order")
                        .WithMany("OrderAssemblies")
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Red20.Model.Entity.OrderAssemblyHireEquipment", b =>
                {
                    b.HasOne("Red20.Model.Entity.OrderAssembly", "OrderAssembly")
                        .WithMany("HireEquipments")
                        .HasForeignKey("OrderAssemblyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Red20.Model.Entity.OrderAssemblyHirePreparationSheet", b =>
                {
                    b.HasOne("Red20.Model.Entity.OrderAssembly", "OrderAssembly")
                        .WithMany()
                        .HasForeignKey("OrderAssemblyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Red20.Model.Entity.OrderCreditNoteItem", b =>
                {
                    b.HasOne("Red20.Model.Entity.OrderItem", "OrderItem")
                        .WithMany()
                        .HasForeignKey("OrderItemId");
                });

            modelBuilder.Entity("Red20.Model.Entity.OrderItem", b =>
                {
                    b.HasOne("Red20.Model.Entity.Order", "Order")
                        .WithMany("OrderItems")
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Red20.Model.Entity.OrderItemJobRecordSheet", b =>
                {
                    b.HasOne("Red20.Model.Entity.OrderItem", "OrderItem")
                        .WithMany()
                        .HasForeignKey("OrderItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Red20.Model.Entity.OrderItemStockItem", b =>
                {
                    b.HasOne("Red20.Model.Entity.OrderItem", "OrderItem")
                        .WithMany("OrderItemStockItems")
                        .HasForeignKey("OrderItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Red20.Model.Entity.Stock", "Stock")
                        .WithMany("OrderItemStockItems")
                        .HasForeignKey("StockId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Red20.Model.Entity.OrderItemWorkSiteSheet", b =>
                {
                    b.HasOne("Red20.Model.Entity.OrderItem", "OrderItem")
                        .WithMany()
                        .HasForeignKey("OrderItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Red20.Model.Entity.OrderStockAllocation", b =>
                {
                    b.HasOne("Red20.Model.Entity.Order", "Order")
                        .WithMany()
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Red20.Model.Entity.Stock", "Stock")
                        .WithMany()
                        .HasForeignKey("StockId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Red20.Model.Entity.PredefinedItemStock", b =>
                {
                    b.HasOne("Red20.Model.Entity.PredefinedItem", "PredefinedItem")
                        .WithMany("PredefinedItemStocks")
                        .HasForeignKey("PredefinedItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Red20.Model.Entity.Stock", "Stock")
                        .WithMany()
                        .HasForeignKey("StockId");
                });

            modelBuilder.Entity("Red20.Model.Entity.PurchaseOrder", b =>
                {
                    b.HasOne("Red20.Model.Entity.PurchaseRequisition", "PurchaseRequisition")
                        .WithMany("PurchaseOrders")
                        .HasForeignKey("PurchaseRequisitionId");

                    b.HasOne("Red20.Model.Entity.Supplier", "Supplier")
                        .WithMany()
                        .HasForeignKey("SupplierId");
                });

            modelBuilder.Entity("Red20.Model.Entity.PurchaseOrderCreditNoteItem", b =>
                {
                    b.HasOne("Red20.Model.Entity.PurchaseOrderItem", "PurchaseOrderItem")
                        .WithMany()
                        .HasForeignKey("PurchaseOrderItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Red20.Model.Entity.PurchaseOrderItem", b =>
                {
                    b.HasOne("Red20.Model.Entity.Order", "Order")
                        .WithMany()
                        .HasForeignKey("OrderId");

                    b.HasOne("Red20.Model.Entity.PurchaseOrder", "PurchaseOrder")
                        .WithMany("PurchaseOrderItems")
                        .HasForeignKey("PurchaseOrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Red20.Model.Entity.Stock", "Stock")
                        .WithMany()
                        .HasForeignKey("StockId");
                });

            modelBuilder.Entity("Red20.Model.Entity.PurchaseOrderItemLog", b =>
                {
                    b.HasOne("Red20.Model.Entity.PurchaseOrderItem", "PurchaseOrderItem")
                        .WithMany("PurchaseOrderItemLogs")
                        .HasForeignKey("PurchaseOrderItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Red20.Model.Entity.PurchaseOrderItemSerialNumber", b =>
                {
                    b.HasOne("Red20.Model.Entity.PurchaseOrderItem", "PurchaseOrderItem")
                        .WithMany("PurchaseOrderItemSerialNumbers")
                        .HasForeignKey("PurchaseOrderItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Red20.Model.Entity.PurchaseRequisition", b =>
                {
                    b.HasOne("Red20.Model.Entity.Order", "Order")
                        .WithMany("PurchaseRequisitions")
                        .HasForeignKey("OrderId");

                    b.HasOne("Red20.Model.Entity.Supplier", "Supplier")
                        .WithMany()
                        .HasForeignKey("SupplierId");
                });

            modelBuilder.Entity("Red20.Model.Entity.PurchaseRequisitionItem", b =>
                {
                    b.HasOne("Red20.Model.Entity.PurchaseRequisition", "PurchaseRequisition")
                        .WithMany("PurchaseRequisitionItems")
                        .HasForeignKey("PurchaseRequisitionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Red20.Model.Entity.Quote", b =>
                {
                    b.HasOne("Red20.Model.Entity.CustomerAddress", "CustomerAddress")
                        .WithMany()
                        .HasForeignKey("CustomerAddressId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Red20.Model.Entity.CustomerContact", "CustomerContact")
                        .WithMany()
                        .HasForeignKey("CustomerContactId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Red20.Model.Entity.Customer", "Customer")
                        .WithMany()
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Red20.Model.Entity.Enquiry", "Enquiry")
                        .WithMany("Quotes")
                        .HasForeignKey("EnquiryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Red20.Model.Entity.QuoteItem", b =>
                {
                    b.HasOne("Red20.Model.Entity.Quote", "Quote")
                        .WithMany("QuoteItems")
                        .HasForeignKey("QuoteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Red20.Model.Entity.Stock", b =>
                {
                    b.HasOne("Red20.Model.Entity.AnalysisCode", "AnalysisCode")
                        .WithMany()
                        .HasForeignKey("AnalysisCodeId");

                    b.HasOne("Red20.Model.Entity.Category", "Category")
                        .WithMany()
                        .HasForeignKey("CategoryId");

                    b.HasOne("Red20.Model.Entity.Supplier", "Supplier")
                        .WithMany()
                        .HasForeignKey("SupplierId");
                });

            modelBuilder.Entity("Red20.Model.Entity.StockAllocation", b =>
                {
                    b.HasOne("Red20.Model.Entity.Stock", "Stock")
                        .WithMany()
                        .HasForeignKey("StockId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Red20.Model.Entity.StockAssembly", b =>
                {
                    b.HasOne("Red20.Model.Entity.Stock", "AssemblyStock")
                        .WithMany("StockAssemblies")
                        .HasForeignKey("AssemblyStockId");
                });

            modelBuilder.Entity("Red20.Model.Entity.StockAssemblyMovementItem", b =>
                {
                    b.HasOne("Red20.Model.Entity.StockAssemblyMovement", "StockAssemblyMovement")
                        .WithMany("StockAssemblyMovementItems")
                        .HasForeignKey("StockAssemblyMovementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Red20.Model.Entity.Stock", "Stock")
                        .WithMany()
                        .HasForeignKey("StockId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Red20.Model.Entity.StockMovementItem", b =>
                {
                    b.HasOne("Red20.Model.Entity.Order", "Order")
                        .WithMany()
                        .HasForeignKey("OrderId");

                    b.HasOne("Red20.Model.Entity.Stock", "Stock")
                        .WithMany()
                        .HasForeignKey("StockId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Red20.Model.Entity.StockMovement", "StockMovement")
                        .WithMany("StockMovementItems")
                        .HasForeignKey("StockMovementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Red20.Model.Entity.StockSerialTracking", b =>
                {
                    b.HasOne("Red20.Model.Entity.Stock", "Stock")
                        .WithMany("StockSerialTrackings")
                        .HasForeignKey("StockId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Red20.Model.Entity.StockSerialTrackingLog", b =>
                {
                    b.HasOne("Red20.Model.Entity.StockSerialTracking", "StockSerialTracking")
                        .WithMany("StockSerialTrackingLogs")
                        .HasForeignKey("StockSerialTrackingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Red20.Model.Entity.StockTransaction", b =>
                {
                    b.HasOne("Red20.Model.Entity.Stock", "Stock")
                        .WithMany()
                        .HasForeignKey("StockId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Red20.Model.Entity.SupplierAddress", b =>
                {
                    b.HasOne("Red20.Model.Entity.Supplier", "Supplier")
                        .WithMany("SupplierAddresses")
                        .HasForeignKey("SupplierId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Red20.Model.Entity.SupplierContact", b =>
                {
                    b.HasOne("Red20.Model.Entity.SupplierAddress", "SupplierAddress")
                        .WithMany("SupplierContacts")
                        .HasForeignKey("SupplierAddressId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Red20.Model.Entity.Supplier", "Supplier")
                        .WithMany()
                        .HasForeignKey("SupplierId");
                });

            modelBuilder.Entity("Red20.Model.Entity.UserToken", b =>
                {
                    b.HasOne("Red20.Model.Entity.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });
#pragma warning restore 612, 618
        }
    }
}
