﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Red20.Excel.Export;
using Red20.Model.Data.Document;
using Red20.Model.Data.Lookup;
using Red20.Model.Data.PurchaseRequisition;
using Red20.Model.Entity;
using Red20.Service.Data.Interface;
using Red20.Service.Storage.Interface;
using Red20.Service.Xero.Interface;
using Red20.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Red20.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class PurchaseRequisitionController : ControllerBase
    {

        private IPurchaseRequisitionService purchaseRequisitionService;
        private IUserService userService;
        private ISupplierService supplierService;
        private IOrderService orderService;
        private IDocumentService documentService;
        private IStorageService blobStorage;
        private ILogger<AuthController> logger;
        IUnitOfWork unitOfWork;
        IXeroService xeroService;

        public PurchaseRequisitionController(
            IPurchaseRequisitionService purchaseRequisitionService,
            IUserService userService,
            ISupplierService supplierService,
            IOrderService orderService,
            IDocumentService documentService,
            IStorageService blobStorage,
            IUnitOfWork unitOfWork,
             IXeroService xeroService,
        ILogger<AuthController> logger)
        {

            this.purchaseRequisitionService = purchaseRequisitionService;
            this.userService = userService;
            this.supplierService = supplierService;
            this.orderService = orderService;
            this.unitOfWork = unitOfWork;
            this.xeroService = xeroService;
            this.blobStorage = blobStorage;
            this.documentService = documentService;
            this.logger = logger;
        }

        [HttpGet]
        [ProducesResponseType(200, Type = typeof(IList<PurchaseRequisitionModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get([FromQuery] PurchaseRequisitionFilterParams filterParams)
        {
            try
            {
                var purchaseRequisitions = await purchaseRequisitionService.GetPaginatedPurchaseRequisitions(filterParams);
                return Ok(purchaseRequisitions);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot create Purchase Order - {ex}");
                return BadRequest();
            }
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(PurchaseRequisitionModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id)
        {

            var purchaseRequisition = await purchaseRequisitionService.GetAsync(id);

            return Ok(purchaseRequisition);
        }

        [HttpPost("createNewPurchaseReq/{id}")]
        [ProducesResponseType(200, Type = typeof(PurchaseRequisitionModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> CreateNewPurchaseReq(Guid id, [FromBody] PurchaseRequisitionUpdateModel model)
        {

            var order = await orderService.GetOrderById(id);
            var accountCode = order.AccountCode.Substring(1, 4);
            var newAccountCode = $"2{accountCode}";
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);

            model = new PurchaseRequisitionUpdateModel
            {
                AccountCode = newAccountCode,
                OrderId = order.OrderId,
                DueDate = order.IssuedDate,
                Date = DateTime.Now.Date,
                CreatedBy = user.Name,
                CreatedByEmailAddress = user.EmailAddress,
                Currency = order.Currency,
                DepartmentNumber = "001",
                SupplierId = model.SupplierId,
                DeliveryAddress = model.Address,
                IsCollection = model.IsCollection,
                Number = await NumberSequenceUtility.GetNextPurchaseRequisitionNumber(unitOfWork)
            };
            var purchaseRequisition = await purchaseRequisitionService.PostAsync(model);

            return Ok(purchaseRequisition);
        }

        [HttpGet("byEnquiry/{id}")]
        [ProducesResponseType(200, Type = typeof(IList<PurchaseRequisitionModel>))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetByOrder(Guid id)
        {

            var purchaseRequisitions = await purchaseRequisitionService.GetByOrderAsync(id);

            return Ok(purchaseRequisitions.ToList());
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(PurchaseRequisitionModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody] PurchaseRequisitionUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }

            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                      c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);

            if (model.DeliveryAddress.Contains("Oldmeldrum") || model.DeliveryAddress.Contains("West Bromwich"))
            {
                model.IsInternalDeliveryAddress = true;
            }
            model.Date = DateTime.Now.Date;
            model.CreatedBy = user.Name;
            model.CreatedByEmailAddress = user.EmailAddress;
            model.Number = await NumberSequenceUtility.GetNextPurchaseRequisitionNumber(unitOfWork);

            try
            {
                var purchaseRequisition = await purchaseRequisitionService.PostAsync(model);
                return Ok(purchaseRequisition);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot create Purchase Requisition - {ex}");
                return BadRequest();
            }
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(PurchaseRequisitionModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Put(Guid id, [FromBody] PurchaseRequisitionUpdateModel model)
        {
            if (model is null)
            {
                return BadRequest();
            }

            var purchaseRequisition = await purchaseRequisitionService.GetAsync(id);

            if (purchaseRequisition is null)
            {
                return BadRequest();
            }

            try
            {
                purchaseRequisition = await purchaseRequisitionService.PutAsync(id, model);
                return Ok(purchaseRequisition);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot create Purchase Requisition - {ex}");
                return BadRequest();
            }
        }

        [HttpGet("accountCodes")]
        [ProducesResponseType(200, Type = typeof(IList<AccountCodeModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetAccountCodes()
        {
            var accountCodes = await xeroService.GetAccountCodesAsync();
            return Ok(accountCodes);
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Delete(Guid id)
        {

            await purchaseRequisitionService.DeleteAsync(id);
            return Ok();
        }

        [HttpPost("export")]
        [ProducesResponseType(200)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> GenerateReport(List<PurchaseRequisitionModel> models)
        {
            try
            {
                var document = new DocumentModel();
                var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
                if (emailClaim == null)
                {
                    return Unauthorized();
                }

                var user = await userService.GetUserByEmailAsync(emailClaim.Value);
                var currentUser = $"{user.Firstname} {user.Lastname}";
                var itemData = ExportUtility.ExportPurchaseRequisitionReport(models, currentUser);
                document = await documentService.PostAsync("Purchase_Requistion_Report.xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"{user.Firstname} {user.Lastname}", "Purchase Requisitions", null, null, null, false, false, false);
                var file = File(itemData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                await blobStorage.UploadAsync(document.DocumentId, itemData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                return Ok();
            } catch (Exception)
            {
                return NotFound();
            }
        }
        [HttpGet("downloadReport")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> DownloadReport()
        {
            try
            {
                var lastDocument = await unitOfWork.Document.Query().OrderByDescending(c => c.Created).FirstOrDefaultAsync();
                var download = await blobStorage.DownloadAsync(lastDocument.DocumentId);
                var file = File(download.Item1, download.Item2);
                return file;
            } catch (Exception)
            {
                return NotFound();
            }
        }
    }
}