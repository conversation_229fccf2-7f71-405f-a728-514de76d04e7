﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace Red20.Data.Migrations
{
    public partial class XeroCustomerSupplierId : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder) {
            migrationBuilder.AddColumn<string>(
                name: "XeroCustomerId",
                table: "Customers",
                nullable: true);
            migrationBuilder.AddColumn<string>(
                name: "XeroSupplierId",
                table: "Suppliers",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder) {
            migrationBuilder.DropColumn(
                name: "XeroCustomerId",
                table: "Customers");
            migrationBuilder.DropColumn(
                name: "XeroSupplierId",
                table: "Suppliers");
        }
    }
}
