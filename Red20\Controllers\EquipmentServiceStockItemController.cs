﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Red20.Model.Data.Order;
using Red20.Model.Data.StockMovement;
using Red20.Service.Data.Interface;

namespace Red20.Controllers {
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class EquipmentServiceStockItemController : ControllerBase {
        private IEquipmentServiceStockItemService service;
        private IEquipmentServiceHistoryService serviceHistoryService;
        private IOrderAssemblyService orderAssemblyService;
        private IStockService stockService;
        private IOrderService orderService;
        private IJobService jobService;
        private IStockMovementItemService stockMovementItemService;
        private IHireEquipmentService hireEquipmentService;
        private ILogger<AuthController> logger;

        public EquipmentServiceStockItemController(
            IEquipmentServiceStockItemService service,
            IEquipmentServiceHistoryService serviceHistoryService,
            IStockService stockService,
            IOrderAssemblyService orderAssemblyService,
            IOrderService orderService,
            IJobService jobService,
            IStockMovementItemService stockMovementItemService,
            IHireEquipmentService hireEquipmentService,
            ILogger<AuthController> logger) {
            this.service = service;
            this.serviceHistoryService = serviceHistoryService;
            this.stockService = stockService;
            this.orderAssemblyService = orderAssemblyService;
            this.orderService = orderService;
            this.jobService = jobService;
            this.stockMovementItemService = stockMovementItemService;
            this.hireEquipmentService = hireEquipmentService;
            this.logger = logger;
        }

        [HttpGet("byServiceHistory/{id}")]
        [ProducesResponseType(200, Type = typeof(IList<EquipmentServiceStockItemModel>))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id) {
            return Ok(await service.GetByEquipmentServiceHistoryAsync(id));
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(EquipmentServiceStockItemModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody]EquipmentServiceStockItemUpdateModel model) {

            if (model is null) {
                return BadRequest();
            }

            var stock = await stockService.GetAsync(model.StockId);
            if (stock is null) {
                return BadRequest();
            }

            var equipmentServiceHistory = model.EquipmentServiceHistoryId == Guid.Empty ? null : await serviceHistoryService.GetAsync(model.EquipmentServiceHistoryId);
            if (equipmentServiceHistory is null) {
                var serviceHistory = new EquipmentServiceHistoryUpdateModel();
                serviceHistory.OrderId = model.OrderId;
                serviceHistory.HireEquipmentId = model.HireEquipmentId;
                serviceHistory.InspectedBy = !string.IsNullOrWhiteSpace(model.InspectedBy) ? model.InspectedBy : null;
                serviceHistory.InspectedDate = model.InspectedDate.HasValue ? model.InspectedDate : null;
                serviceHistory.Notes = !string.IsNullOrWhiteSpace(model.Notes) ? model.Notes : null;
                serviceHistory.OrderAssemblyId = model.OrderAssemblyId;
                serviceHistory.OrderNumber = model.OrderNumber;

                try {
                    equipmentServiceHistory = await serviceHistoryService.PostAsync(serviceHistory);
                } catch (Exception ex) {
                    logger.LogError($"Cannot create Equipment Service History Record - {ex}");
                    return BadRequest();
                }
            }

            try {
                model.StockCode = stock.Code;
                model.EquipmentServiceHistoryId = equipmentServiceHistory.EquipmentServiceHistoryId;
                var equipmentServiceStockItem = await service.PostAsync(model);
                return Ok(equipmentServiceStockItem);
            } catch (Exception ex) {
                logger.LogError($"Cannot Equipment Service Stock Item - {ex}");
                return BadRequest();
            }
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(EquipmentServiceStockItemModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Put(Guid id, [FromBody]EquipmentServiceStockItemUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }

            var equipmentServiceStockItem = await service.GetAsync(id);
            if (equipmentServiceStockItem is null) {
                return BadRequest();
            }

            var stock = await stockService.GetAsync(model.StockId);
            if (stock is null) {
                return BadRequest();
            }

            try {
                model.StockCode = stock.Code;
                equipmentServiceStockItem = await service.PutAsync(id, model);
                return Ok(equipmentServiceStockItem);
            } catch (Exception ex) {
                logger.LogError($"Cannot update Equipment Service Stock Item - {ex}");
                return BadRequest();
            }
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Delete(Guid id) {
            if (!await service.EquipmentServiceStockItemIdExistsAsync(id)) {
                return BadRequest();
            }

            await service.DeleteAsync(id);
            return Ok();
        }
    }
}