﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Red20.Model.Data;
using Red20.Service.Data.Interface;
using System.Security.Claims;
using Red20.Service.Email.Interface;
using Red20.Settings;
using Microsoft.Extensions.Options;
using Red20.Service.Data;
using Red20.Service.Storage.Interface;
using Microsoft.EntityFrameworkCore;
using Red20.Model.Data.Document;
using Red20.Excel.Export;
using System.Text;
using Red20.Service.Xero.Interface;
using Red20.Model.Entity;
using AutoMapper;

namespace Red20.Controllers {
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class UserCheckInController : ControllerBase {

        private IUserService userService;
        private IUserCheckInService userCheckInService;
        private IUserCheckInCheckOutService userCheckInCheckOutService;
        private IDocumentService documentService;
        private ILogger<AuthController> logger;
        private ClientSettings settings;
        private IUnitOfWork unitOfWork;
        private IMapper mapper;
        private IStorageService blobStorage;

        public UserCheckInController(
            IUserService userService,
            IUserCheckInService userCheckInService,
            IUserCheckInCheckOutService userCheckInCheckOutService,
            IDocumentService documentService,
            IStorageService blobStorage,
            IOptions<ClientSettings> settings,
            IUnitOfWork unitOfWork,
            IMapper mapper,
            ILogger<AuthController> logger) {

            this.userService = userService;
            this.userCheckInService = userCheckInService;
            this.userCheckInCheckOutService = userCheckInCheckOutService;
            this.documentService = documentService;
            this.settings = settings.Value;
            this.logger = logger;
            this.mapper = mapper;
            this.unitOfWork = unitOfWork;
            this.blobStorage = blobStorage;
        }

        [HttpGet]
        [ProducesResponseType(200, Type = typeof(IList<UserCheckInModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get() {
            var today = DateTime.Now.Date;
            var userCheckIns = await userCheckInService.GetAsync();
            var todaysCheckinUsers = userCheckIns.Where(c => c.Created.Date == today);
            return Ok(todaysCheckinUsers);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(200, Type = typeof(UserCheckInModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get(Guid id) {

            var user = await userCheckInService.GetAsync(id);

            return Ok(user);
        }

        [HttpPut("checkIn/{id}")]
        [ProducesResponseType(200, Type = typeof(UserCheckInModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> CheckIn(Guid id, [FromBody] UserCheckInUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }

            var isDaylightSaving = TimeZoneInfo.Local.IsDaylightSavingTime(DateTime.UtcNow);

            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                      c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);

            var userCheckIn = await userCheckInService.GetAsync(id);

            model.ModifiedBy = $"{user.Firstname} {user.Lastname}";
            model.LastCheckedIn = isDaylightSaving ? DateTime.UtcNow.AddHours(1) : DateTime.UtcNow;
            model.LastCheckedOut = null;
            model.LastTotalHours = null;

            var newCheckIn = new UserCheckInCheckOutUpdateModel {
                UserCheckInId = id,
                UserCheckInCheckOutId = Guid.NewGuid(),
                CheckIn = isDaylightSaving ? DateTime.UtcNow.AddHours(1) : DateTime.UtcNow,
                CheckInUserId = userCheckIn.UserId,
                ChecInUsername =userCheckIn.Username
            };

            await userCheckInCheckOutService.PostAsync(newCheckIn);

           userCheckIn = await userCheckInService.PutAsync(id, model);

            return Ok(userCheckIn);
        }

        [HttpPut("checkOut/{id}")]
        [ProducesResponseType(200, Type = typeof(UserCheckInModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> CheckOut(Guid id, [FromBody] UserCheckInUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }

            var isDaylightSaving = TimeZoneInfo.Local.IsDaylightSavingTime(DateTime.UtcNow);

            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);

            var userCheckIn = await userCheckInService.GetAsync(id);

            var userCheckInCheckOut =  unitOfWork.UserCheckInCheckOut.Query().Where(c => c.UserCheckInId == id).OrderByDescending(c => c.Created).FirstOrDefault();

            model.ModifiedBy = $"{user.Firstname} {user.Lastname}";

            model.LastCheckedOut = isDaylightSaving ? DateTime.UtcNow.AddHours(1) : DateTime.UtcNow;

            model.LastTotalHours = (model.LastCheckedOut.Value - userCheckIn.LastCheckedIn.Value).TotalHours;

            userCheckInCheckOut.CheckOut = isDaylightSaving ? DateTime.UtcNow.AddHours(1) : DateTime.UtcNow;

            userCheckInCheckOut.TotalHours = (userCheckInCheckOut.CheckOut.Value - userCheckIn.LastCheckedIn.Value).TotalHours;

            await userCheckInCheckOutService.PutAsync(userCheckInCheckOut.UserCheckInCheckOutId, mapper.Map<UserCheckInCheckOutUpdateModel>(userCheckInCheckOut));

            userCheckIn = await userCheckInService.PutAsync(id, model);

            return Ok(userCheckIn);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(200, Type = typeof(UserCheckInModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Put(Guid id, [FromBody] UserCheckInUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);

            model.ModifiedBy = $"{user.Firstname} {user.Lastname}";

            var userCheckIn = await userCheckInService.PutAsync(id, model);
            return Ok(userCheckIn);
        }
        [HttpGet("byCheckIn/{id}")]
        [ProducesResponseType(200, Type = typeof(UserCheckInCheckOutModel))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetByUserCheckIn(Guid id) {
            try {
                var checkInCheckOuts = await userCheckInCheckOutService.GetByUserCheckInIdAsync(id);
                return Ok(checkInCheckOuts);
            } catch (Exception ex) {
                var x = ex;
                return BadRequest();
            }
        }

        [HttpPut("updateCheckInCheckOut/{id}")]
        [ProducesResponseType(200, Type = typeof(UserCheckInCheckOutModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> UpdateTime(Guid id, [FromBody] UserCheckInCheckOutUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }
            var isDaylightSaving = TimeZoneInfo.Local.IsDaylightSavingTime(DateTime.UtcNow);
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);

            model.TotalHours = model.CheckOut.HasValue ? (model.CheckOut.Value - model.CheckIn.Value).TotalHours : 0;
            model.CheckIn = model.CheckIn.Value;
            if (model.CheckOut.HasValue) {
                model.CheckOut = model.CheckOut.Value;
            }
            model.Modified = DateTime.Now;
            var userCheckInCheckOut = await userCheckInCheckOutService.PutAsync(id, model);

            var userCheckIn = await unitOfWork.UserCheckIn.GetAsync(model.UserCheckInId);

            userCheckIn.LastCheckedIn = model.CheckIn;
            if (model.CheckOut.HasValue) {
                userCheckIn.LastCheckedOut = model.CheckOut;
            }
            userCheckIn.LastTotalHours = model.TotalHours;

            await userCheckInService.PutAsync(model.UserCheckInId, mapper.Map<UserCheckInUpdateModel>(userCheckIn));

            return Ok(userCheckInCheckOut);
        }

        //public async Task<IActionResult> UpdateTime(Guid id, [FromBody] UserCheckInCheckOutUpdateModel model) {
        //    if (model is null) {
        //        return BadRequest();
        //    }
        //    var isCheckInDaylight = model.CheckIn.Value.IsDaylightSavingTime();
        //    var nameClaim = User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name);
        //    var user = await userService.GetAsync(Guid.Parse(nameClaim.Value));

        //    var getUserCheckInCheckOut = await userCheckInCheckOutService.GetAsync(id);
        //    var checkin = model.CheckIn.HasValue ? model.CheckIn.Value : getUserCheckInCheckOut.CheckIn.Value;
        //    var checkOut = model.CheckOut.HasValue ? model.CheckOut.Value : getUserCheckInCheckOut.CheckOut.Value;
        //    model.TotalHours = model.CheckOut.HasValue ? (checkOut - checkin).TotalHours : 0;

        //    if (checkOut != null) {
        //        model.CheckOut = checkOut;
        //    }
        //    model.Modified = DateTime.Now;
        //    var userCheckInCheckOut = await userCheckInCheckOutService.PutAsync(id, model);

        //    var userCheckIn = await unitOfWork.UserCheckIn.GetAsync(model.UserCheckInId);

        //    userCheckIn.LastCheckedIn = checkin;
        //    if (checkOut != null) {
        //        userCheckIn.LastCheckedOut = checkOut;
        //    }
        //    userCheckIn.LastTotalHours = model.TotalHours;

        //    await userCheckInService.PutAsync(model.UserCheckInId, mapper.Map<UserCheckInUpdateModel>(userCheckIn));

        //    return Ok(userCheckInCheckOut);
        //}

        [HttpPut("saveCheckIn/{employeeCode}")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> UpdateCheckInTime(string employeeCode) {
            var user = await unitOfWork.User.Query().Where(c => c.EmployeeCode == employeeCode).FirstOrDefaultAsync();
            var isDaylightSaving = TimeZoneInfo.Local.IsDaylightSavingTime(DateTime.UtcNow);
            var userCheckIn = await unitOfWork.UserCheckIn.Query().Include(x => x.UserCheckInCheckOuts).Where(c => c.UserId == user.UserId).OrderByDescending(c => c.Created).FirstOrDefaultAsync();

            userCheckIn.LastCheckedIn = isDaylightSaving ? DateTime.UtcNow.AddHours(1) : DateTime.UtcNow;

            userCheckIn.LastCheckedOut = null;

            userCheckIn.Modified = isDaylightSaving ? DateTime.UtcNow.AddHours(1) : DateTime.UtcNow;

            userCheckIn.ModifiedBy = $"{user.Firstname} {user.Lastname}";

            var checkInCheckOut = new UserCheckInCheckOutUpdateModel {
                UserCheckInId = userCheckIn.UserCheckInId,
                UserCheckInCheckOutId = Guid.NewGuid(),
                CheckIn = isDaylightSaving ? DateTime.UtcNow.AddHours(1) : DateTime.UtcNow,
                CheckInUserId = userCheckIn.UserId,
                ChecInUsername = $"{user.Firstname} {user.Lastname}"
            };

            await userCheckInCheckOutService.PostAsync(checkInCheckOut);

            unitOfWork.UserCheckIn.Update(userCheckIn);

            await unitOfWork.SaveChangesAsync();

            return Ok();
        }

        [HttpPut("saveCheckOut/{employeeCode}")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> UpdateCheckOutTime(string employeeCode) {
            var user = await unitOfWork.User.Query().Where(c => c.EmployeeCode == employeeCode).FirstOrDefaultAsync();

            var userCheckIn = await unitOfWork.UserCheckIn.Query().Where(c => c.UserId == user.UserId).OrderByDescending(c => c.LastCheckedIn).FirstOrDefaultAsync();

            var isDaylightSaving = TimeZoneInfo.Local.IsDaylightSavingTime(DateTime.UtcNow);

            userCheckIn.LastCheckedOut = isDaylightSaving ? DateTime.UtcNow.AddHours(1) : DateTime.UtcNow;

            userCheckIn.LastTotalHours = (userCheckIn.LastCheckedOut.Value - userCheckIn.LastCheckedIn.Value).TotalHours;

            userCheckIn.Modified = isDaylightSaving ? DateTime.UtcNow.AddHours(1) : DateTime.UtcNow;

            userCheckIn.ModifiedBy = $"{user.Firstname} {user.Lastname}";

            var userCheckInCheckOut = await unitOfWork.UserCheckInCheckOut.Query().Where(c => c.UserCheckInId == userCheckIn.UserCheckInId).OrderByDescending(z => z.Created).FirstOrDefaultAsync();

            userCheckInCheckOut.CheckOut = userCheckIn.LastCheckedOut;

            userCheckInCheckOut.TotalHours = (userCheckIn.LastCheckedOut.Value - userCheckInCheckOut.CheckIn.Value).TotalHours;

            userCheckInCheckOut.Modified = isDaylightSaving ? DateTime.UtcNow.AddHours(1) : DateTime.UtcNow;

            unitOfWork.UserCheckInCheckOut.Update(userCheckInCheckOut);

            unitOfWork.UserCheckIn.Update(userCheckIn);

            await unitOfWork.SaveChangesAsync();

            return Ok();
        }


        [HttpGet("getUserCheckIn/{employeeCode}")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> CheckUserCheckIn(string employeeCode) {

            bool hasCheckIn = false;

            var user = await unitOfWork.User.Query().Where(c => c.EmployeeCode == employeeCode).FirstOrDefaultAsync();

            var userCheckIn = await unitOfWork.UserCheckIn.Query().Where(c => c.UserId == user.UserId).OrderByDescending(c => c.Created).FirstOrDefaultAsync();

            if(userCheckIn.LastCheckedIn.HasValue && !userCheckIn.LastCheckedOut.HasValue) {
                hasCheckIn = true;
            }

            return Ok(hasCheckIn);
        }
        [HttpGet("printReport/{from}/{to}")]
        [ProducesResponseType(200, Type = typeof(FileContentResult))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> PrintReport(DateTime from, DateTime to) {
            try {
                var document = new DocumentModel();
                var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                       c.Type == ClaimTypes.Email);
                if (emailClaim == null)
                {
                    return Unauthorized();
                }

                var user = await userService.GetUserByEmailAsync(emailClaim.Value);
                var currentUser = $"{user.Firstname} {user.Lastname}";
                var checkInCheckOuts = unitOfWork.UserCheckInCheckOut.Query().Include(x => x.UserCheckIn).Where(c => c.CheckIn.HasValue && c.CheckIn.Value.Date >= from.Date && c.CheckIn.Value.Date <= to.Date).ToList();
                var itemData = ExportUtility.ExportCheckInCheckOut(checkInCheckOuts, from.Date.ToString("dd/MMM/yyyy"), to.Date.ToString("dd/MMM/yyyy"));

                document = await documentService.PostAsync("CheckIn_CheckOut_Report.xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"{user.Firstname} {user.Lastname}", "Users", null, null, null, false, false, false);
                var file = File(itemData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                await blobStorage.UploadAsync(document.DocumentId, itemData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                return file;
            } catch (Exception ex) {
                return BadRequest();
            }
        }
    }
}