﻿using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Red20.Model.Auth;
using Red20.Service.Security.Interface;

namespace Red20.Controllers {    
    [Route("api/[controller]")]
    [ApiController]
    public class AuthController : ControllerBase {

        private IAuthService authService;
        private ILogger<AuthController> _logger;

        public AuthController(
            IAuthService authService,
            ILogger<AuthController> _logger) {

            this.authService = authService;
            this._logger = _logger;
        }


        [HttpPost("token")]
        [ProducesResponseType(200, Type = typeof(TokenModel))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Token([FromBody]AuthModel model) {


            try {
                var result = await authService.Authenticate(model, model.IsEngineer);
                if (result == null || result.AccessToken == null) {
                    _logger.LogWarning("User cannot be found. Please check your username and password and try again.");
                    return Unauthorized();
                }
                return Ok(result);
            } catch (System.Exception ex) {
                return Unauthorized($"{ex}");
            }
        }

        //[HttpPost("engineerToken")]
        //[AllowAnonymous]
        //[ProducesResponseType(200, Type = typeof(TokenModel))]
        //[ProducesResponseType(401)]
        //public async Task<IActionResult> EngineerToken([FromBody] AuthModel model) {
        //    var result = await authService.Authenticate(model, engineerLogin: true);

        //    if (result == null || result.AccessToken == null) {
        //        _logger.LogWarning("User cannot be found. Please check your username and password and try again.");
        //        return Unauthorized();
        //    }


        //    return Ok(result);
        //}

        [HttpPost("refreshToken")]
        [ProducesResponseType(200, Type = typeof(TokenModel))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> RefreshToken([FromBody]TokenModel model) {
            var result = await authService.RefreshToken(model);

            if (result == null || result.AccessToken == null) {
                return Unauthorized();
            }

            return Ok(result);
        }
    }
}