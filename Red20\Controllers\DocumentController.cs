﻿using Hangfire;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Net.Http.Headers;
using Red20.Controllers.ControllerExtensions;
using Red20.Excel.Import;
using Red20.Model.Data.Document;
using Red20.Model.Entity;
using Red20.Service.Data.Interface;
using Red20.Service.Email.Interface;
using Red20.Service.Storage.Interface;
using Red20.Settings;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Red20.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class DocumentController : ControllerBase
    {
        private IDocumentService service;
        private IEmailService emailService;
        private IUserService userService;
        private ILogger<DocumentController> logger;
        private IStorageService blobStorage;
        private IUnitOfWork unitOfWork;
        private ClientSettings settings;

        public DocumentController(IDocumentService service,
            IUserService userService,
            ILogger<DocumentController> logger,
            IStorageService blobStorage,
            IUnitOfWork unitOfWork,
            IEmailService emailService,
            IOptions<ClientSettings> settings)
        {
            this.service = service;
            this.userService = userService;
            this.logger = logger;
            this.blobStorage = blobStorage;
            this.unitOfWork = unitOfWork;
            this.emailService = emailService;
            this.settings = settings.Value;
        }

        [HttpGet]
        [ProducesResponseType(200, Type = typeof(IList<DocumentModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Get()
        {
            var documents = service.GetDocuments();
            return Ok(documents);
        }

        [HttpGet("byType/{type}")]
        [ProducesResponseType(200, Type = typeof(IList<DocumentModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetByType(string type)
        {
            var documents = await service.GetByTypeAsync(type);
            return Ok(documents);
        }

        [HttpGet("byImport")]
        [ProducesResponseType(200, Type = typeof(IList<DocumentModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetByImportType()
        {
            var documents = await service.GetByTypeAsync("Import");
            return Ok(documents);
        }

        [HttpGet("byCompanyId/{id}")]
        [ProducesResponseType(200, Type = typeof(IList<DocumentModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetByCompanyId(Guid id)
        {
            var documents = await service.GetByCompanyIdAsync(id);
            return Ok(documents);
        }

        [HttpGet("byComplaintId/{id}")]
        [ProducesResponseType(200, Type = typeof(IList<DocumentModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetByComplaintId(Guid id)
        {
            var documents = await service.GetByComplaintIdAsync(id);
            return Ok(documents);
        }

        [HttpGet("byPurchaseOrderId/{id}")]
        [ProducesResponseType(200, Type = typeof(IList<DocumentModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetByPurchaseOrderId(Guid id)
        {
            var documents = await service.GetByPurchaseOrderIdAsync(id);
            return Ok(documents);
        }

        [HttpGet("quoteAttachments/{id}")]
        [ProducesResponseType(200, Type = typeof(IList<DocumentModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetQuoteAttachments(Guid id)
        {
            var documents = await service.GetQuoteAttachmentsAsync(id);
            return Ok(documents);
        }

        [HttpGet("orderAttachments/{id}")]
        [ProducesResponseType(200, Type = typeof(IList<DocumentModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetOrderAttachments(Guid id)
        {
            var documents = await service.GetOrderAttachmentsAsync(id);
            return Ok(documents);
        }

        [HttpGet("stockValuationReport")]
        [ProducesResponseType(200, Type = typeof(IList<DocumentModel>))]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetStockValuationReports()
        {
            var documents = await service.GetStockValuationReportsAsync();
            return Ok(documents);
        }
        [HttpPost("import")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> ImportStock([FromForm] IFormFile file)
        {
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);
            var document = new DocumentModel();

            if (file is null || file.Length == 0)
            {
                return BadRequest();
            }

            try
            {
                document = await service.PostAsync(file.FileName, file.ContentType, $"{user.Firstname} {user.Lastname}", string.Empty, null, null, null, false, false, false, null);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot create document - {ex}");
                return BadRequest();
            }

            try
            {
                var fileData = this.FileToByteArray(file);
                var username = $"{user.Firstname} {user.Lastname}";
                BackgroundJob.Enqueue<ImportUtility>(task => task.ImportHireEquipment(fileData));
                //BackgroundJob.Enqueue<ImportUtility>(task => task.ImportAsset(username, fileData));
                //BackgroundJob.Enqueue<ImportUtility>(task => task.ImportStock(username, fileData));

                await blobStorage.UploadAsync(document.DocumentId, fileData, file.ContentType);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot upload document to Azure Storage - {ex}");
            }

            return Ok(document);
        }

        [HttpPost("{id}")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromForm] IFormFile file, Guid id)
        {
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);
            var document = new DocumentModel();

            if (file is null || file.Length == 0)
            {
                return BadRequest();
            }

            try
            {
                document = await service.PostAsync(file.FileName, file.ContentType, $"{user.Firstname} {user.Lastname}", string.Empty, id, null, null, false, false, false, null);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot create document - {ex}");
                return BadRequest();
            }

            try
            {
                var fileData = this.FileToByteArray(file);
                await blobStorage.UploadAsync(document.DocumentId, fileData, file.ContentType);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot upload document to Azure Storage - {ex}");
            }

            return Ok(document);
        }

        [HttpPost("complaintDocument/{id}")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> PostComplaintDocument([FromForm] IFormFile file, Guid id, string type)
        {
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                      c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);
            var document = new DocumentModel();

            if (file is null || file.Length == 0)
            {
                return BadRequest();
            }

            try
            {
                document = await service.PostAsync(file.FileName, file.ContentType, $"{user.Firstname} {user.Lastname}", string.Empty, null, id, null, false, false, false, null);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot create document - {ex}");
                return BadRequest();
            }

            try
            {
                var fileData = this.FileToByteArray(file);
                await blobStorage.UploadAsync(document.DocumentId, fileData, file.ContentType);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot upload document to Azure Storage - {ex}");
            }

            return Ok(document);
        }

        [HttpPost("supplierDocument/{id}")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> PostSupplierDocument([FromForm] IFormFile file, Guid id)
        {
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);
            var document = new DocumentModel();

            if (file is null || file.Length == 0)
            {
                return BadRequest();
            }

            try
            {
                document = await service.PostAsync(file.FileName, file.ContentType, $"{user.Firstname} {user.Lastname}", string.Empty, id, null, null, false, false, false, null);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot create document - {ex}");
                return BadRequest();
            }

            try
            {
                var fileData = this.FileToByteArray(file);
                await blobStorage.UploadAsync(document.DocumentId, fileData, file.ContentType);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot upload document to Azure Storage - {ex}");
            }

            return Ok(document);
        }

        [HttpPost("quoteAttachment/{id}/{isQuoteInternal}")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> PostQuoteAttachment([FromForm] IFormFile file, Guid id, bool isQuoteInternal = false)
        {
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);
            var document = new DocumentModel();

            if (file is null || file.Length == 0)
            {
                return BadRequest();
            }

            try
            {
                document = await service.PostAsync(file.FileName, file.ContentType, $"{user.Firstname} {user.Lastname}", string.Empty, id, null, null, true, false, false, null, isQuoteInternal);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot create document - {ex}");
                return BadRequest();
            }

            try
            {
                var fileData = this.FileToByteArray(file);
                await blobStorage.UploadAsync(document.DocumentId, fileData, file.ContentType);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot upload document to Azure Storage - {ex}");
            }

            return Ok(document);
        }

        [HttpPost("orderAttachment/{id}")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> PostOrderAttachment([FromForm] IFormFile file, Guid id)
        {
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);
            var document = new DocumentModel();

            if (file is null || file.Length == 0)
            {
                return BadRequest();
            }

            try
            {
                document = await service.PostAsync(file.FileName, file.ContentType, $"{user.Firstname} {user.Lastname}", string.Empty, id, null, null, false, true, false, null);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot create document - {ex}");
                return BadRequest();
            }

            try
            {
                var fileData = this.FileToByteArray(file);
                await blobStorage.UploadAsync(document.DocumentId, fileData, file.ContentType);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot upload document to Azure Storage - {ex}");
            }

            return Ok(document);
        }

        [HttpPost("purchaseOrderDocument/{id}")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> PostPurchaseOrderAttachment([FromForm] IFormFile file, Guid id)
        {
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);
            var document = new DocumentModel();

            if (file is null || file.Length == 0)
            {
                return BadRequest();
            }

            try
            {
                document = await service.PostAsync(file.FileName, file.ContentType, $"{user.Firstname} {user.Lastname}", "Purchase Order", null, null, id, false, false, false, null);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot create document - {ex}");
                return BadRequest();
            }

            try
            {
                var fileData = this.FileToByteArray(file);
                await blobStorage.UploadAsync(document.DocumentId, fileData, file.ContentType);
            } catch (Exception ex)
            {
                logger.LogError($"Cannot upload document to Azure Storage - {ex}");
            }

            return Ok(document);
        }

        [AllowAnonymous]
        [HttpGet("download/{id}")]
        [ProducesResponseType(200, Type = typeof(IFormFile))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetDocument(Guid id)
        {
            if (!await blobStorage.ExistsAsync(id))
            {
                return NotFound();
            }

            var download = await blobStorage.DownloadAsync(id);
            DocumentModel model = await service.GetByIdAsync(id);
            HttpContext.Response.ContentType = download.Item2;
            HttpContext.Response.Headers.Add(HeaderNames.ContentDisposition, "attachment");

            var fileName = $"{model.FileName}";

            return File(download.Item1, download.Item2, fileName);
        }

        [AllowAnonymous]
        [HttpDelete("{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Delete(Guid id)
        {

            bool docExists = await blobStorage.ExistsAsync(id);

            if (docExists is true)
            {
                try
                {
                    await blobStorage.DeleteAsync(id);
                    await service.DeleteAsync(id);
                    return Ok();
                } catch (Exception ex)
                {
                    return BadRequest(ex);
                }
            } else
            {
                return NotFound();
            }
        }

        [AllowAnonymous]
        [HttpPost("emailUser/{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> EmailUser(Guid id)
        {

            var supplier = await unitOfWork.Supplier.GetAsync(id);

            var supplierName = supplier.Name;

            var url = settings.Host;

            var link = Flurl.Url.Combine(url, $"supplier/{id}/supplier-details");

            await emailService.SendSupplierAttachmentApproval("<EMAIL>", "Karen Duncan", supplierName, link);


            return Ok();
        }
        [AllowAnonymous]
        [HttpPut("approveDocument/{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> ApproveDocument(Guid id, DocumentModel model)
        {
            var document = await unitOfWork.Document.GetAsync(id);
            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);
            var currentUser = $"{user.Firstname} {user.Lastname}";

            document.ApprovedDate = DateTime.UtcNow;
            document.ApprovedBy = currentUser;

            unitOfWork.Document.Update(document);

            var supplierActionLog = new SupplierActionLog
            {
                SupplierActionLogId = Guid.NewGuid(),
                SupplierId = model.CompanyId.Value,
                CreatedBy = currentUser,
                CreatedOn = DateTime.UtcNow,
                Description = $"Document '{document.Name}' has been approved."
            };

            await unitOfWork.SupplierActionLog.CreateAsync(supplierActionLog);

            await unitOfWork.SaveChangesAsync();

            return Ok();
        }

        [AllowAnonymous]
        [HttpPut("reviewDocument/{id}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> ReviewDocument(Guid id, DocumentModel model)
        {
            var previousApprovedDate = "";

            var document = await unitOfWork.Document.GetAsync(id);

            previousApprovedDate = document.ApprovedDate.Value.ToString("dd/MM/yyyy");

            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);

            var currentUser = $"{user.Firstname} {user.Lastname}";

            document.ApprovedDate = DateTime.UtcNow;

            document.ApprovedBy = currentUser;

            unitOfWork.Document.Update(document);

            var supplierActionLog = new SupplierActionLog
            {
                SupplierActionLogId = Guid.NewGuid(),
                SupplierId = model.CompanyId.Value,
                CreatedBy = currentUser,
                CreatedOn = DateTime.UtcNow,
                Description = $"Document '{document.Name}' has been reviewed. It was previously Approved/Reviewed on '{previousApprovedDate}'"
            };

            await unitOfWork.SupplierActionLog.CreateAsync(supplierActionLog);

            await unitOfWork.SaveChangesAsync();

            return Ok();
        }

        [HttpPut("updateDocumentName/{id}/{fileName}")]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> UpdateDocumentName(Guid id, string fileName)
        {
            var document = await unitOfWork.Document.GetAsync(id);
            var documentExtentions = document.Filename.Split('.');

            document.Name = fileName;
            document.Filename = fileName + "." + documentExtentions[1];

            unitOfWork.Document.Update(document);

            await unitOfWork.SaveChangesAsync();

            return Ok();
        }
    }
}