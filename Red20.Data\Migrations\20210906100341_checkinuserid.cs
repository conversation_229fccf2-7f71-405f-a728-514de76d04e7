﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Red20.Data.Migrations
{
    public partial class checkinuserid : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "CheckInUserId",
                table: "UserCheckInCheckOuts",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CheckInUserId",
                table: "UserCheckInCheckOuts");
        }
    }
}
