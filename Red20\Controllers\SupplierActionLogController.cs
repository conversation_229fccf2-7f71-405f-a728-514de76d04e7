﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Red20.Model.Data;
using Red20.Model.Data.Supplier;
using Red20.Service.Data;
using Red20.Service.Data.Interface;

namespace Red20.Controllers {
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class SupplierActionLogController : ControllerBase {

        private ISupplierActionLogService service;
        private IUserService userService;
        private ILogger<AuthController> logger;
        IUnitOfWork unitOfWork;

        public SupplierActionLogController(
            ISupplierActionLogService service,
            IUnitOfWork unitOfWork,
            ILogger<AuthController> logger,
            IUserService userService)
        {

            this.service = service;
            this.unitOfWork = unitOfWork;
            this.logger = logger;
            this.userService = userService;
        }

        [HttpGet("bySupplier/{id}")]
        [ProducesResponseType(200, Type = typeof(IList<SupplierActionLogModel>))]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> GetBySupplier(Guid id) {
            var supplierActionLogModels = await service.GetBySupplierAsync(id);
            return Ok(supplierActionLogModels.ToList());
        }

        [HttpPost]
        [ProducesResponseType(200, Type = typeof(SupplierActionLogUpdateModel))]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Post([FromBody]SupplierActionLogUpdateModel model) {
            if (model is null) {
                return BadRequest();
            }

            var emailClaim = User.Claims.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" ||
                                                     c.Type == ClaimTypes.Email);
            if (emailClaim == null)
            {
                return Unauthorized();
            }

            var user = await userService.GetUserByEmailAsync(emailClaim.Value);

            var currentUser = $"{user.Firstname} {user.Lastname}";

            model.CreatedBy = currentUser;

            var supplierActionLogModel = await service.PostAsync(model);

            return Ok(supplierActionLogModel);
        }
    }
}